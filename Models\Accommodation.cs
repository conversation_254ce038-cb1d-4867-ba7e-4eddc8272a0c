﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class Accommodation
    {
        public int Id { get; set; }

        [Required, StringLength(100)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string Address { get; set; }

        public string? ImageUrl { get; set; }

        [Range(1, 5)]
        public int StarRating { get; set; }

        public bool IsFeatured { get; set; } = false;

        public int LocationId { get; set; }

        public int CityId { get; set; }

        [ForeignKey("LocationId")]
        public Location? Location { get; set; }

        [ForeignKey("CityId")]
        public City? City { get; set; }

        public List<Room>? Rooms { get; set; }

        public List<AccommodationImage>? Images { get; set; }

        public List<Review>? Reviews { get; set; }

        public List<AccommodationAmenity>? AccommodationAmenities { get; set; }

        public List<AccommodationPromotion>? AccommodationPromotions { get; set; }

        [NotMapped]
        public decimal MinPrice { get; set; }

        [NotMapped]
        public double AverageRating => Reviews?.Any() == true ? Reviews.Average(r => r.Rating) : 0;
    }
}
