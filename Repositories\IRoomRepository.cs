﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IRoomRepository
    {
        Task<IEnumerable<Room>> GetAllAsync();
        Task<Room> GetByIdAsync(int id);
        Task<IEnumerable<Room>> GetByAccommodationIdAsync(int accommodationId);
        Task<IEnumerable<Room>> GetAvailableRoomsAsync(int accommodationId, DateTime checkIn, DateTime checkOut);
        Task AddAsync(Room room);
        Task UpdateAsync(Room room);
        Task DeleteAsync(int id);
    }
}
