@model ViVu.Models.Room
@{
    ViewData["Title"] = "Chỉnh sửa phòng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chỉnh sửa phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Phòng</a></li>
        <li class="breadcrumb-item active">Chỉnh sửa</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Chỉnh sửa thông tin phòng
        </div>
        <div class="card-body">
            <form asp-action="Edit" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Name" class="control-label">Tên phòng</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="AccommodationId" class="control-label">Chỗ ở</label>
                            <select asp-for="AccommodationId" class="form-select" asp-items="ViewBag.Accommodations" required>
                                <option value="">-- Chọn chỗ ở --</option>
                            </select>
                            <span asp-validation-for="AccommodationId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="PricePerNight" class="control-label">Giá/đêm (VNĐ)</label>
                            <input asp-for="PricePerNight" class="form-control" type="number" min="0.01" step="0.01" required />
                            <span asp-validation-for="PricePerNight" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="MaxOccupancy" class="control-label">Sức chứa (người)</label>
                            <input asp-for="MaxOccupancy" class="form-control" type="number" min="1" required />
                            <span asp-validation-for="MaxOccupancy" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Hình ảnh</label>
                            <input type="file" name="imageFile" class="form-control" accept="image/*" />
                            <small class="text-muted">Để trống nếu không muốn thay đổi hình ảnh</small>
                            @if (!string.IsNullOrEmpty(Model.ImageUrl))
                            {
                                <div class="mt-2">
                                    <img src="@Model.ImageUrl" alt="@Model.Name" class="image-preview" style="max-width: 200px; max-height: 150px;" />
                                </div>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check mt-4">
                                <input asp-for="IsAvailable" class="form-check-input" />
                                <label asp-for="IsAvailable" class="form-check-label">Có sẵn</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="5" required></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label class="control-label">Tiện nghi</label>
                    <div class="row">
                        @foreach (var item in ViewBag.Amenities)
                        {
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input type="checkbox" name="selectedAmenities" value="@item.Amenity.Id"
                                           class="form-check-input" id="<EMAIL>"
                                           @(item.IsSelected ? "checked" : "") />
                                    <label class="form-check-label" for="<EMAIL>">@item.Amenity.Name</label>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/disable-price-validation.js"></script>

    <script>
        // Hiển thị xem trước hình ảnh khi chọn file
        document.querySelector('input[name="imageFile"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    // Tìm hoặc tạo phần tử xem trước hình ảnh
                    let preview = document.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview mt-2';
                        preview.style.maxWidth = '200px';
                        preview.style.maxHeight = '150px';
                        e.target.parentNode.appendChild(preview);
                    }
                    preview.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
}
