@model ViVu.Models.ServiceBooking
@{
    ViewData["Title"] = "Xác nhận đặt dịch vụ";
}

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="bi bi-check-circle-fill me-2"></i>Đặt dịch vụ thành công!</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        <h5 class="mt-3">Cảm ơn bạn đã đặt dịch vụ!</h5>
                        <p class="text-muted">Mã đặt dịch vụ của bạn là <strong>#@Model.Id</strong></p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Thông tin đặt dịch vụ</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Mã đặt dịch vụ:</th>
                                    <td>#@Model.Id</td>
                                </tr>
                                <tr>
                                    <th>Ngày đặt:</th>
                                    <td>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <th>Ngày sử dụng:</th>
                                    <td>@Model.ServiceDate.ToString("dd/MM/yyyy")</td>
                                </tr>
                                <tr>
                                    <th>Trạng thái:</th>
                                    <td>
                                        @switch (Model.Status)
                                        {
                                            case ServiceBookingStatus.Pending:
                                                <span class="badge bg-warning">Chờ xác nhận</span>
                                                break;
                                            case ServiceBookingStatus.Confirmed:
                                                <span class="badge bg-primary">Đã xác nhận</span>
                                                break;
                                            case ServiceBookingStatus.Completed:
                                                <span class="badge bg-success">Hoàn thành</span>
                                                break;
                                            case ServiceBookingStatus.Cancelled:
                                                <span class="badge bg-danger">Đã hủy</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Thông tin khách hàng</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Họ tên:</th>
                                    <td>@Model.ApplicationUser.FullName</td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>@Model.ApplicationUser.Email</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.ApplicationUser.Address))
                                {
                                    <tr>
                                        <th>Địa chỉ:</th>
                                        <td>@Model.ApplicationUser.Address</td>
                                    </tr>
                                }
                            </table>
                        </div>
                    </div>
                    
                    <h5>Chi tiết dịch vụ đã đặt</h5>
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Dịch vụ</th>
                                    <th>Địa điểm</th>
                                    <th>Số lượng</th>
                                    <th>Đơn giá</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var detail in Model.ServiceBookingDetails)
                                {
                                    <tr>
                                        <td>@detail.Service.Name</td>
                                        <td>@detail.Service.Location?.Name, @detail.Service.City?.Name</td>
                                        <td>@detail.Quantity</td>
                                        <td>@detail.Price.ToString("N0") VNĐ</td>
                                        <td>@detail.TotalPrice.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Tổng cộng:</th>
                                    <th>@Model.TotalPrice.ToString("N0") VNĐ</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="mb-4">
                            <h5>Yêu cầu đặc biệt</h5>
                            <div class="p-3 bg-light rounded">
                                @Model.SpecialRequests
                            </div>
                        </div>
                    }
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        <span>Chúng tôi sẽ liên hệ với bạn để xác nhận đơn đặt dịch vụ trong thời gian sớm nhất. Vui lòng kiểm tra email hoặc điện thoại của bạn.</span>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a asp-action="MyBookings" class="btn btn-primary me-2">
                            <i class="bi bi-list-ul"></i> Xem đơn đặt dịch vụ của tôi
                        </a>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Quay lại danh sách dịch vụ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
