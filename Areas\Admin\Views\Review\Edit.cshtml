@model ViVu.Models.Review
@{
    ViewData["Title"] = "Chỉnh sửa đánh giá";
    Layout = "_AdminLayout";
    
    string itemName = "";
    string itemType = "";
    
    if (Model.AccommodationId.HasValue && Model.Accommodation != null)
    {
        itemName = Model.Accommodation.Name;
        itemType = "Chỗ ở";
    }
    else if (Model.TourId.HasValue && Model.Tour != null)
    {
        itemName = Model.Tour.Name;
        itemType = "Tour";
    }
    else if (Model.ServiceId.HasValue && Model.Service != null)
    {
        itemName = Model.Service.Name;
        itemType = "Dịch vụ";
    }
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý đánh giá</a></li>
        <li class="breadcrumb-item active">Chỉnh sửa</li>
    </ol>

    <div class="row">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-edit me-1"></i>
                    Chỉnh sửa đánh giá cho @itemType: @itemName
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />

                        <div class="mb-3">
                            <label class="form-label">Người dùng</label>
                            <input type="text" class="form-control" value="@Model.User?.FullName" readonly />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Đối tượng</label>
                            <input type="text" class="form-control" value="@itemType: @itemName" readonly />
                        </div>

                        <div class="mb-3">
                            <label asp-for="Rating" class="form-label">Đánh giá</label>
                            <select asp-for="Rating" class="form-select">
                                <option value="1">1 sao</option>
                                <option value="2">2 sao</option>
                                <option value="3">3 sao</option>
                                <option value="4">4 sao</option>
                                <option value="5">5 sao</option>
                            </select>
                            <span asp-validation-for="Rating" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Comment" class="form-label">Nhận xét</label>
                            <textarea asp-for="Comment" class="form-control" rows="5"></textarea>
                            <span asp-validation-for="Comment" class="text-danger"></span>
                        </div>

                        <div class="d-flex">
                            <a asp-action="Index" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu thay đổi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
