@model ViVu.Areas.Admin.Controllers.EditUserViewModel
@{
    ViewData["Title"] = "Chỉnh sửa người dùng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chỉnh sửa người dùng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Người dùng</a></li>
        <li class="breadcrumb-item active">Chỉnh sửa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-edit me-1"></i>
            Chỉnh sửa thông tin người dùng
        </div>
        <div class="card-body">
            <form asp-action="Edit">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Email" class="control-label">Email</label>
                            <input asp-for="Email" class="form-control" required />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Password" class="control-label">Mật khẩu mới</label>
                            <input asp-for="Password" class="form-control" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                            <small class="text-muted">Để trống nếu không muốn thay đổi mật khẩu.</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="FullName" class="control-label">Họ tên</label>
                            <input asp-for="FullName" class="form-control" required />
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="PhoneNumber" class="control-label">Điện thoại</label>
                            <input asp-for="PhoneNumber" class="form-control" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label asp-for="Address" class="control-label">Địa chỉ</label>
                    <input asp-for="Address" class="form-control" />
                    <span asp-validation-for="Address" class="text-danger"></span>
                </div>
                
                <div class="form-group mb-3">
                    <label class="control-label">Vai trò</label>
                    <div class="row">
                        @foreach (var role in ViewBag.Roles)
                        {
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input type="checkbox" name="SelectedRoles" value="@role.Name" class="form-check-input" id="<EMAIL>" 
                                           @(Model.SelectedRoles.Contains(role.Name) ? "checked" : "") />
                                    <label class="form-check-label" for="<EMAIL>">@role.Name</label>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
