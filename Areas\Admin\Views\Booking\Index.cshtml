﻿@model IEnumerable<ViVu.Models.Booking>
@{
    ViewData["Title"] = "Quản lý đặt phòng";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý đặt phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Đặt phòng</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-calendar-check me-1"></i> Danh sách đặt phòng</div>
                <div>
                    <form id="filterForm" asp-action="Index" method="get" class="d-flex">
                        <select name="status" class="form-select form-select-sm me-2" onchange="document.getElementById('filterForm').submit()">
                            <option value="">Tất cả trạng thái</option>
                            @foreach (var status in ViewBag.Statuses)
                            {
                                <option value="@status.Value" selected="@(status.Value == ViewBag.SelectedStatus)">@status.Text</option>
                            }
                        </select>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Mã đơn</th>
                        <th>Khách hàng</th>
                        <th>Ngày đặt</th>
                        <th>Ngày nhận phòng</th>
                        <th>Ngày trả phòng</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Id</td>
                            <td>@item.ApplicationUser?.FullName</td>
                            <td>@item.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>@item.CheckInDate.ToString("dd/MM/yyyy")</td>
                            <td>@item.CheckOutDate.ToString("dd/MM/yyyy")</td>
                            <td>@item.TotalPrice.ToString("N0") VNĐ</td>
                            <td>
                                @if (item.Status == BookingStatus.Pending)
                                {
                                    <span class="badge bg-warning">Chờ xác nhận</span>
                                }
                                else if (item.Status == BookingStatus.Confirmed)
                                {
                                    <span class="badge bg-primary">Đã xác nhận</span>
                                }
                                else if (item.Status == BookingStatus.Completed)
                                {
                                    <span class="badge bg-success">Hoàn thành</span>
                                }
                                else if (item.Status == BookingStatus.Cancelled)
                                {
                                    <span class="badge bg-danger">Đã hủy</span>
                                }
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if (item.Status == BookingStatus.Cancelled || item.Status == BookingStatus.Completed)
                                    {
                                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
