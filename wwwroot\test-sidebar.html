<!DOCTYPE html>
<html>
<head>
    <title>Test Sidebar Expandable</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/sidebar-nav.css">
    <link rel="stylesheet" href="/css/sidebar-expandable.css">
    <style>
        body { margin: 0; padding: 0; }
        .test-content { 
            margin-left: 80px; 
            padding: 20px; 
            background: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar-nav">
        <div class="sidebar-section sidebar-actions">
            <!-- AI Expandable -->
            <div class="sidebar-expandable">
                <button class="sidebar-nav-item expandable-toggle" type="button" data-target="ai-menu">
                    <i class="bi bi-magic"></i>
                    <span class="tooltip">AI Gợi ý</span>
                    <i class="bi bi-chevron-right expand-icon"></i>
                </button>
            </div>

            <!-- Cart Expandable -->
            <div class="sidebar-expandable">
                <button class="sidebar-nav-item expandable-toggle" type="button" data-target="cart-menu">
                    <i class="bi bi-cart"></i>
                    <span class="tooltip">Giỏ hàng</span>
                    <i class="bi bi-chevron-right expand-icon"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Expanded Sidebar Content -->
    <div class="sidebar-expanded" id="sidebar-expanded">
        <div class="sidebar-expanded-content">
            <!-- AI Menu -->
            <div class="expanded-menu" id="ai-menu">
                <div class="expanded-menu-header">
                    <h5><i class="bi bi-magic me-2"></i>AI Gợi ý</h5>
                    <button class="btn-close-expanded" data-target="ai-menu">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="expanded-menu-body">
                    <a href="#" class="expanded-menu-item">
                        <i class="bi bi-stars me-2"></i>
                        <div>
                            <div class="item-title">Gợi Ý Lịch Trình</div>
                            <div class="item-desc">Tạo lịch trình nhanh chóng</div>
                        </div>
                    </a>
                    <a href="#" class="expanded-menu-item">
                        <i class="bi bi-list-task me-2"></i>
                        <div>
                            <div class="item-title">Gợi Ý Lịch Trình (Đầy đủ)</div>
                            <div class="item-desc">Tùy chọn chi tiết và đầy đủ</div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Cart Menu -->
            <div class="expanded-menu" id="cart-menu">
                <div class="expanded-menu-header">
                    <h5><i class="bi bi-cart me-2"></i>Giỏ hàng</h5>
                    <button class="btn-close-expanded" data-target="cart-menu">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="expanded-menu-body">
                    <a href="#" class="expanded-menu-item">
                        <i class="bi bi-building me-2"></i>
                        <div>
                            <div class="item-title">Giỏ đặt phòng</div>
                            <div class="item-desc">Quản lý đặt phòng khách sạn</div>
                        </div>
                    </a>
                    <a href="#" class="expanded-menu-item">
                        <i class="bi bi-airplane me-2"></i>
                        <div>
                            <div class="item-title">Giỏ đặt tour</div>
                            <div class="item-desc">Quản lý đặt tour du lịch</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Content -->
    <div class="test-content">
        <h1>Test Sidebar Expandable</h1>
        <p>Click on the sidebar buttons to test expandable functionality.</p>
        <button onclick="testSimple()" class="btn btn-primary">Test Manual Click</button>
        <button onclick="console.log('Elements:', {
            buttons: document.querySelectorAll('.expandable-toggle').length,
            sidebar: !!document.getElementById('sidebar-expanded'),
            menus: document.querySelectorAll('.expanded-menu').length
        })" class="btn btn-info">Check Elements</button>
    </div>

    <script src="/js/sidebar-simple-test.js"></script>
</body>
</html>
