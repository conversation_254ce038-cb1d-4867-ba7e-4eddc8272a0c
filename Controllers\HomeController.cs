using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Data;
using ViVu.Models;

namespace ViVu.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(
        ApplicationDbContext context,
        ILogger<HomeController> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        // Hiển thị các khách sạn nổi bật
        var featuredAccommodations = await _context.Accommodations
            .Include(a => a.Location)
            .Include(a => a.City)
            .Include(a => a.Rooms)
            .Where(a => a.IsFeatured)
            .ToListAsync();

        // Tính giá thấp nhất cho mỗi khách sạn
        foreach (var accommodation in featuredAccommodations)
        {
            accommodation.MinPrice = accommodation.Rooms.Any()
                ? accommodation.Rooms.Min(r => r.<PERSON>)
                : 0;
        }

        // Hiển thị các tour nổi bật
        var featuredTours = await _context.Tours
            .Include(t => t.Location)
            .Include(t => t.City)
            .Where(t => t.IsFeatured)
            .ToListAsync();

        // Hiển thị các địa điểm nổi bật
        var featuredLocations = await _context.Locations
            .Include(l => l.City)
            .ThenInclude(c => c.Country)
            .Where(l => l.IsFeatured)
            .ToListAsync();

        // Hiển thị các thành phố nổi bật
        var featuredCities = await _context.Cities
            .Include(c => c.Country)
            .Where(c => c.IsFeatured)
            .ToListAsync();

        // Truyền dữ liệu vào ViewBag
        ViewBag.FeaturedAccommodations = featuredAccommodations;
        ViewBag.FeaturedTours = featuredTours;
        ViewBag.FeaturedLocations = featuredLocations;
        ViewBag.FeaturedCities = featuredCities;

        // Truyền danh sách địa điểm và thành phố cho form tìm kiếm
        ViewBag.Locations = new SelectList(await _context.Locations.ToListAsync(), "Id", "Name");
        ViewBag.Cities = new SelectList(await _context.Cities.ToListAsync(), "Id", "Name");

        // Lấy danh sách sản phẩm
        var products = await _context.Products
            .Include(p => p.Category)
            .ToListAsync();

        return View(products);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    public IActionResult About()
    {
        return View();
    }

    public IActionResult Contact()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
