using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Net;
using HtmlAgilityPack;
using System.IO;
using System.Collections.Concurrent;

namespace ViVu.Services
{
    public class OllamaService
    {
        private readonly HttpClient _httpClient;
        private readonly HttpClient _webClient;
        private readonly string _apiUrl = "http://localhost:11434/api/generate";
        private readonly string _model = "mistral"; // hoặc "llama2", "phi", "gemma"

        // Cache for web data to avoid repeated requests
        private static readonly ConcurrentDictionary<string, (string Content, DateTime Timestamp)> _webDataCache =
            new();

        // Cache expiration time (in minutes)
        private readonly int _cacheExpirationMinutes = 60;

        // Các nguồn dữ liệu du lịch Bến Tre
        private readonly List<string> _tourismDataSources = new()
        {
            "https://www.tripadvisor.com/Tourism-g303945-<PERSON>_Tre_Ben_Tre_Province_Mekong_Delta-Vacations.html",
            "https://vietnam.travel/places-to-go/mekong-delta/ben-tre",
            "https://www.vietnamonline.com/destination/ben-tre.html",
            "https://www.lonelyplanet.com/vietnam/mekong-delta/ben-tre",
            "https://www.travelfish.org/location/vietnam/mekong_delta/ben_tre/ben_tre",
            "https://www.vietnamtourism.com/en/index.php/tourism/items/2559",
            "https://www.mekongdeltaexplorer.vn/ben-tre-travel-guide.html"
        };

        public OllamaService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(60); // Tăng timeout vì mô hình cục bộ có thể mất nhiều thời gian hơn

            // Separate HttpClient for web requests
            _webClient = new HttpClient();
            _webClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _webClient.Timeout = TimeSpan.FromSeconds(30);

            Console.WriteLine($"OllamaService initialized with model: {_model}");
        }

        /// <summary>
        /// Lấy dữ liệu từ một URL web
        /// </summary>
        /// <param name="url">URL cần lấy dữ liệu</param>
        /// <returns>Nội dung HTML của trang web</returns>
        private async Task<string> FetchWebDataAsync(string url)
        {
            try
            {
                // Kiểm tra cache trước
                if (_webDataCache.TryGetValue(url, out var cachedData))
                {
                    // Nếu dữ liệu chưa hết hạn, trả về từ cache
                    if ((DateTime.Now - cachedData.Timestamp).TotalMinutes < _cacheExpirationMinutes)
                    {
                        Console.WriteLine($"Returning cached data for URL: {url}");
                        return cachedData.Content;
                    }
                }

                Console.WriteLine($"Fetching data from URL: {url}");
                var response = await _webClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();

                    // Lưu vào cache
                    _webDataCache[url] = (content, DateTime.Now);

                    return content;
                }
                else
                {
                    Console.WriteLine($"Error fetching URL {url}: {response.StatusCode}");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception when fetching URL {url}: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Trích xuất thông tin du lịch từ HTML
        /// </summary>
        /// <param name="html">Nội dung HTML</param>
        /// <returns>Thông tin du lịch đã được trích xuất</returns>
        private string ExtractTourismInfo(string html)
        {
            try
            {
                if (string.IsNullOrEmpty(html))
                    return string.Empty;

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var sb = new StringBuilder();

                // Trích xuất tiêu đề
                var titleNode = doc.DocumentNode.SelectSingleNode("//h1");
                if (titleNode != null)
                {
                    sb.AppendLine($"Tiêu đề: {titleNode.InnerText.Trim()}");
                    sb.AppendLine();
                }

                // Trích xuất các đoạn văn bản chính
                var paragraphs = doc.DocumentNode.SelectNodes("//p[string-length(.) > 100]");
                if (paragraphs != null)
                {
                    foreach (var p in paragraphs.Take(5)) // Lấy 5 đoạn đầu tiên
                    {
                        var text = WebUtility.HtmlDecode(p.InnerText).Trim();
                        text = Regex.Replace(text, @"\s+", " "); // Loại bỏ khoảng trắng thừa

                        if (!string.IsNullOrWhiteSpace(text) && text.Length > 100)
                        {
                            sb.AppendLine(text);
                            sb.AppendLine();
                        }
                    }
                }

                // Trích xuất danh sách (nếu có)
                var lists = doc.DocumentNode.SelectNodes("//ul/li | //ol/li");
                if (lists != null)
                {
                    sb.AppendLine("Các điểm nổi bật:");
                    foreach (var item in lists.Take(10)) // Lấy 10 mục đầu tiên
                    {
                        var text = WebUtility.HtmlDecode(item.InnerText).Trim();
                        text = Regex.Replace(text, @"\s+", " ");

                        if (!string.IsNullOrWhiteSpace(text) && text.Length > 20)
                        {
                            sb.AppendLine($"- {text}");
                        }
                    }
                    sb.AppendLine();
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting tourism info: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Lấy thông tin du lịch thời gian thực từ các nguồn web
        /// </summary>
        /// <returns>Thông tin du lịch tổng hợp</returns>
        public async Task<string> GetRealtimeTourismDataAsync()
        {
            try
            {
                Console.WriteLine("Fetching realtime tourism data from web sources...");
                var sb = new StringBuilder();
                sb.AppendLine("THÔNG TIN DU LỊCH BẾN TRE THỜI GIAN THỰC:");
                sb.AppendLine("===========================================");
                sb.AppendLine();

                foreach (var url in _tourismDataSources)
                {
                    var html = await FetchWebDataAsync(url);
                    if (!string.IsNullOrEmpty(html))
                    {
                        var info = ExtractTourismInfo(html);
                        if (!string.IsNullOrEmpty(info))
                        {
                            sb.AppendLine($"Nguồn: {url}");
                            sb.AppendLine("-------------------------------------------");
                            sb.AppendLine(info);
                            sb.AppendLine();
                        }
                    }
                }

                var result = sb.ToString();
                Console.WriteLine($"Fetched tourism data, length: {result.Length} characters");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting realtime tourism data: {ex.Message}");
                return "Không thể lấy thông tin du lịch thời gian thực.";
            }
        }

        /// <summary>
        /// Lấy thông tin thời tiết hiện tại cho Bến Tre
        /// </summary>
        /// <returns>Thông tin thời tiết</returns>
        public async Task<string> GetCurrentWeatherAsync()
        {
            try
            {
                // URL thông tin thời tiết Bến Tre
                var url = "https://www.timeanddate.com/weather/vietnam/ben-tre";

                var html = await FetchWebDataAsync(url);
                if (string.IsNullOrEmpty(html))
                    return "Không thể lấy thông tin thời tiết.";

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var sb = new StringBuilder();
                sb.AppendLine("THÔNG TIN THỜI TIẾT BẾN TRE:");
                sb.AppendLine("============================");

                // Trích xuất nhiệt độ hiện tại
                var tempNode = doc.DocumentNode.SelectSingleNode("//div[contains(@class, 'h2')]");
                if (tempNode != null)
                {
                    sb.AppendLine($"Nhiệt độ hiện tại: {tempNode.InnerText.Trim()}");
                }

                // Trích xuất mô tả thời tiết
                var descNode = doc.DocumentNode.SelectSingleNode("//p[contains(@class, 'summary')]");
                if (descNode != null)
                {
                    sb.AppendLine($"Tình trạng: {descNode.InnerText.Trim()}");
                }

                // Trích xuất thông tin chi tiết
                var detailsTable = doc.DocumentNode.SelectNodes("//table[contains(@class, 'table')]/tbody/tr");
                if (detailsTable != null)
                {
                    foreach (var row in detailsTable)
                    {
                        var cells = row.SelectNodes("td");
                        if (cells != null && cells.Count >= 2)
                        {
                            var label = cells[0].InnerText.Trim();
                            var value = cells[1].InnerText.Trim();
                            sb.AppendLine($"{label}: {value}");
                        }
                    }
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting weather data: {ex.Message}");
                return "Không thể lấy thông tin thời tiết.";
            }
        }

        /// <summary>
        /// Lấy thông tin sự kiện hiện tại ở Bến Tre
        /// </summary>
        /// <returns>Thông tin sự kiện</returns>
        public async Task<string> GetCurrentEventsAsync()
        {
            try
            {
                // URL thông tin sự kiện Bến Tre (giả định)
                var url = "https://www.vietnam-guide.com/ben-tre/events.htm";

                var html = await FetchWebDataAsync(url);
                if (string.IsNullOrEmpty(html))
                    return "Không thể lấy thông tin sự kiện.";

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var sb = new StringBuilder();
                sb.AppendLine("SỰ KIỆN HIỆN TẠI TẠI BẾN TRE:");
                sb.AppendLine("============================");

                // Trích xuất danh sách sự kiện
                var eventNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'event-item')]");
                if (eventNodes != null && eventNodes.Count > 0)
                {
                    foreach (var eventNode in eventNodes)
                    {
                        var titleNode = eventNode.SelectSingleNode(".//h3");
                        var dateNode = eventNode.SelectSingleNode(".//span[contains(@class, 'date')]");
                        var descNode = eventNode.SelectSingleNode(".//p");

                        if (titleNode != null)
                        {
                            sb.AppendLine($"Tên sự kiện: {titleNode.InnerText.Trim()}");

                            if (dateNode != null)
                                sb.AppendLine($"Thời gian: {dateNode.InnerText.Trim()}");

                            if (descNode != null)
                                sb.AppendLine($"Mô tả: {descNode.InnerText.Trim()}");

                            sb.AppendLine();
                        }
                    }
                }
                else
                {
                    sb.AppendLine("Không tìm thấy sự kiện nào sắp diễn ra.");
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting event data: {ex.Message}");
                return "Không thể lấy thông tin sự kiện.";
            }
        }

        public async Task<string> GeneratePersonalizedRecommendation(string userPreferences, string travelHistory, string destination)
        {
            try
            {
                Console.WriteLine("Generating personalized recommendation with real-time data...");

                // Lấy dữ liệu thời gian thực từ web
                Console.WriteLine("Fetching real-time data for recommendation...");
                var tourismData = await GetRealtimeTourismDataAsync();
                var weatherData = await GetCurrentWeatherAsync();
                var eventsData = await GetCurrentEventsAsync();

                // Tạo phần dữ liệu thời gian thực
                var realtimeData = new StringBuilder();
                realtimeData.AppendLine("DỮ LIỆU THỜI GIAN THỰC:");
                realtimeData.AppendLine("=======================");

                // Thêm thông tin thời tiết
                realtimeData.AppendLine("THÔNG TIN THỜI TIẾT:");
                realtimeData.AppendLine(weatherData);
                realtimeData.AppendLine();

                // Thêm thông tin sự kiện
                realtimeData.AppendLine("SỰ KIỆN HIỆN TẠI:");
                realtimeData.AppendLine(eventsData);
                realtimeData.AppendLine();

                // Thêm thông tin du lịch (rút gọn để không làm prompt quá dài)
                realtimeData.AppendLine("THÔNG TIN DU LỊCH:");
                // Lấy 1000 ký tự đầu tiên từ dữ liệu du lịch để tránh prompt quá dài
                var truncatedTourismData = tourismData.Length > 1000
                    ? tourismData.Substring(0, 1000) + "..."
                    : tourismData;
                realtimeData.AppendLine(truncatedTourismData);

                // Cải thiện prompt để đảm bảo tuân thủ chặt chẽ sở thích người dùng và sử dụng dữ liệu thời gian thực
                string prompt = $"Bạn là trợ lý du lịch chuyên về du lịch Việt Nam, đặc biệt là tỉnh Bến Tre. " +
                    $"Hãy tạo gợi ý du lịch cá nhân hóa dựa trên thông tin sau:\n\n" +
                    $"SỞ THÍCH CỦA NGƯỜI DÙNG:\n{userPreferences}\n\n" +
                    $"LỊCH SỬ DU LỊCH:\n{travelHistory}\n\n" +
                    $"ĐIỂM ĐẾN: {destination}\n\n" +
                    $"THÔNG TIN THỜI GIAN THỰC (CẬP NHẬT MỚI NHẤT):\n{realtimeData}\n\n" +
                    $"HƯỚNG DẪN QUAN TRỌNG:\n" +
                    $"1. Bạn PHẢI TUÂN THỦ CHẶT CHẼ các sở thích đã chọn của người dùng. KHÔNG đề xuất hoạt động hoặc phong cách du lịch trái với sở thích đã nêu.\n" +
                    $"2. Nếu người dùng chọn 'Tour có hướng dẫn', hãy đề xuất các tour có hướng dẫn viên. Nếu họ chọn 'Du lịch tự túc', hãy đề xuất lịch trình tự khám phá. Nếu họ chọn cả hai, hãy đề xuất kết hợp cả hai loại.\n" +
                    $"3. Chỉ đề xuất các hoạt động liên quan đến thiên nhiên nếu người dùng đã chọn 'Thiên nhiên'. Tương tự cho 'Lịch sử', 'Ẩm thực', 'Mạo hiểm', và 'Thư giãn'. KHÔNG đề xuất hoạt động không liên quan đến sở thích đã chọn.\n" +
                    $"4. Nếu người dùng đi cùng trẻ em hoặc người lớn tuổi, hãy đảm bảo các hoạt động phù hợp với họ.\n" +
                    $"5. Đảm bảo ngân sách và thời gian đề xuất nằm CHÍNH XÁC trong phạm vi người dùng đã chỉ định.\n" +
                    $"6. Nếu người dùng chọn cả 'Tour có hướng dẫn' và 'Du lịch tự túc', hãy đảm bảo đề xuất CẢ HAI loại hình du lịch này trong lịch trình.\n" +
                    $"7. SỬ DỤNG thông tin thời gian thực được cung cấp để đưa ra gợi ý phù hợp với thời tiết hiện tại và các sự kiện đang diễn ra.\n" +
                    $"8. Đề cập đến các điểm du lịch cụ thể được nêu trong dữ liệu thời gian thực.\n" +
                    $"9. Đề xuất các hoạt động phù hợp với điều kiện thời tiết hiện tại.\n\n" +
                    $"Hãy tạo một lịch trình chi tiết phù hợp CHÍNH XÁC với sở thích của người dùng. " +
                    $"Lịch trình nên bao gồm các hoạt động theo ngày, đề xuất chỗ ở, và ước tính chi phí. " +
                    $"Đảm bảo tiêu đề của lịch trình phản ánh đúng loại hình du lịch đã chọn (Tour có hướng dẫn, Du lịch tự túc, hoặc Kết hợp). " +
                    $"Đánh dấu rõ những gợi ý dựa trên dữ liệu thời gian thực bằng cách thêm '(Dữ liệu thời gian thực)' vào cuối gợi ý đó.";

                Console.WriteLine($"Prompt length with real-time data: {prompt.Length} characters");

                var requestData = new
                {
                    model = _model,
                    prompt = prompt,
                    stream = false,
                    options = new
                    {
                        temperature = 0.7f,
                        top_p = 0.9f,
                        max_tokens = 2500
                    }
                };

                var jsonRequest = JsonSerializer.Serialize(requestData);
                Console.WriteLine($"Ollama API request: {jsonRequest}");
                Console.WriteLine($"Ollama API URL: {_apiUrl}");

                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                Console.WriteLine($"Sending request to Ollama API with prompt length: {prompt.Length} characters");

                Console.WriteLine("Attempting to send request to Ollama API...");
                var response = await _httpClient.PostAsync(_apiUrl, content);
                Console.WriteLine($"Ollama API response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Ollama API response received. Length: {responseContent.Length} characters");
                    Console.WriteLine($"Ollama API response content: {responseContent}");

                    try
                    {
                        Console.WriteLine("Attempting to deserialize Ollama response...");
                        var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);

                        Console.WriteLine("Checking if response contains 'response' property...");
                        if (responseObject.TryGetProperty("response", out JsonElement responseElement))
                        {
                            var responseText = responseElement.GetString();
                            Console.WriteLine($"Response property found. Value length: {responseText?.Length ?? 0}");

                            // Kiểm tra nội dung phản hồi
                            if (string.IsNullOrWhiteSpace(responseText))
                            {
                                Console.WriteLine("WARNING: Ollama returned empty content");
                                return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                            }

                            // Thêm thông báo về dữ liệu thời gian thực
                            var finalResponse = new StringBuilder();
                            finalResponse.AppendLine("# LỊCH TRÌNH DU LỊCH BẾN TRE (Với dữ liệu thời gian thực)");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("> *Lịch trình này được tạo dựa trên dữ liệu thời gian thực về thời tiết, sự kiện và thông tin du lịch mới nhất tại Bến Tre.*");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine(responseText);

                            // Thêm phần ghi chú về dữ liệu thời gian thực
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("---");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("**Ghi chú:** Lịch trình này được tạo dựa trên dữ liệu thời gian thực được cập nhật vào " +
                                                    DateTime.Now.ToString("HH:mm, dd/MM/yyyy") + ". Thông tin có thể thay đổi, vui lòng kiểm tra lại trước khi khởi hành.");

                            // Ghi log thành công
                            var result = finalResponse.ToString();
                            Console.WriteLine($"Successfully generated AI recommendation with real-time data. Length: {result.Length} characters");
                            Console.WriteLine($"First 100 characters of response: {result.Substring(0, Math.Min(100, result.Length))}...");
                            return result;
                        }
                        else
                        {
                            Console.WriteLine("ERROR: Response does not contain 'response' property");
                            Console.WriteLine($"Available properties: {string.Join(", ", responseObject.EnumerateObject().Select(p => p.Name))}");
                            return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing Ollama response: {ex.Message}");
                        Console.WriteLine($"Exception type: {ex.GetType().Name}");
                        Console.WriteLine($"Response content: {responseContent}");
                        return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Ollama API error (Status: {response.StatusCode}): {errorContent}");
                    Console.WriteLine($"Request URL: {_apiUrl}");
                    Console.WriteLine($"Request model: {_model}");
                    return $"Rất tiếc, chúng tôi không thể tạo gợi ý cá nhân hóa vào lúc này. Lỗi: Không thể kết nối với dịch vụ AI.";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating AI recommendation: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                return "Rất tiếc, chúng tôi không thể tạo gợi ý cá nhân hóa vào lúc này. Vui lòng thử lại sau. " +
                       "Lỗi: Đã xảy ra lỗi khi xử lý yêu cầu.";
            }
        }

        public async Task<string> GenerateTravelInsights(string destination, string interests)
        {
            Console.WriteLine($"=== GenerateTravelInsights called with destination: {destination}, interests: {interests} ===");
            Console.WriteLine($"Thread ID: {Environment.CurrentManagedThreadId}");
            Console.WriteLine($"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

            try
            {
                // Lấy dữ liệu thời gian thực từ web
                Console.WriteLine("Fetching real-time data for travel insights...");
                var tourismData = await GetRealtimeTourismDataAsync();
                var weatherData = await GetCurrentWeatherAsync();

                // Tạo phần dữ liệu thời gian thực
                var realtimeData = new StringBuilder();
                realtimeData.AppendLine("DỮ LIỆU THỜI GIAN THỰC:");
                realtimeData.AppendLine("=======================");

                // Thêm thông tin thời tiết
                realtimeData.AppendLine("THÔNG TIN THỜI TIẾT:");
                realtimeData.AppendLine(weatherData);
                realtimeData.AppendLine();

                // Thêm thông tin du lịch (rút gọn để không làm prompt quá dài)
                realtimeData.AppendLine("THÔNG TIN DU LỊCH:");
                // Lấy 800 ký tự đầu tiên từ dữ liệu du lịch để tránh prompt quá dài
                var truncatedTourismData = tourismData.Length > 800
                    ? string.Concat(tourismData.AsSpan(0, 800), "...")
                    : tourismData;
                realtimeData.AppendLine(truncatedTourismData);

                Console.WriteLine("Building prompt for travel insights with real-time data...");
                string prompt = $"Bạn là hướng dẫn viên du lịch có kiến thức sâu rộng về văn hóa, lịch sử và du lịch Việt Nam. " +
                    $"Hãy cung cấp thông tin chi tiết về {destination} ở Việt Nam, tập trung vào {interests}. " +
                    $"Bao gồm thông tin về thời điểm tốt nhất để tham quan, ý nghĩa văn hóa, bối cảnh lịch sử, " +
                    $"và bất kỳ khía cạnh độc đáo hoặc ít được biết đến nào có thể nâng cao trải nghiệm của du khách.\n\n" +
                    $"THÔNG TIN THỜI GIAN THỰC (CẬP NHẬT MỚI NHẤT):\n{realtimeData}\n\n" +
                    $"HƯỚNG DẪN QUAN TRỌNG:\n" +
                    $"1. Sử dụng thông tin thời gian thực được cung cấp để làm phong phú thêm nội dung của bạn.\n" +
                    $"2. Đề cập đến điều kiện thời tiết hiện tại và cách nó có thể ảnh hưởng đến trải nghiệm du lịch.\n" +
                    $"3. Đề cập đến các điểm du lịch cụ thể được nêu trong dữ liệu thời gian thực.\n" +
                    $"4. Đánh dấu rõ những thông tin dựa trên dữ liệu thời gian thực bằng cách thêm '(Dữ liệu thời gian thực)' vào cuối thông tin đó.";

                Console.WriteLine($"Prompt built, length: {prompt.Length} characters");

                Console.WriteLine("Creating request data object...");
                var requestData = new
                {
                    model = _model,
                    prompt = prompt,
                    stream = false,
                    options = new
                    {
                        temperature = 0.7f,
                        top_p = 0.9f,
                        max_tokens = 1500
                    }
                };
                Console.WriteLine("Request data object created");

                Console.WriteLine("Serializing request to JSON...");
                var jsonRequest = JsonSerializer.Serialize(requestData);
                Console.WriteLine($"Ollama API request for travel insights: {jsonRequest}");
                Console.WriteLine($"Ollama API URL: {_apiUrl}");

                Console.WriteLine("Creating StringContent for HTTP request...");
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                Console.WriteLine($"StringContent created, Content-Type: {content.Headers.ContentType}");
                Console.WriteLine($"Sending request to Ollama API with prompt length: {prompt.Length} characters");

                Console.WriteLine($"Attempting to send request to Ollama API at {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}...");
                try
                {
                    var response = await _httpClient.PostAsync(_apiUrl, content);
                    Console.WriteLine($"Ollama API response received at {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                    Console.WriteLine($"Ollama API response status: {response.StatusCode}");

                    if (response.IsSuccessStatusCode)
                    {
                        Console.WriteLine("Reading response content...");
                        var responseContent = await response.Content.ReadAsStringAsync();
                        Console.WriteLine($"Ollama API response received. Length: {responseContent.Length} characters");
                        Console.WriteLine($"Ollama API response content (first 100 chars): {responseContent[..Math.Min(100, responseContent.Length)]}...");

                        try
                        {
                            Console.WriteLine("Attempting to deserialize Ollama response...");
                            var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);
                            Console.WriteLine("Response deserialized successfully");

                            Console.WriteLine("Checking if response contains 'response' property...");
                            if (responseObject.TryGetProperty("response", out JsonElement responseElement))
                            {
                                var responseText = responseElement.GetString();
                                Console.WriteLine($"Response property found. Value length: {responseText?.Length ?? 0}");

                                if (string.IsNullOrWhiteSpace(responseText))
                                {
                                    Console.WriteLine("WARNING: Ollama returned empty content");
                                    return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                                }

                                // Thêm thông báo về dữ liệu thời gian thực
                                var finalResponse = new StringBuilder();
                                finalResponse.AppendLine("# THÔNG TIN DU LỊCH " + destination.ToUpper() + " (Với dữ liệu thời gian thực)");
                                finalResponse.AppendLine();
                                finalResponse.AppendLine("> *Thông tin này được tạo dựa trên dữ liệu thời gian thực về thời tiết và thông tin du lịch mới nhất.*");
                                finalResponse.AppendLine();
                                finalResponse.AppendLine(responseText);

                                // Thêm phần ghi chú về dữ liệu thời gian thực
                                finalResponse.AppendLine();
                                finalResponse.AppendLine("---");
                                finalResponse.AppendLine();
                                finalResponse.AppendLine("**Ghi chú:** Thông tin này được tạo dựa trên dữ liệu thời gian thực được cập nhật vào " +
                                                        DateTime.Now.ToString("HH:mm, dd/MM/yyyy") + ". Thông tin có thể thay đổi, vui lòng kiểm tra lại trước khi khởi hành.");

                                var result = finalResponse.ToString();
                                Console.WriteLine($"Successfully generated travel insights with real-time data. Length: {result.Length} characters");
                                return result;
                            }
                            else
                            {
                                Console.WriteLine("ERROR: Response does not contain 'response' property");
                                Console.WriteLine($"Available properties: {string.Join(", ", responseObject.EnumerateObject().Select(p => p.Name))}");
                                return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing Ollama response: {ex.Message}");
                            Console.WriteLine($"Exception type: {ex.GetType().Name}");
                            Console.WriteLine($"Stack trace: {ex.StackTrace}");
                            Console.WriteLine($"Response content: {responseContent}");
                            return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                        }
                    }
                    else
                    {
                        Console.WriteLine("Reading error content from response...");
                        var errorContent = await response.Content.ReadAsStringAsync();
                        Console.WriteLine($"Ollama API error (Status: {response.StatusCode}): {errorContent}");
                        Console.WriteLine($"Request URL: {_apiUrl}");
                        Console.WriteLine($"Request model: {_model}");
                        return "Rất tiếc, không thể tạo thông tin du lịch vào lúc này.";
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    Console.WriteLine($"HTTP request error: {httpEx.Message}");
                    Console.WriteLine($"HTTP status code: {httpEx.StatusCode}");
                    Console.WriteLine($"Stack trace: {httpEx.StackTrace}");
                    return "Rất tiếc, không thể kết nối với dịch vụ AI. Vui lòng kiểm tra kết nối mạng và thử lại sau.";
                }
                catch (TaskCanceledException tcEx)
                {
                    Console.WriteLine($"Task canceled (timeout): {tcEx.Message}");
                    Console.WriteLine($"Stack trace: {tcEx.StackTrace}");
                    return "Rất tiếc, yêu cầu đã hết thời gian chờ. Vui lòng thử lại sau.";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating travel insights: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner exception type: {ex.InnerException.GetType().Name}");
                    Console.WriteLine($"Inner exception stack trace: {ex.InnerException.StackTrace}");
                }

                return "Rất tiếc, không thể tạo thông tin du lịch vào lúc này.";
            }
        }

        public async Task<string> GenerateCustomTourSuggestion(string preferences, string duration, string budget)
        {
            try
            {
                Console.WriteLine("Generating custom tour suggestion with real-time data...");

                // Lấy dữ liệu thời gian thực từ web
                Console.WriteLine("Fetching real-time data for custom tour...");
                var tourismData = await GetRealtimeTourismDataAsync();
                var weatherData = await GetCurrentWeatherAsync();
                var eventsData = await GetCurrentEventsAsync();

                // Tạo phần dữ liệu thời gian thực
                var realtimeData = new StringBuilder();
                realtimeData.AppendLine("DỮ LIỆU THỜI GIAN THỰC:");
                realtimeData.AppendLine("=======================");

                // Thêm thông tin thời tiết
                realtimeData.AppendLine("THÔNG TIN THỜI TIẾT:");
                realtimeData.AppendLine(weatherData);
                realtimeData.AppendLine();

                // Thêm thông tin sự kiện
                realtimeData.AppendLine("SỰ KIỆN HIỆN TẠI:");
                realtimeData.AppendLine(eventsData);
                realtimeData.AppendLine();

                // Thêm thông tin du lịch (rút gọn để không làm prompt quá dài)
                realtimeData.AppendLine("THÔNG TIN DU LỊCH:");
                // Lấy 800 ký tự đầu tiên từ dữ liệu du lịch để tránh prompt quá dài
                var truncatedTourismData = tourismData.Length > 800
                    ? string.Concat(tourismData.AsSpan(0, 800), "...")
                    : tourismData;
                realtimeData.AppendLine(truncatedTourismData);

                string prompt = $"Bạn là chuyên gia lập kế hoạch tour du lịch cho tỉnh Bến Tre ở Việt Nam. " +
                    $"Hãy tạo một lịch trình tour tùy chỉnh với các thông số sau: " +
                    $"Sở thích: {preferences}, Thời gian: {duration}, Ngân sách: {budget}. " +
                    $"\n\nTHÔNG TIN THỜI GIAN THỰC (CẬP NHẬT MỚI NHẤT):\n{realtimeData}\n\n" +
                    $"HƯỚNG DẪN QUAN TRỌNG:\n" +
                    $"1. Bạn PHẢI TUÂN THỦ CHẶT CHẼ các sở thích đã chọn của người dùng. KHÔNG đề xuất hoạt động hoặc phong cách du lịch trái với sở thích đã nêu.\n" +
                    $"2. Đảm bảo đề cập rõ ràng đến ngân sách {budget} trong phản hồi của bạn và KHÔNG đề xuất chi phí vượt quá ngân sách này.\n" +
                    $"3. Đảm bảo đề cập rõ ràng đến thời gian {duration} trong phản hồi của bạn và tạo lịch trình CHÍNH XÁC với số ngày này.\n" +
                    $"4. Nếu người dùng đề cập đến 'tour có hướng dẫn', hãy CHỈ đề xuất các tour có hướng dẫn viên. Nếu họ đề cập đến 'du lịch tự túc', hãy CHỈ đề xuất lịch trình tự khám phá. Nếu họ đề cập đến cả hai, hãy đề xuất kết hợp cả hai loại.\n" +
                    $"5. CHỈ đề xuất các hoạt động liên quan đến thiên nhiên nếu người dùng đề cập đến 'thiên nhiên'. Tương tự cho 'lịch sử', 'ẩm thực', 'mạo hiểm', và 'thư giãn'. KHÔNG đề xuất hoạt động không liên quan đến sở thích đã nêu.\n" +
                    $"6. Nếu người dùng đề cập đến 'trẻ em' hoặc 'gia đình', hãy đảm bảo các hoạt động phù hợp với trẻ em.\n" +
                    $"7. Nếu người dùng đề cập đến 'người lớn tuổi', hãy đảm bảo các hoạt động phù hợp với người lớn tuổi.\n" +
                    $"8. SỬ DỤNG thông tin thời gian thực được cung cấp để đưa ra gợi ý phù hợp với thời tiết hiện tại và các sự kiện đang diễn ra.\n" +
                    $"9. Đề cập đến các điểm du lịch cụ thể được nêu trong dữ liệu thời gian thực.\n" +
                    $"10. Đề xuất các hoạt động phù hợp với điều kiện thời tiết hiện tại.\n" +
                    $"\nBao gồm các điểm tham quan phổ biến và những địa điểm ít người biết đến. Đưa ra các đề xuất cụ thể về " +
                    $"chỗ ở, hoạt động, phương tiện di chuyển và ẩm thực phù hợp với ngân sách. " +
                    $"Cấu trúc phản hồi dưới dạng lịch trình theo ngày với chi phí ước tính cho từng thành phần. " +
                    $"Đảm bảo tiêu đề của lịch trình phản ánh đúng loại hình du lịch đã chọn (Tour có hướng dẫn, Du lịch tự túc, hoặc Kết hợp). " +
                    $"Đánh dấu rõ những gợi ý dựa trên dữ liệu thời gian thực bằng cách thêm '(Dữ liệu thời gian thực)' vào cuối gợi ý đó.";

                Console.WriteLine($"Prompt length with real-time data: {prompt.Length} characters");

                var requestData = new
                {
                    model = _model,
                    prompt = prompt,
                    stream = false,
                    options = new
                    {
                        temperature = 0.7f,
                        top_p = 0.9f,
                        max_tokens = 2000
                    }
                };

                var jsonRequest = JsonSerializer.Serialize(requestData);
                Console.WriteLine($"Ollama API request for custom tour: {jsonRequest}");
                Console.WriteLine($"Ollama API URL: {_apiUrl}");

                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                Console.WriteLine($"Sending request to Ollama API with prompt length: {prompt.Length} characters");

                Console.WriteLine("Attempting to send request to Ollama API for custom tour...");
                var response = await _httpClient.PostAsync(_apiUrl, content);
                Console.WriteLine($"Ollama API response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Ollama API response received. Length: {responseContent.Length} characters");
                    Console.WriteLine($"Ollama API response content (first 100 chars): {responseContent[..Math.Min(100, responseContent.Length)]}...");

                    try
                    {
                        Console.WriteLine("Attempting to deserialize Ollama response...");
                        var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);

                        Console.WriteLine("Checking if response contains 'response' property...");
                        if (responseObject.TryGetProperty("response", out JsonElement responseElement))
                        {
                            var responseText = responseElement.GetString();
                            Console.WriteLine($"Response property found. Value length: {responseText?.Length ?? 0}");

                            if (string.IsNullOrWhiteSpace(responseText))
                            {
                                Console.WriteLine("WARNING: Ollama returned empty content");
                                return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                            }

                            // Thêm thông báo về dữ liệu thời gian thực
                            var finalResponse = new StringBuilder();
                            finalResponse.AppendLine("# TOUR DU LỊCH BẾN TRE TÙY CHỈNH (Với dữ liệu thời gian thực)");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("> *Tour này được tạo dựa trên dữ liệu thời gian thực về thời tiết, sự kiện và thông tin du lịch mới nhất tại Bến Tre.*");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine(responseText);

                            // Thêm phần ghi chú về dữ liệu thời gian thực
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("---");
                            finalResponse.AppendLine();
                            finalResponse.AppendLine("**Ghi chú:** Tour này được tạo dựa trên dữ liệu thời gian thực được cập nhật vào " +
                                                    DateTime.Now.ToString("HH:mm, dd/MM/yyyy") + ". Thông tin có thể thay đổi, vui lòng kiểm tra lại trước khi khởi hành.");

                            var result = finalResponse.ToString();
                            Console.WriteLine($"Successfully generated custom tour with real-time data. Length: {result.Length} characters");
                            return result;
                        }
                        else
                        {
                            Console.WriteLine("ERROR: Response does not contain 'response' property");
                            Console.WriteLine($"Available properties: {string.Join(", ", responseObject.EnumerateObject().Select(p => p.Name))}");
                            return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing Ollama response: {ex.Message}");
                        Console.WriteLine($"Exception type: {ex.GetType().Name}");
                        Console.WriteLine($"Response content: {responseContent}");
                        return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Ollama API error (Status: {response.StatusCode}): {errorContent}");
                    Console.WriteLine($"Request URL: {_apiUrl}");
                    Console.WriteLine($"Request model: {_model}");
                    return "Rất tiếc, không thể tạo gợi ý tour tùy chỉnh vào lúc này.";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating custom tour suggestion: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                return "Rất tiếc, không thể tạo gợi ý tour tùy chỉnh vào lúc này.";
            }
        }
    }
}
