﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models
{
    public class Country
    {
        public int Id { get; set; }
        
        [Required, StringLength(100)]
        public string Name { get; set; }
        
        [Required, StringLength(2)]
        public string Code { get; set; }
        
        public string? Description { get; set; }
        
        public string? ImageUrl { get; set; }
        
        public bool IsFeatured { get; set; } = false;
        
        public List<City>? Cities { get; set; }
    }
}
