@model IEnumerable<ViVu.Models.Location>
@{
    ViewData["Title"] = "Quản lý địa điểm";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý địa điểm</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Quản lý địa điểm</li>
    </ol>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-map-marker-alt me-1"></i> Danh sách địa điểm</div>
                <div>
                    <a asp-area="Admin" asp-controller="Data" asp-action="SeedLocations" class="btn btn-success btn-sm me-2">
                        <i class="fas fa-sync-alt me-1"></i> Cập nhật dữ liệu mẫu
                    </a>
                    <a asp-area="Admin" asp-controller="Data" asp-action="UpdateLocationImages" class="btn btn-info btn-sm me-2">
                        <i class="fas fa-images me-1"></i> Cập nhật hình ảnh
                    </a>
                    <a asp-action="Create" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i> Thêm mới
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>Tên địa điểm</th>
                        <th>Thành phố</th>
                        <th>Nổi bật</th>
                        <th>Địa chỉ</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <img src="@(string.IsNullOrEmpty(item.ImageUrl) ? "/images/default-location.jpg" : item.ImageUrl)"
                                     alt="@item.Name" style="width: 80px; height: 60px; object-fit: cover;" />
                            </td>
                            <td>@item.Name</td>
                            <td>@item.City?.Name</td>
                            <td>
                                <form asp-action="ToggleFeatured" asp-route-id="@item.Id" method="post" class="d-inline">
                                    <button type="submit" class="btn @(item.IsFeatured ? "btn-success" : "btn-outline-secondary") btn-sm">
                                        @(item.IsFeatured ? "Nổi bật" : "Không nổi bật")
                                    </button>
                                </form>
                            </td>
                            <td>@item.Address</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
