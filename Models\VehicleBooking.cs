﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class VehicleBooking
    {
        public int Id { get; set; }
        
        public string UserId { get; set; }
        
        [Required]
        public DateTime BookingDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        public DateTime StartDate { get; set; }
        
        [Required]
        public DateTime EndDate { get; set; }
        
        [Required]
        public decimal TotalPrice { get; set; }
        
        public string? SpecialRequests { get; set; }
        
        public VehicleBookingStatus Status { get; set; } = VehicleBookingStatus.Pending;
        
        [ForeignKey("UserId")]
        [ValidateNever]
        public ApplicationUser ApplicationUser { get; set; }
        
        public List<VehicleBookingDetail> VehicleBookingDetails { get; set; }
    }
    
    public enum VehicleBookingStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
