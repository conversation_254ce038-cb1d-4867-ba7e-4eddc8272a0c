/**
 * Destination Showcase JavaScript
 * Handles the interactive elements for the destination showcase component
 */

document.addEventListener("DOMContentLoaded", function () {
  // Hide the navbar initially and show it when scrolling down
  const navbar = document.querySelector(".navbar");
  const showcaseContainer = document.getElementById(
    "destination-showcase-container"
  );

  if (navbar && showcaseContainer) {
    // Initially hide navbar
    navbar.style.opacity = "0";
    navbar.style.transform = "translateY(-100%)";
    navbar.style.transition = "opacity 0.3s ease, transform 0.3s ease";

    // Show navbar when scrolling past the showcase
    window.addEventListener("scroll", function () {
      if (window.scrollY > 100) {
        navbar.style.opacity = "1";
        navbar.style.transform = "translateY(0)";
      } else {
        navbar.style.opacity = "0";
        navbar.style.transform = "translateY(-100%)";
      }
    });
  }

  // Initialize the destination showcase
  initDestinationShowcase();
});

/**
 * Initialize the destination showcase component
 */
function initDestinationShowcase() {
  // Set up continent navigation
  setupContinentNavigation();

  // Set up tab navigation
  setupTabNavigation();

  // Set up card hover effects
  setupCardHoverEffects();

  // Set up like/share buttons
  setupCardActions();
}

/**
 * Set up continent navigation
 */
function setupContinentNavigation() {
  // Get navigation elements
  const prevBtn = document.querySelector(".continent-nav .nav-btn:first-child");
  const nextBtn = document.querySelector(".continent-nav .nav-btn:last-child");
  const indicators = document.querySelectorAll(".continent-indicator");

  // Set up click events for province navigation
  const prevNav = document.querySelector(".continent-nav:first-child span");
  const nextNav = document.querySelector(".continent-nav:last-child span");

  if (prevNav) {
    prevNav.addEventListener("click", function () {
      currentContinentIndex =
        (currentContinentIndex - 1 + continents.length) % continents.length;
      updateContinentDisplay(continents[currentContinentIndex]);
    });
  }

  if (nextNav) {
    nextNav.addEventListener("click", function () {
      currentContinentIndex = (currentContinentIndex + 1) % continents.length;
      updateContinentDisplay(continents[currentContinentIndex]);
    });
  }

  // Define destinations data
  const continents = [
    {
      name: "Vĩnh Long",
      className: "vinhlong",
      description:
        "Khám phá vẻ đẹp miền sông nước của Vĩnh Long với những vườn trái cây xanh mát, những làng nghề truyền thống và ẩm thực đặc sắc. Vĩnh Long mang đến trải nghiệm du lịch miền Tây Nam Bộ đích thực.",
      destinations: [
        {
          name: "Cù lao An Bình",
          subtitle: "Vườn trái cây",
          image: "/images/banners/banner_home_01.jpg",
        },
        {
          name: "Làng nghề truyền thống",
          subtitle: "Văn hóa địa phương",
          image: "/images/banners/banner_home_02.jpg",
        },
        {
          name: "Chợ nổi Vĩnh Long",
          subtitle: "Văn hóa sông nước",
          image:
            "/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg",
        },
      ],
      next: "Cần Thơ",
      prev: "Tiền Giang",
    },
    {
      name: "Cần Thơ",
      className: "cantho",
      description:
        "Cần Thơ - thành phố lớn nhất miền Tây Nam Bộ với chợ nổi Cái Răng nổi tiếng, ẩm thực phong phú và con người hiếu khách. Đến Cần Thơ, bạn sẽ được trải nghiệm nhịp sống sôi động của vùng đồng bằng sông Cửu Long.",
      destinations: [
        {
          name: "Chợ nổi Cái Răng",
          subtitle: "Di sản văn hóa",
          image: "/images/banners/banner_home_01.jpg",
        },
        {
          name: "Nhà cổ Bình Thủy",
          subtitle: "Kiến trúc độc đáo",
          image: "/images/banners/banner_home_02.jpg",
        },
        {
          name: "Vườn trái cây Mỹ Khánh",
          subtitle: "Trải nghiệm nông nghiệp",
          image:
            "/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg",
        },
      ],
      next: "Bến Tre",
      prev: "Vĩnh Long",
    },
    {
      name: "Bến Tre",
      className: "asia",
      description:
        "Khám phá vẻ đẹp miền sông nước, những vườn dừa xanh mát và văn hóa đặc sắc của xứ dừa Bến Tre. Từ những làng nghề truyền thống đến ẩm thực đặc sản, Bến Tre mang đến trải nghiệm du lịch khó quên cho mọi du khách.",
      destinations: [
        {
          name: "Cồn Phụng",
          subtitle: "Điểm đến văn hóa",
          image:
            "/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg",
        },
        {
          name: "Làng nghề dừa",
          subtitle: "Trải nghiệm địa phương",
          image:
            "/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg",
        },
        {
          name: "Làng hoa Chợ Lách",
          subtitle: "Vườn hoa đặc sắc",
          image: "/images/experiences/exp_lang-hoa-cho-lach_01.jpg",
        },
      ],
      next: "Tiền Giang",
      prev: "Cần Thơ",
    },
    {
      name: "Tiền Giang",
      className: "tiengiang",
      description:
        "Tiền Giang - vùng đất của những vườn cây ăn trái trù phú, những làng nghề truyền thống và những di tích lịch sử văn hóa. Đến Tiền Giang, bạn sẽ được thưởng thức những trái cây ngon nhất miền Tây và khám phá văn hóa địa phương độc đáo.",
      destinations: [
        {
          name: "Cù lao Thới Sơn",
          subtitle: "Vườn trái cây",
          image: "/images/banners/banner_home_01.jpg",
        },
        {
          name: "Làng nghề Gò Công",
          subtitle: "Làng nghề truyền thống",
          image: "/images/banners/banner_home_02.jpg",
        },
        {
          name: "Chợ nổi Cái Bè",
          subtitle: "Văn hóa sông nước",
          image:
            "/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg",
        },
      ],
      next: "Vĩnh Long",
      prev: "Bến Tre",
    },
    {
      name: "Đồng Tháp",
      className: "dongthap",
      description:
        "Đồng Tháp - vùng đất sen hồng với những cánh đồng sen bạt ngàn, những khu di tích lịch sử và văn hóa đặc sắc. Đồng Tháp mang đến cho du khách những trải nghiệm bình dị nhưng đầy ấn tượng về miền Tây Nam Bộ.",
      destinations: [
        {
          name: "Đồng Sen Tháp Mười",
          subtitle: "Cảnh quan thiên nhiên",
          image: "/images/banners/banner_home_01.jpg",
        },
        {
          name: "Khu di tích Xẻo Quýt",
          subtitle: "Di tích lịch sử",
          image: "/images/banners/banner_home_02.jpg",
        },
        {
          name: "Làng hoa Sa Đéc",
          subtitle: "Vườn hoa đặc sắc",
          image:
            "/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg",
        },
      ],
      next: "Vĩnh Long",
      prev: "Tiền Giang",
    },
  ];

  // Current continent index
  let currentContinentIndex = 2; // Start with Bến Tre (index 2)

  // Add click event to previous button
  if (prevBtn) {
    prevBtn.addEventListener("click", function () {
      currentContinentIndex =
        (currentContinentIndex - 1 + continents.length) % continents.length;
      updateContinentDisplay(continents[currentContinentIndex]);
    });
  }

  // Add click event to next button
  if (nextBtn) {
    nextBtn.addEventListener("click", function () {
      currentContinentIndex = (currentContinentIndex + 1) % continents.length;
      updateContinentDisplay(continents[currentContinentIndex]);
    });
  }

  // Add click events to indicators
  indicators.forEach((indicator, index) => {
    indicator.addEventListener("click", function () {
      currentContinentIndex = index;
      updateContinentDisplay(continents[currentContinentIndex]);
    });
  });
}

/**
 * Update the continent display
 * @param {Object} continent - The continent data to display
 */
function updateContinentDisplay(continent) {
  // Update continent name and description
  const continentNameEl = document.querySelector(".continent-name");
  const continentDescEl = document.querySelector(".continent-description");

  if (continentNameEl) {
    continentNameEl.textContent = continent.name;
  }

  if (continentDescEl) {
    continentDescEl.textContent = continent.description;
  }

  // Update showcase class
  const showcase = document.querySelector(".destination-showcase");
  if (showcase) {
    showcase.className = "destination-showcase " + continent.className;
  }

  // Update destination cards
  const destinationCards = document.querySelectorAll(".destination-card");
  if (destinationCards.length === continent.destinations.length) {
    destinationCards.forEach((card, index) => {
      const destination = continent.destinations[index];
      const img = card.querySelector("img");
      const title = card.querySelector(".destination-card-title");
      const subtitle = card.querySelector(".destination-card-subtitle");

      if (img) {
        // Use a fallback image if the destination image is not available
        img.onerror = function () {
          this.src = "/images/default-location.jpg";
        };
        img.src = destination.image;
        img.alt = destination.name;
      }

      if (title) {
        title.textContent = destination.name;
      }

      if (subtitle) {
        subtitle.textContent = destination.subtitle;
      }
    });
  }

  // Update navigation text
  const prevText = document.querySelector(".continent-nav:first-child span");
  const nextText = document.querySelector(".continent-nav:last-child span");

  if (prevText) {
    prevText.textContent = continent.prev;
  }

  if (nextText) {
    nextText.textContent = continent.next;
  }

  // Update indicators
  const indicators = document.querySelectorAll(".continent-indicator");
  indicators.forEach((indicator, index) => {
    if (index === currentContinentIndex) {
      indicator.classList.add("active");
    } else {
      indicator.classList.remove("active");
    }
  });
}

/**
 * Set up card hover effects
 */
function setupCardHoverEffects() {
  const cards = document.querySelectorAll(".destination-card");

  cards.forEach((card) => {
    card.addEventListener("mouseenter", function () {
      this.classList.add("hover");
    });

    card.addEventListener("mouseleave", function () {
      this.classList.remove("hover");
    });
  });
}

/**
 * Set up card action buttons (like/share)
 */
function setupCardActions() {
  const likeButtons = document.querySelectorAll(".card-action-btn .bi-heart");

  likeButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      this.classList.toggle("bi-heart");
      this.classList.toggle("bi-heart-fill");
      this.classList.toggle("text-danger");
    });
  });
}

/**
 * Set up tab navigation functionality
 */
function setupTabNavigation() {
  const tabButtons = document.querySelectorAll(".tab-nav-btn");
  const tabPanels = document.querySelectorAll(".tab-content-panel");

  console.log("Setting up tab navigation:", {
    tabButtons: tabButtons.length,
    tabPanels: tabPanels.length,
  });

  // Initialize first tab as active
  if (tabButtons.length > 0 && tabPanels.length > 0) {
    // Remove any existing active classes first
    tabButtons.forEach((btn) => btn.classList.remove("active"));
    tabPanels.forEach((panel) => panel.classList.remove("active"));

    // Set first tab as active
    tabButtons[0].classList.add("active");
    tabPanels[0].classList.add("active");

    console.log("Initialized first tab as active:", tabPanels[0].id);
  }

  // Add click event listeners to tab buttons
  tabButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Get the target tab from data attribute
      const targetTab = this.getAttribute("data-tab");
      console.log("Tab clicked:", targetTab);

      // Remove active class from all buttons and panels
      tabButtons.forEach((btn) => btn.classList.remove("active"));
      tabPanels.forEach((panel) => panel.classList.remove("active"));

      // Add active class to clicked button
      this.classList.add("active");

      // Show corresponding panel
      const targetPanel = document.getElementById(targetTab + "-panel");
      if (targetPanel) {
        targetPanel.classList.add("active");
        console.log("Activated panel:", targetPanel.id);
      } else {
        console.error("Target panel not found:", targetTab + "-panel");
      }

      // Initialize Bootstrap tabs within the panel if they exist
      initializeBootstrapTabs(targetPanel);
    });
  });
}

/**
 * Initialize Bootstrap tabs within a panel
 */
function initializeBootstrapTabs(panel) {
  if (!panel) return;

  const bootstrapTabs = panel.querySelectorAll('[data-bs-toggle="tab"]');
  bootstrapTabs.forEach((tab) => {
    tab.addEventListener("click", function (e) {
      e.preventDefault();

      // Use Bootstrap's tab functionality
      const tabInstance = new bootstrap.Tab(this);
      tabInstance.show();
    });
  });
}
