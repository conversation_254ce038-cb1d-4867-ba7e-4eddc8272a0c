﻿﻿@{
    ViewData["Title"] = "Tìm kiếm";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">T<PERSON><PERSON> kiếm khách sạn</h3>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label">Tìm kiếm</label>
                                <input type="text" name="searchTerm" class="form-control" placeholder="Nhập tên khách sạn, địa điểm...">
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn địa điểm --</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Thành phố</label>
                                <select name="cityId" class="form-select" asp-items="ViewBag.Cities">
                                    <option value="">-- Chọn thành phố --</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Ngày nhận phòng</label>
                                <input type="date" name="checkIn" class="form-control" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")" min="@DateTime.Today.ToString("yyyy-MM-dd")">
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Ngày trả phòng</label>
                                <input type="date" name="checkOut" class="form-control" value="@DateTime.Today.AddDays(2).ToString("yyyy-MM-dd")" min="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                            
                            <div class="col-md-4">
                                <label class="form-label">Giá từ (VNĐ)</label>
                                <input type="number" name="minPrice" class="form-control" placeholder="Giá thấp nhất">
                            </div>
                            
                            <div class="col-md-4">
                                <label class="form-label">Giá đến (VNĐ)</label>
                                <input type="number" name="maxPrice" class="form-control" placeholder="Giá cao nhất">
                            </div>
                            
                            <div class="col-md-4">
                                <label class="form-label">Xếp hạng sao</label>
                                <select name="starRating" class="form-select">
                                    <option value="">Tất cả</option>
                                    <option value="5">5 sao</option>
                                    <option value="4">4 sao trở lên</option>
                                    <option value="3">3 sao trở lên</option>
                                    <option value="2">2 sao trở lên</option>
                                    <option value="1">1 sao trở lên</option>
                                </select>
                            </div>
                            
                            <div class="col-12 text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="bi bi-search me-2"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-5">
        <h3 class="text-center mb-4">Điểm đến phổ biến</h3>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-dark text-white h-100">
                    <img src="/images/destinations/hanoi.jpg" class="card-img" alt="Hà Nội" style="height: 250px; object-fit: cover;">
                    <div class="card-img-overlay d-flex flex-column justify-content-end" style="background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));">
                        <h5 class="card-title">Hà Nội</h5>
                        <p class="card-text">Khám phá thủ đô nghìn năm văn hiến với nhiều di tích lịch sử và ẩm thực đặc sắc.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-dark text-white h-100">
                    <img src="/images/destinations/danang.jpg" class="card-img" alt="Đà Nẵng" style="height: 250px; object-fit: cover;">
                    <div class="card-img-overlay d-flex flex-column justify-content-end" style="background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));">
                        <h5 class="card-title">Đà Nẵng</h5>
                        <p class="card-text">Thành phố biển xinh đẹp với bãi biển Mỹ Khê nổi tiếng và cầu Rồng biểu tượng.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-dark text-white h-100">
                    <img src="/images/destinations/hochiminh.jpg" class="card-img" alt="Hồ Chí Minh" style="height: 250px; object-fit: cover;">
                    <div class="card-img-overlay d-flex flex-column justify-content-end" style="background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));">
                        <h5 class="card-title">Hồ Chí Minh</h5>
                        <p class="card-text">Thành phố sôi động nhất Việt Nam với nhiều điểm tham quan và trung tâm mua sắm.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4 mb-5">
        <h3 class="text-center mb-4">Gợi ý cho bạn</h3>
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Kỳ nghỉ cuối tuần</h5>
                        <p class="card-text">Tận hưởng kỳ nghỉ ngắn tại các khách sạn gần thành phố với giá ưu đãi.</p>
                        <a href="#" class="btn btn-outline-primary">Xem ngay</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Khách sạn sang trọng</h5>
                        <p class="card-text">Trải nghiệm dịch vụ 5 sao tại các khách sạn sang trọng hàng đầu Việt Nam.</p>
                        <a href="#" class="btn btn-outline-primary">Xem ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
