﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFServiceRepository : IServiceRepository
    {
        private readonly ApplicationDbContext _context;

        public EFServiceRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Service>> GetAllAsync()
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .ToListAsync();
        }

        public async Task<Service> GetByIdAsync(int id)
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Service> GetByIdWithDetailsAsync(int id)
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .Include(s => s.Images)
                .Include(s => s.Reviews)
                    .ThenInclude(r => r.User)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<Service>> GetByLocationIdAsync(int locationId)
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .Where(s => s.LocationId == locationId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Service>> GetByCityIdAsync(int cityId)
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .Where(s => s.CityId == cityId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Service>> GetFeaturedServicesAsync(int count = 6)
        {
            return await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .Where(s => s.IsFeatured)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<Service>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null,
            DateTime? serviceDate = null, decimal? minPrice = null, decimal? maxPrice = null)
        {
            var query = _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .AsQueryable();

            // Tìm theo tên hoặc mô tả
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s => s.Name.Contains(searchTerm) ||
                                        s.Description.Contains(searchTerm) ||
                                        s.Details.Contains(searchTerm));
            }

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(s => s.LocationId == locationId.Value);
            }

            // Lọc theo thành phố
            if (cityId.HasValue)
            {
                query = query.Where(s => s.CityId == cityId.Value);
            }

            // Lọc theo giá
            if (minPrice.HasValue)
            {
                query = query.Where(s => s.Price >= minPrice.Value);
            }

            if (maxPrice.HasValue)
            {
                query = query.Where(s => s.Price <= maxPrice.Value);
            }

            return await query.ToListAsync();
        }

        public async Task AddAsync(Service service)
        {
            await _context.Services.AddAsync(service);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Service service)
        {
            _context.Services.Update(service);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var service = await _context.Services.FindAsync(id);
            if (service != null)
            {
                _context.Services.Remove(service);
                await _context.SaveChangesAsync();
            }
        }
    }
}
