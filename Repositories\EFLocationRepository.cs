﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFLocationRepository : ILocationRepository
    {
        private readonly ApplicationDbContext _context;

        public EFLocationRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Location>> GetAllAsync()
        {
            return await _context.Locations
                .Include(l => l.City)
                    .ThenInclude(c => c.Country)
                .ToListAsync();
        }

        public async Task<Location> GetByIdAsync(int id)
        {
            return await _context.Locations
                .Include(l => l.City)
                    .ThenInclude(c => c.Country)
                .Include(l => l.Accommodations)
                .FirstOrDefaultAsync(l => l.Id == id);
        }

        public async Task<Location> GetByIdWithDetailsAsync(int id)
        {
            return await _context.Locations
                .Include(l => l.City)
                    .ThenInclude(c => c.Country)
                .Include(l => l.Accommodations)
                .Include(l => l.Panoramas)
                .Include(l => l.Tours)
                .Include(l => l.Reviews)
                    .ThenInclude(r => r.User)
                .FirstOrDefaultAsync(l => l.Id == id);
        }

        public async Task<IEnumerable<Location>> GetByCityIdAsync(int cityId)
        {
            return await _context.Locations
                .Include(l => l.City)
                .Where(l => l.CityId == cityId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Location>> GetFeaturedAsync()
        {
            return await _context.Locations
                .Include(l => l.City)
                    .ThenInclude(c => c.Country)
                .Where(l => l.IsFeatured)
                .ToListAsync();
        }

        public async Task AddAsync(Location location)
        {
            await _context.Locations.AddAsync(location);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Location location)
        {
            try
            {
                // Lấy entity hiện tại từ database để tránh mất các mối quan hệ
                var existingLocation = await _context.Locations
                    .AsNoTracking() // Quan trọng: Không theo dõi entity này
                    .FirstOrDefaultAsync(l => l.Id == location.Id);

                if (existingLocation != null)
                {
                    // Cập nhật chỉ các thuộc tính cơ bản, không động đến các mối quan hệ
                    _context.Database.ExecuteSqlRaw(
                        "UPDATE Locations SET " +
                        "Name = {0}, " +
                        "Description = {1}, " +
                        "Address = {2}, " +
                        "Phone = {3}, " +
                        "Email = {4}, " +
                        "Website = {5}, " +
                        "CityId = {6}, " +
                        "IsFeatured = {7}, " +
                        "Latitude = {8}, " +
                        "Longitude = {9}, " +
                        "ImageUrl = {10} " +
                        "WHERE Id = {11}",
                        location.Name,
                        location.Description,
                        location.Address,
                        location.Phone,
                        location.Email,
                        location.Website,
                        location.CityId,
                        location.IsFeatured,
                        location.Latitude,
                        location.Longitude,
                        location.ImageUrl,
                        location.Id);
                }
                else
                {
                    // Nếu không tìm thấy, thêm mới
                    await _context.Locations.AddAsync(location);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong EFLocationRepository.UpdateAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task DeleteAsync(int id)
        {
            var location = await _context.Locations.FindAsync(id);
            if (location != null)
            {
                _context.Locations.Remove(location);
                await _context.SaveChangesAsync();
            }
        }
    }
}
