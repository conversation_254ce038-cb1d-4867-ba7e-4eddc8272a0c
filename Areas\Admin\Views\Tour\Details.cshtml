@model ViVu.Models.Tour

@{
    ViewData["Title"] = "Chi tiết tour";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý tour</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-1"></i>
                    Thông tin tour
                </div>
                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i> Chỉnh sửa
                    </a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash me-1"></i> Xóa
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h3 class="mb-3">@Model.Name</h3>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Địa điểm:</strong>
                                <span>@(Model.Location != null ? Model.Location.Name : ""), @(Model.City != null ? Model.City.Name : "")</span>
                            </div>
                            <div class="mb-3">
                                <strong>Địa chỉ:</strong>
                                <span>@Model.Address</span>
                            </div>
                            <div class="mb-3">
                                <strong>Thời gian:</strong>
                                <span>@Model.Duration ngày</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Giá:</strong>
                                <span class="text-primary fw-bold">@Model.Price.ToString("N0") VNĐ</span>
                            </div>
                            <div class="mb-3">
                                <strong>Số người tối đa:</strong>
                                <span>@Model.MaxGroupSize người</span>
                            </div>
                            <div class="mb-3">
                                <strong>Nổi bật:</strong>
                                <span>@(Model.IsFeatured ? "Có" : "Không")</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>Mô tả</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Description.Replace("\n", "<br>"))
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>Lịch trình</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Itinerary.Replace("\n", "<br>"))
                        </div>
                    </div>
                    
                    @if (Model.TourAmenities != null && Model.TourAmenities.Any())
                    {
                        <div class="mb-4">
                            <h5>Tiện ích</h5>
                            <div class="row">
                                @foreach (var tourAmenity in Model.TourAmenities)
                                {
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(tourAmenity.Amenity.Icon))
                                            {
                                                <i class="@tourAmenity.Amenity.Icon me-2"></i>
                                            }
                                            <span>@tourAmenity.Amenity.Name</span>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
                
                <div class="col-md-4">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" />
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>Chưa có hình ảnh
                        </div>
                    }
                    
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Đánh giá (@Model.Reviews.Count)</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Điểm trung bình:</strong>
                                    <span class="text-warning">
                                        @Model.Reviews.Average(r => r.Rating).ToString("0.0")
                                        <i class="fas fa-star"></i>
                                    </span>
                                </div>
                                
                                @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt).Take(3))
                                {
                                    <div class="border-bottom mb-3 pb-3">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>@review.User.FullName</strong>
                                            </div>
                                            <div class="text-warning">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    <i class="@(i <= review.Rating ? "fas" : "far") fa-star"></i>
                                                }
                                            </div>
                                        </div>
                                        <div class="text-muted small">@review.CreatedAt.ToString("dd/MM/yyyy")</div>
                                        <div>@review.Comment</div>
                                    </div>
                                }
                                
                                @if (Model.Reviews.Count > 3)
                                {
                                    <div class="text-center">
                                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#moreReviews">
                                            Xem thêm đánh giá
                                        </button>
                                    </div>
                                    
                                    <div class="collapse mt-3" id="moreReviews">
                                        @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt).Skip(3))
                                        {
                                            <div class="border-bottom mb-3 pb-3">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <strong>@review.User.FullName</strong>
                                                    </div>
                                                    <div class="text-warning">
                                                        @for (int i = 1; i <= 5; i++)
                                                        {
                                                            <i class="@(i <= review.Rating ? "fas" : "far") fa-star"></i>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="text-muted small">@review.CreatedAt.ToString("dd/MM/yyyy")</div>
                                                <div>@review.Comment</div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i>Chưa có đánh giá nào
                        </div>
                    }
                </div>
            </div>
            
            <div class="mt-4">
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
                </a>
            </div>
        </div>
    </div>
</div>
