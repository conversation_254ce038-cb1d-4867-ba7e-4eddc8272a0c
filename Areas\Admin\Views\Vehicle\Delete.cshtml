@model ViVu.Models.Vehicle

@{
    ViewData["Title"] = "Xóa phương tiện";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý phương tiện</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-1"></i>
            <PERSON><PERSON><PERSON> <PERSON>n xóa
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-circle me-2"></i>
                Bạn có chắc chắn muốn xóa phương tiện này không? Hành động này không thể hoàn tác.
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" />
                    }
                    else
                    {
                        <div class="alert alert-info">Không có hình ảnh</div>
                    }
                </div>
                <div class="col-md-8">
                    <h3>@Model.Name</h3>
                    <div class="mb-3">
                        <span class="badge bg-primary">@Model.Type.ToString()</span>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Biển số:</strong> @Model.LicensePlate</p>
                            <p><strong>Sức chứa:</strong> @Model.Capacity người</p>
                            <p><strong>Giá thuê:</strong> @Model.PricePerDay.ToString("#,##0") VNĐ/ngày</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Địa điểm:</strong> @Model.Location?.Name</p>
                            <p><strong>Thành phố:</strong> @Model.City?.Name</p>
                            <p><strong>ID:</strong> @Model.Id</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Mô tả</h5>
                        <p>@Model.Description</p>
                    </div>
                </div>
            </div>
            
            <form asp-action="Delete" method="post" class="mt-3">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
