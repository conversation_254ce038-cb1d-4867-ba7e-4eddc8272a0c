﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class ServiceBookingController : Controller
    {
        private readonly IServiceBookingRepository _serviceBookingRepository;

        public ServiceBookingController(IServiceBookingRepository serviceBookingRepository)
        {
            _serviceBookingRepository = serviceBookingRepository;
        }

        // GET: Admin/ServiceBooking
        public async Task<IActionResult> Index()
        {
            var bookings = await _serviceBookingRepository.GetAllAsync();
            return View(bookings);
        }

        // GET: Admin/ServiceBooking/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var booking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // GET: Admin/ServiceBooking/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var booking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/ServiceBooking/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, ServiceBooking booking)
        {
            if (id != booking.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var existingBooking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);
                if (existingBooking == null)
                {
                    return NotFound();
                }

                // Chỉ cập nhật trạng thái đặt dịch vụ
                existingBooking.Status = booking.Status;
                existingBooking.SpecialRequests = booking.SpecialRequests;

                // Cập nhật trạng thái chi tiết đặt dịch vụ
                foreach (var detail in existingBooking.ServiceBookingDetails)
                {
                    detail.Status = (ServiceBookingDetailStatus)booking.Status;
                }

                await _serviceBookingRepository.UpdateAsync(existingBooking);
                return RedirectToAction(nameof(Index));
            }

            return View(booking);
        }

        // GET: Admin/ServiceBooking/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var booking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/ServiceBooking/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _serviceBookingRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }
    }
}
