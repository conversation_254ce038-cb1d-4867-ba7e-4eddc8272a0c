﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class TourBookingController : Controller
    {
        private readonly ITourBookingRepository _tourBookingRepository;
        private readonly ApplicationDbContext _context;

        public TourBookingController(
            ITourBookingRepository tourBookingRepository,
            ApplicationDbContext context)
        {
            _tourBookingRepository = tourBookingRepository;
            _context = context;
        }

        // GET: Admin/TourBooking
        public async Task<IActionResult> Index(string status = null)
        {
            IQueryable<TourBooking> bookingsQuery = _context.TourBookings
                .Include(b => b.ApplicationUser)
                .OrderByDescending(b => b.BookingDate);

            // Lọc theo trạng thái nếu có
            if (!string.IsNullOrEmpty(status) && Enum.TryParse<TourBookingStatus>(status, out var bookingStatus))
            {
                bookingsQuery = bookingsQuery.Where(b => b.Status == bookingStatus);
            }

            var bookings = await bookingsQuery.ToListAsync();

            // Truyền danh sách trạng thái để hiển thị bộ lọc
            ViewBag.Statuses = Enum.GetValues(typeof(TourBookingStatus))
                .Cast<TourBookingStatus>()
                .Select(s => new
                {
                    Value = s.ToString(),
                    Text = s.ToString()
                }).ToList();

            ViewBag.SelectedStatus = status;

            return View(bookings);
        }

        // GET: Admin/TourBooking/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var booking = await _tourBookingRepository.GetByIdAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/TourBooking/UpdateStatus/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, TourBookingStatus status)
        {
            var booking = await _context.TourBookings
                .Include(b => b.TourBookingDetails)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            booking.Status = status;

            // Cập nhật trạng thái của tất cả các chi tiết đặt tour
            foreach (var detail in booking.TourBookingDetails)
            {
                detail.Status = (TourBookingDetailStatus)status;
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Details), new { id });
        }

        // POST: Admin/TourBooking/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _tourBookingRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }
    }
}
