﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Extensions;
using ViVu.Models;
using ViVu.Models.ViewModels;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class TourController : Controller
    {
        private readonly ITourRepository _tourRepository;
        private readonly ITourBookingRepository _tourBookingRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public TourController(
            ITourRepository tourRepository,
            ITourBookingRepository tourBookingRepository,
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context)
        {
            _tourRepository = tourRepository;
            _tourBookingRepository = tourBookingRepository;
            _userManager = userManager;
            _context = context;
        }

        // GET: Tour
        public async Task<IActionResult> Index(string searchTerm = null)
        {
            var tours = await _tourRepository.GetAllAsync();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                tours = tours.Where(t =>
                    t.Name.ToLower().Contains(searchTerm) ||
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.Location != null && t.Location.Name.ToLower().Contains(searchTerm)) ||
                    (t.City != null && t.City.Name.ToLower().Contains(searchTerm))
                ).ToList();

                ViewBag.SearchTerm = searchTerm;
            }

            return View(tours);
        }

        // POST: Tour/Search
        [HttpPost]
        public async Task<IActionResult> Search(int? locationId, DateTime? startDate, int? groupSize)
        {
            // Kiểm tra ngày bắt đầu
            if (!startDate.HasValue)
            {
                startDate = DateTime.Today.AddDays(1);
            }

            // Tìm kiếm tour
            var query = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(t => t.LocationId == locationId.Value);
            }

            // Lọc theo số người (nếu có)
            if (groupSize.HasValue)
            {
                query = query.Where(t => t.MaxGroupSize >= groupSize.Value);
            }

            var results = await query.ToListAsync();

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.LocationId = locationId;
            ViewBag.StartDate = startDate;
            ViewBag.GroupSize = groupSize;

            return View("Results", results);
        }

        // GET: Tour/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var tour = await _tourRepository.GetByIdWithDetailsAsync(id);
            if (tour == null)
            {
                return NotFound();
            }

            // Kiểm tra xem người dùng đã đặt tour này chưa
            if (User.Identity != null && User.Identity.IsAuthenticated)
            {
                var userId = _userManager.GetUserId(User);
                ViewBag.HasBooked = await _tourBookingRepository.GetByUserIdAsync(userId) != null &&
                                   (await _tourBookingRepository.GetByUserIdAsync(userId))
                                   .Any(b => b.TourBookingDetails.Any(d => d.TourId == id));

                // Kiểm tra xem người dùng đã đánh giá tour này chưa
                ViewBag.HasReviewed = tour.Reviews != null &&
                                     tour.Reviews.Any(r => r.UserId == userId);

                ViewBag.CanReview = ViewBag.HasBooked && !ViewBag.HasReviewed;
            }
            else
            {
                ViewBag.HasBooked = false;
                ViewBag.HasReviewed = false;
                ViewBag.CanReview = false;
            }

            var viewModel = new TourReviewViewModel
            {
                TourId = tour.Id,
                Tour = tour,
                Reviews = tour.Reviews
            };

            return View(viewModel);
        }

        // GET: Tour/Review/5
        [Authorize]
        public async Task<IActionResult> Review(int id)
        {
            var tour = await _tourRepository.GetByIdWithDetailsAsync(id);
            if (tour == null)
            {
                return NotFound();
            }

            // Kiểm tra xem người dùng đã đặt tour này chưa
            var userId = _userManager.GetUserId(User);
            var hasBooked = await _tourBookingRepository.GetByUserIdAsync(userId) != null &&
                           (await _tourBookingRepository.GetByUserIdAsync(userId))
                           .Any(b => b.TourBookingDetails.Any(d => d.TourId == id));

            if (!hasBooked)
            {
                TempData["ErrorMessage"] = "Bạn cần phải đặt và trải nghiệm tour trước khi đánh giá.";
                return RedirectToAction(nameof(Details), new { id });
            }

            // Kiểm tra xem người dùng đã đánh giá tour này chưa
            var hasReviewed = tour.Reviews != null &&
                             tour.Reviews.Any(r => r.UserId == userId);

            if (hasReviewed)
            {
                TempData["ErrorMessage"] = "Bạn đã đánh giá tour này rồi.";
                return RedirectToAction(nameof(Details), new { id });
            }

            var viewModel = new TourReviewViewModel
            {
                TourId = tour.Id,
                Tour = tour,
                Reviews = tour.Reviews
            };

            return View(viewModel);
        }

        // POST: Tour/SubmitReview
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize]
        public async Task<IActionResult> SubmitReview(TourReviewViewModel viewModel)
        {
            if (!ModelState.IsValid)
            {
                var tour = await _tourRepository.GetByIdWithDetailsAsync(viewModel.TourId);
                viewModel.Tour = tour;
                viewModel.Reviews = tour.Reviews;
                return View("Review", viewModel);
            }

            var userId = _userManager.GetUserId(User);

            // Kiểm tra xem người dùng đã đặt tour này chưa
            var hasBooked = await _tourBookingRepository.GetByUserIdAsync(userId) != null &&
                           (await _tourBookingRepository.GetByUserIdAsync(userId))
                           .Any(b => b.TourBookingDetails.Any(d => d.TourId == viewModel.TourId));

            if (!hasBooked)
            {
                TempData["ErrorMessage"] = "Bạn cần phải đặt và trải nghiệm tour trước khi đánh giá.";
                return RedirectToAction(nameof(Details), new { id = viewModel.TourId });
            }

            // Kiểm tra xem người dùng đã đánh giá tour này chưa
            var tourDetails = await _tourRepository.GetByIdWithDetailsAsync(viewModel.TourId);
            var hasReviewed = tourDetails.Reviews != null &&
                             tourDetails.Reviews.Any(r => r.UserId == userId);

            if (hasReviewed)
            {
                TempData["ErrorMessage"] = "Bạn đã đánh giá tour này rồi.";
                return RedirectToAction(nameof(Details), new { id = viewModel.TourId });
            }

            // Tạo đánh giá mới
            var review = new Review
            {
                TourId = viewModel.TourId,
                UserId = userId,
                Rating = viewModel.Rating,
                Comment = viewModel.Comment,
                CreatedAt = DateTime.UtcNow
            };

            // Lưu đánh giá vào cơ sở dữ liệu
            _context.Reviews.Add(review);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Cảm ơn bạn đã đánh giá tour!";
            return RedirectToAction(nameof(Details), new { id = viewModel.TourId });
        }

        // GET: Tour/Cart
        public IActionResult Cart()
        {
            var cart = HttpContext.Session.GetObjectFromJson<TourCart>("TourCart") ?? new TourCart();
            return View(cart);
        }

        // POST: Tour/AddToCart
        [HttpPost]
        public async Task<IActionResult> AddToCart(int tourId, int numberOfAdults = 1, int numberOfChildren = 0, DateTime? tourDate = null)
        {
            var tour = await _tourRepository.GetByIdAsync(tourId);
            if (tour == null)
            {
                return NotFound();
            }

            // Kiểm tra số lượng người lớn và trẻ em
            if (numberOfAdults <= 0)
            {
                numberOfAdults = 1;
            }

            if (numberOfChildren < 0)
            {
                numberOfChildren = 0;
            }

            // Kiểm tra ngày tour
            if (!tourDate.HasValue || tourDate.Value < DateTime.Today)
            {
                tourDate = DateTime.Today.AddDays(1);
            }

            var cart = HttpContext.Session.GetObjectFromJson<TourCart>("TourCart") ?? new TourCart();

            var item = new TourCartItem
            {
                TourId = tour.Id,
                TourName = tour.Name,
                Price = tour.Price,
                NumberOfAdults = numberOfAdults,
                NumberOfChildren = numberOfChildren,
                TourDate = tourDate.Value,
                ImageUrl = tour.ImageUrl,
                Duration = tour.Duration
            };

            cart.AddItem(item);
            HttpContext.Session.SetObjectAsJson("TourCart", cart);

            return RedirectToAction(nameof(Cart));
        }

        // POST: Tour/RemoveFromCart
        [HttpPost]
        public IActionResult RemoveFromCart(int tourId)
        {
            var cart = HttpContext.Session.GetObjectFromJson<TourCart>("TourCart");
            if (cart != null)
            {
                cart.RemoveItem(tourId);
                HttpContext.Session.SetObjectAsJson("TourCart", cart);
            }

            return RedirectToAction(nameof(Cart));
        }

        // GET: Tour/Checkout
        [Authorize]
        public IActionResult Checkout()
        {
            var cart = HttpContext.Session.GetObjectFromJson<TourCart>("TourCart");
            if (cart == null || cart.Items.Count == 0)
            {
                return RedirectToAction(nameof(Cart));
            }

            return View(cart);
        }

        // POST: Tour/Checkout
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize]
        public async Task<IActionResult> Checkout(string specialRequests)
        {
            try
            {
                var cart = HttpContext.Session.GetObjectFromJson<TourCart>("TourCart");
                if (cart == null || cart.Items.Count == 0)
                {
                    return RedirectToAction(nameof(Cart));
                }

                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return Challenge();
                }

                // Tạo đơn đặt tour
                var booking = new TourBooking
                {
                    UserId = user.Id,
                    BookingDate = DateTime.UtcNow,
                    TourDate = cart.Items.First().TourDate,
                    TotalPrice = cart.TotalPrice,
                    SpecialRequests = specialRequests,
                    Status = TourBookingStatus.Pending,
                    TourBookingDetails = new List<TourBookingDetail>()
                };

                // Thêm chi tiết đặt tour
                foreach (var item in cart.Items)
                {
                    booking.TourBookingDetails.Add(new TourBookingDetail
                    {
                        TourId = item.TourId,
                        NumberOfAdults = item.NumberOfAdults,
                        NumberOfChildren = item.NumberOfChildren,
                        Price = item.Price,
                        TourDate = item.TourDate,
                        Status = TourBookingDetailStatus.Pending
                    });
                }

                // Lưu đơn đặt tour vào cơ sở dữ liệu
                await _tourBookingRepository.AddAsync(booking);

                // Xóa giỏ hàng
                cart.Clear();
                HttpContext.Session.SetObjectAsJson("TourCart", cart);

                // Đảm bảo booking.Id đã được thiết lập sau khi lưu
                if (booking.Id <= 0)
                {
                    throw new Exception("Không thể tạo đơn đặt tour. ID không hợp lệ.");
                }

                return RedirectToAction(nameof(BookingConfirmation), new { id = booking.Id });
            }
            catch (Exception ex)
            {
                // Log lỗi
                Console.WriteLine($"Lỗi khi đặt tour: {ex.Message}");

                // Thông báo lỗi cho người dùng
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi xác nhận đặt tour. Vui lòng thử lại sau.";

                return RedirectToAction(nameof(Checkout));
            }
        }

        // GET: Tour/BookingConfirmation/5
        [Authorize]
        public async Task<IActionResult> BookingConfirmation(int id)
        {
            try
            {
                if (id <= 0)
                {
                    TempData["ErrorMessage"] = "Mã đặt tour không hợp lệ.";
                    return RedirectToAction(nameof(MyBookings));
                }

                var booking = await _tourBookingRepository.GetByIdAsync(id);
                if (booking == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy thông tin đặt tour.";
                    return RedirectToAction(nameof(MyBookings));
                }

                var user = await _userManager.GetUserAsync(User);
                if (user == null || (booking.UserId != user.Id && !User.IsInRole("Admin")))
                {
                    TempData["ErrorMessage"] = "Bạn không có quyền xem thông tin đặt tour này.";
                    return RedirectToAction(nameof(MyBookings));
                }

                // Đảm bảo booking.TourBookingDetails không null
                if (booking.TourBookingDetails == null || !booking.TourBookingDetails.Any())
                {
                    TempData["ErrorMessage"] = "Thông tin chi tiết đặt tour không hợp lệ.";
                    return RedirectToAction(nameof(MyBookings));
                }

                return View(booking);
            }
            catch (Exception ex)
            {
                // Log lỗi
                Console.WriteLine($"Lỗi khi xem xác nhận đặt tour: {ex.Message}");

                // Thông báo lỗi cho người dùng
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi hiển thị thông tin xác nhận đặt tour.";

                return RedirectToAction(nameof(MyBookings));
            }
        }

        // GET: Tour/MyBookings
        [Authorize]
        public async Task<IActionResult> MyBookings()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Challenge();
            }

            var bookings = await _tourBookingRepository.GetByUserIdAsync(user.Id);
            return View(bookings);
        }

        // GET: Tour/BookingDetails/5
        [Authorize]
        public async Task<IActionResult> BookingDetails(int id)
        {
            var booking = await _tourBookingRepository.GetByIdAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null || (booking.UserId != user.Id && !User.IsInRole("Admin")))
            {
                return Forbid();
            }

            return View(booking);
        }

        // POST: Tour/CancelBooking/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize]
        public async Task<IActionResult> CancelBooking(int id)
        {
            var booking = await _tourBookingRepository.GetByIdAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null || (booking.UserId != user.Id && !User.IsInRole("Admin")))
            {
                return Forbid();
            }

            // Chỉ cho phép hủy đặt tour nếu chưa diễn ra
            if (booking.Status == TourBookingStatus.Pending || booking.Status == TourBookingStatus.Confirmed)
            {
                booking.Status = TourBookingStatus.Cancelled;
                await _tourBookingRepository.UpdateAsync(booking);
                return RedirectToAction(nameof(MyBookings));
            }

            return RedirectToAction(nameof(BookingDetails), new { id });
        }
    }
}
