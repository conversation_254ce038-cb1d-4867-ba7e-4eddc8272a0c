﻿/* Global Styles */
:root {
  --primary-color: #0d6efd;
  --primary-color-rgb: 13, 110, 253;
  --primary-dark: #0a58ca;
  --primary-light: #6ea8fe;
  --secondary-color: #6c757d;
  --secondary-color-rgb: 108, 117, 125;
  --success-color: #198754;
  --success-color-rgb: 25, 135, 84;
  --info-color: #0dcaf0;
  --info-color-rgb: 13, 202, 240;
  --warning-color: #ffc107;
  --warning-color-rgb: 255, 193, 7;
  --danger-color: #dc3545;
  --danger-color-rgb: 220, 53, 69;
  --light-color: #f8f9fa;
  --light-color-rgb: 248, 249, 250;
  --dark-color: #212529;
  --dark-color-rgb: 33, 37, 41;
  --accent-color: #ff6b6b;
  --accent-color-rgb: 255, 107, 107;
  --accent-dark: #e74c3c;
  --text-color: #333;
  --text-light: #6c757d;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --transition-speed: 0.3s;
  --font-family-heading: "Montserrat", sans-serif;
  --font-family-body: "Poppins", sans-serif;
}

body {
  font-family: var(--font-family-body);
  color: var(--text-color);
  line-height: 1.6;
  padding-top: 70px; /* Adjusted padding for fixed navbar */
  background-color: #f9f9f9;
  background-image: linear-gradient(to bottom, #ffffff, #f9f9f9);
  background-attachment: fixed;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

/* Fix for anchor links being hidden under fixed header */
html {
  scroll-padding-top: 100px; /* Ensures anchors are scrolled to with padding for the fixed header */
}

::selection {
  background-color: var(--primary-color);
  color: white;
}

.wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.content-wrapper {
  flex: 1;
  padding-top: 30px; /* Increased padding */
  position: relative;
  z-index: 1;
  margin-top: 10px; /* Added margin for better spacing */
}

/* Navbar Styles */
.navbar {
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-color: #ffffff !important;
  transition: all 0.3s ease;
  height: auto;
  z-index: 1030; /* Ensure navbar is above other content */
  width: 100%;
  left: 0;
  right: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 56px;
}

.navbar .container-fluid {
  padding: 6px 15px;
  width: 100%;
}

.navbar.scrolled {
  padding: 0;
}

.navbar.scrolled .container-fluid {
  padding: 4px 15px;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.4rem;
  position: relative;
  overflow: hidden;
  padding: 0.5rem 0.75rem;
  margin-right: 1.5rem;
  color: var(--primary-color) !important;
  display: flex;
  align-items: center;
}

.navbar-brand i {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.navbar-brand::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.navbar-brand:hover {
  background-color: rgba(13, 110, 253, 0.05);
  border-radius: 4px;
}

.navbar-brand:hover::after {
  width: 100%;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2px;
}

.navbar-nav .nav-item {
  display: flex;
  align-items: center;
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.4rem 0.7rem;
  transition: all 0.3s ease;
  position: relative;
  font-size: 0.85rem;
  color: #333 !important;
  border-radius: 4px;
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color) !important;
  background-color: rgba(13, 110, 253, 0.05);
}

.navbar-nav .nav-link:hover::after {
  width: 80%;
}

.navbar-nav .nav-item.active .nav-link {
  color: var(--primary-color) !important;
  background-color: rgba(13, 110, 253, 0.08);
}

.navbar-nav .nav-item.active .nav-link::after {
  width: 80%;
}

/* Agoda-style header elements */
.navbar .btn {
  padding: 0.35rem 0.7rem;
  font-size: 0.85rem;
  margin-left: 5px;
}

/* Agoda-style header elements */
.navbar .navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0d6efd !important;
}

.navbar .navbar-nav .nav-link {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.4rem 0.7rem;
  color: #333 !important;
}

.navbar .navbar-nav .nav-link:hover {
  color: #0d6efd !important;
  background-color: rgba(13, 110, 253, 0.05);
}

/* Agoda-style buttons in header */
.navbar .btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 0.35rem 0.7rem;
}

.navbar .btn-outline-primary {
  border-color: #0d6efd;
  color: #0d6efd;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 0.35rem 0.7rem;
}

/* Card Styles */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-speed) ease;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
  background-color: #fff;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(var(--primary-color-rgb), 0.1) 0%,
    rgba(var(--info-color-rgb), 0.1) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-speed) ease;
  z-index: 0;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card:hover::before {
  opacity: 1;
}

.card-img-top {
  transition: transform 0.8s ease;
  height: 200px;
  object-fit: cover;
}

.card:hover .card-img-top {
  transform: scale(1.08);
}

/* Destination Cards */
.destination-card {
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.destination-card img {
  transition: transform 0.5s ease;
}

.destination-card:hover img {
  transform: scale(1.05);
}

.featured-destinations-large .destination-card {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.featured-destinations-large .destination-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Tour Cards */
.tour-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tour-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15) !important;
}

.tour-card .card-img-top {
  transition: transform 0.5s ease;
}

.tour-card:hover .card-img-top {
  transform: scale(1.05);
}

.featured-tour-banner {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.featured-tour-banner:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* AI Recommendation Form Styles */
.recommendation-container {
  background-color: #f8f9fa;
  background-image: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 80vh;
  padding: 3rem 0;
}

.travel-type-card,
.interest-card,
.travel-group-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  overflow: hidden;
}

.travel-type-card:hover,
.interest-card:hover,
.travel-group-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.travel-type-card.selected,
.interest-card.selected,
.travel-group-card.selected {
  border: 2px solid var(--primary-color);
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
}

/* AI Recommendation Results Styles */
.recommendation-results-container {
  background-color: #f8f9fa;
  min-height: 80vh;
  padding: 2rem 0;
}

.recommendation-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.4s ease;
  margin-bottom: 2rem;
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  opacity: 0;
  transform: translateY(20px);
}

.recommendation-card.animated {
  opacity: 1;
  transform: translateY(0);
}

.recommendation-card-hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.recommendation-card .card-header {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  color: white;
  padding: 1.25rem;
  border: none;
}

.recommendation-card .card-body {
  padding: 1.5rem;
}

.recommendation-tabs {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  padding: 0.5rem;
  margin-bottom: 2rem;
  display: flex;
  overflow-x: auto;
  transition: all 0.3s ease;
}

.recommendation-tabs.sticky-tabs {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 1000;
  border-radius: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 2rem;
}

.recommendation-tab {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  margin: 0 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  color: var(--text-color);
  text-decoration: none;
}

.recommendation-tab:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.recommendation-tab.active {
  background-color: var(--primary-color);
  color: white;
}

.recommendation-tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
}

.recommendation-section {
  margin-bottom: 2rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.recommendation-section.animated {
  opacity: 1;
  transform: translateY(0);
}

.recommendation-nav {
  position: sticky;
  top: 90px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  padding: 1rem;
}

.recommendation-nav-item {
  display: block;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  color: var(--text-color);
  text-decoration: none;
}

.recommendation-nav-item:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.recommendation-nav-item.active {
  background-color: var(--primary-color);
  color: white;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feature-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.recommendation-card:hover .feature-icon {
  background-color: var(--primary-color);
  transform: scale(1.1);
}

.recommendation-card:hover .feature-icon i {
  color: white;
}

.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.travel-type-card.selected .card-body,
.interest-card.selected .card-body,
.travel-group-card.selected .card-body {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.travel-type-card .icon-container {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  transition: all 0.3s ease;
}

.travel-type-card.selected .icon-container {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  transform: scale(1.1);
}

.form-step {
  display: none;
  animation: fadeIn 0.5s ease;
}

.form-step.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Budget slider styling */
.budget-slider-container {
  max-width: 500px;
  margin: 0 auto;
}

.budget-display {
  font-size: 2rem;
  color: var(--primary-color);
  font-weight: bold;
}

/* Pulse animation for buttons */
.pulse-animation {
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 rgba(var(--primary-color-rgb), 0.4);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(var(--primary-color-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0);
  }
}

.card-body {
  position: relative;
  z-index: 1;
  padding: 1.5rem;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.card-title {
  font-family: var(--font-family-heading);
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--dark-color);
  transition: color var(--transition-speed) ease;
}

.card:hover .card-title {
  color: var(--primary-color);
}

.card-text {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Button Styles */
.btn {
  border-radius: var(--border-radius);
  padding: 0.6rem 1.5rem;
  font-weight: 500;
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 0.9rem;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  z-index: -1;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  border: none;
  box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary-color) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #146c43 0%, var(--success-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(25, 135, 84, 0.4);
}

.btn-group .btn {
  margin: 0 2px;
}

/* Hero Section */
.hero-section {
  position: relative;
  margin-top: 0;
  overflow: hidden;
  z-index: 1;
  margin-bottom: 30px;
  width: 100%;
}

/* Carousel Styling */
.carousel-item {
  position: relative;
}

.carousel-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

.carousel-caption {
  z-index: 2;
  bottom: 20%;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
  background-color: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-radius: 10px;
  max-width: 80%;
  margin: 0 auto;
  backdrop-filter: blur(3px);
}

.carousel-indicators {
  z-index: 3;
}

.carousel-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 5px;
}

.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  opacity: 0.7;
  z-index: 3;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
}

.hero-section h1,
.hero-section h2.display-4 {
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
  font-weight: 800;
  letter-spacing: 1px;
  color: #ffffff;
}

.hero-section p {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  max-width: 600px;
  margin: 0 auto;
  font-weight: 500;
  color: #ffffff;
}

.hero-section .btn {
  margin-top: 1.5rem;
  padding: 0.8rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.hero-section .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
  background-color: var(--primary-dark);
}

.hero-search-form {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  margin-top: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Footer Styles */
.footer {
  background-color: var(--dark-color);
  color: white;
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(
    90deg,
    var(--primary-color) 0%,
    var(--info-color) 50%,
    var(--success-color) 100%
  );
}

.footer h5 {
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.footer h5::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
  display: inline-block;
  padding: 3px 0;
}

.footer a:hover {
  color: white;
  transform: translateX(5px);
}

.footer .social-links a {
  margin-right: 15px;
  font-size: 1.2rem;
  transition: all var(--transition-speed) ease;
}

.footer .social-links a:hover {
  transform: translateY(-5px);
}

.footer hr {
  background-color: rgba(255, 255, 255, 0.1);
  margin: 2rem 0;
}

/* Form Controls */
.form-control,
.form-select {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
  background-color: #f9f9f9;
}

.form-control:hover,
.form-select:hover {
  border-color: rgba(13, 110, 253, 0.3);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
  background-color: #fff;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1rem 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.8;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > label {
  padding: 1rem 0.75rem;
}

.input-group {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.input-group-text {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
  border-radius: 4px;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-info {
  background-color: var(--info-color);
  color: var(--dark-color);
}

.badge-warning {
  background-color: var(--warning-color);
  color: var(--dark-color);
}

.badge-danger {
  background-color: var(--danger-color);
}

/* Star Ratings */
.bi-star-fill,
.bi-star-half,
.bi-star {
  color: var(--warning-color);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all var(--transition-speed) ease;
}

.rating:hover .bi-star-fill {
  transform: scale(1.1);
}

/* Price Display */
.text-success {
  color: var(--success-color) !important;
  font-weight: 600;
}

.price-tag {
  position: relative;
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: var(--success-color);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(25, 135, 84, 0.2);
}

.price-tag::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-right: 5px solid var(--success-color);
  border-bottom: 5px solid transparent;
}

.price-old {
  text-decoration: line-through;
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Responsive Adjustments */
/* Navbar right side */
.navbar .d-flex.align-items-center {
  gap: 10px;
}

.navbar .btn-group .btn {
  font-size: 0.85rem;
  padding: 0.4rem 0.75rem;
}

.navbar .dropdown-menu {
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
  padding: 0.5rem 0;
}

.navbar .dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.navbar .dropdown-item:hover {
  background-color: rgba(13, 110, 253, 0.05);
}

@media (max-width: 992px) {
  .navbar-collapse {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    width: 100%;
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .navbar-nav .nav-link::after {
    display: none;
  }

  .navbar-nav .nav-item:last-child .nav-link {
    border-bottom: none;
  }

  .hero-section h1 {
    font-size: 2.25rem;
  }

  .hero-section p {
    font-size: 1.1rem;
  }

  .btn {
    padding: 0.5rem 1.25rem;
  }
}

@media (max-width: 768px) {
  body {
    padding-top: 80px; /* Adjusted for mobile */
  }

  .navbar {
    padding: 10px 0;
  }

  .navbar-brand {
    font-size: 1.25rem;
  }

  .content-wrapper {
    padding-top: 20px;
    margin-top: 0;
  }

  .hero-section {
    padding-top: 10px;
  }

  .hero-section h1 {
    font-size: 1.75rem;
  }

  .hero-section p {
    font-size: 1rem;
  }

  .card {
    margin-bottom: 25px;
  }

  .footer {
    padding: 3rem 0 1.5rem;
  }

  .footer h5 {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  body {
    padding-top: 70px; /* Further adjusted for small mobile */
  }

  .content-wrapper {
    padding-top: 15px;
  }

  .hero-section {
    padding-top: 5px;
  }

  .hero-section h1 {
    font-size: 1.5rem;
  }

  .btn {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }

  .card-title {
    font-size: 1.25rem;
  }

  .footer {
    text-align: center;
  }

  .footer h5::after {
    left: 50%;
    transform: translateX(-50%);
  }
}

/* Homepage Tabs Styles */
.homepage-tabs-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.homepage-main-tabs {
  background: linear-gradient(135deg, var(--primary-color), var(--info-color));
  padding: 0;
}

.homepage-main-tabs .nav-tabs {
  border: none;
  margin: 0;
}

.homepage-main-tabs .nav-link {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
}

.homepage-main-tabs .nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.homepage-main-tabs .nav-link.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-bottom: 3px solid white;
}

.homepage-tab-content {
  background: white;
  min-height: 600px;
  padding: 2rem;
}

.homepage-tab-content .tab-pane {
  animation: fadeInUp 0.5s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles for Homepage Tabs */
@media (max-width: 768px) {
  .homepage-main-tabs .nav-tabs {
    flex-wrap: wrap;
  }

  .homepage-main-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .homepage-tab-content {
    padding: 1rem;
    min-height: 500px;
  }

  .homepage-tabs-container {
    margin: 0 10px;
  }
}

@media (max-width: 576px) {
  .homepage-main-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .homepage-main-tabs .nav-link i {
    display: none;
  }

  .homepage-tab-content {
    padding: 0.75rem;
  }
}

/* Section Styles */
.section-title {
  position: relative;
  margin-bottom: 2.5rem;
  text-align: center;
}

.section-title h2 {
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.section-title h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
  border-radius: 3px;
}

.section-title p {
  max-width: 700px;
  margin: 0.5rem auto 0;
  color: var(--text-light);
}

/* Custom Components */
.feature-box {
  padding: 2rem;
  border-radius: var(--border-radius);
  background-color: white;
  box-shadow: var(--box-shadow);
  transition: all var(--transition-speed) ease;
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.feature-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
  transition: height var(--transition-speed) ease;
  z-index: -1;
}

.feature-box:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-box:hover::before {
  height: 100%;
  opacity: 0.05;
}

.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 1.75rem;
  color: var(--primary-color);
  transition: all var(--transition-speed) ease;
}

.feature-box:hover .feature-icon {
  background-color: var(--primary-color);
  color: white;
  transform: rotateY(180deg);
}

.feature-title {
  font-weight: 600;
  margin-bottom: 1rem;
}

/* Testimonial Card */
.testimonial-card {
  padding: 2rem;
  border-radius: var(--border-radius);
  background-color: white;
  box-shadow: var(--box-shadow);
  margin-bottom: 30px;
  position: relative;
}

.testimonial-card::before {
  content: "\201C";
  font-family: Georgia, serif;
  position: absolute;
  top: 10px;
  left: 20px;
  font-size: 5rem;
  color: rgba(var(--primary-color-rgb), 0.1);
  line-height: 1;
}

.testimonial-content {
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
}

.testimonial-author-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
  border: 3px solid rgba(var(--primary-color-rgb), 0.1);
}

.testimonial-author-info h5 {
  margin-bottom: 0;
  font-weight: 600;
}

.testimonial-author-info p {
  margin-bottom: 0;
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Booking Process */
.booking-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3rem;
  position: relative;
}

.booking-step {
  text-align: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.booking-step::after {
  content: "";
  position: absolute;
  top: 25px;
  left: 50%;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #e9ecef 0%, #dee2e6 100%);
  z-index: -1;
  transition: all var(--transition-speed) ease;
}

.booking-step:last-child::after {
  display: none;
}

.booking-step-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  position: relative;
  z-index: 1;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  font-size: 1.25rem;
}

.booking-step-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-light);
  transition: all var(--transition-speed) ease;
}

.booking-step-description {
  font-size: 0.85rem;
  color: var(--text-light);
  max-width: 150px;
  margin: 0 auto;
}

.booking-step.active .booking-step-icon {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  border-color: var(--primary-color);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.booking-step.active .booking-step-title {
  color: var(--primary-color);
  font-weight: 700;
}

.booking-step.completed .booking-step-icon {
  background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
  border-color: var(--success-color);
  color: white;
  box-shadow: 0 5px 15px rgba(25, 135, 84, 0.3);
}

.booking-step.completed::after {
  background: linear-gradient(90deg, var(--success-color) 0%, #146c43 100%);
}

.booking-step.completed .booking-step-title {
  color: var(--success-color);
}

/* Additional Utility Classes */
.shadow-hover {
  transition: box-shadow var(--transition-speed) ease;
}

.shadow-hover:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.bg-light-primary {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.bg-light-success {
  background-color: rgba(var(--success-color-rgb), 0.1);
}

.bg-light-info {
  background-color: rgba(var(--info-color-rgb), 0.1);
}

.bg-light-warning {
  background-color: rgba(var(--warning-color-rgb), 0.1);
}

.rounded-lg {
  border-radius: var(--border-radius) !important;
}

.text-gradient {
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Search Tabs Styling - Agoda Style */
.search-tabs {
  border-bottom: 1px solid #e9ecef;
  background-color: #fff;
}

.search-tabs .nav-tabs {
  border-bottom: none;
  padding: 0 15px;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.search-tabs .nav-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.search-tabs .nav-item {
  margin-bottom: 0;
  white-space: nowrap;
}

.search-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  border-radius: 0;
  padding: 1rem 1.5rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  margin-right: 5px;
  display: flex;
  align-items: center;
}

.search-tabs .nav-link:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.search-tabs .nav-link.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: transparent;
  font-weight: 600;
}

.search-tabs .nav-link i {
  font-size: 1.1rem;
  margin-right: 8px;
}

/* Search Form Styling */
.tab-content {
  background-color: #fff;
}

.tab-content .form-label {
  font-size: 0.85rem;
  margin-bottom: 0.3rem;
}

.input-group {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-group:hover {
  border-color: #adb5bd;
}

.input-group-text {
  border: none;
  background-color: #fff;
  color: #6c757d;
  padding-right: 0;
}

.form-control,
.form-select {
  border: none;
  padding-left: 0;
  box-shadow: none;
}

.form-control:focus,
.form-select:focus {
  box-shadow: none;
  outline: none;
}

.input-group:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

/* Custom styling for date inputs */
input[type="date"] {
  position: relative;
  padding-right: 30px;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

/* Search Button Styling */
.tab-content .btn-primary {
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  padding: 0.75rem 1.5rem;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
  transition: all 0.3s ease;
}

.tab-content .btn-primary:hover {
  background: linear-gradient(135deg, #0a58ca 0%, #084298 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

/* Additional styles for search form responsiveness */
@media (max-width: 767.98px) {
  .search-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .tab-content {
    padding: 1rem !important;
  }

  .tab-content .form-label {
    font-size: 0.8rem;
  }

  .tab-content .btn-primary {
    margin-top: 1rem;
    width: 100%;
  }
}
