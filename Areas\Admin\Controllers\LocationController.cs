﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class LocationController : Controller
    {
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public LocationController(
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            ApplicationDbContext context,
            IWebHostEnvironment hostEnvironment)
        {
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Location
        public async Task<IActionResult> Index()
        {
            var locations = await _locationRepository.GetAllAsync();
            return View(locations);
        }

        // GET: Admin/Location/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var location = await _locationRepository.GetByIdWithDetailsAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            return View(location);
        }

        // GET: Admin/Location/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View();
        }

        // POST: Admin/Location/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Location location, IFormFile imageFile)
        {
            Console.WriteLine("=== BẮT ĐẦU XỬ LÝ TẠO ĐỊA ĐIỂM MỚI ===");
            Console.WriteLine($"Thông tin địa điểm: Tên={location.Name}, CityId={location.CityId}");

            // Kiểm tra xem có file hình ảnh được gửi lên không
            bool hasImageFile = imageFile != null && imageFile.Length > 0;

            // Log thông tin về file hình ảnh
            if (hasImageFile)
            {
                Console.WriteLine($"Đã nhận file hình ảnh: {imageFile.FileName}, Kích thước: {imageFile.Length} bytes, ContentType: {imageFile.ContentType}");
            }
            else
            {
                Console.WriteLine("Không có file hình ảnh được gửi lên");
            }

            // Kiểm tra ModelState
            if (!ModelState.IsValid)
            {
                Console.WriteLine("ModelState không hợp lệ. Các lỗi:");
                foreach (var state in ModelState)
                {
                    if (state.Value.Errors.Count > 0)
                    {
                        Console.WriteLine($"- {state.Key}: {string.Join(", ", state.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                }
            }
            else
            {
                Console.WriteLine("ModelState hợp lệ, tiếp tục xử lý");
            }

            // Xóa lỗi City nếu có
            ModelState.Remove("City");

            if (ModelState.IsValid)
            {
                try
                {
                    // Xử lý hình ảnh
                    if (hasImageFile)
                    {
                        Console.WriteLine("Bắt đầu xử lý hình ảnh...");
                        location.ImageUrl = await SaveImage(imageFile);
                        Console.WriteLine($"Đã xử lý hình ảnh thành công, URL: {location.ImageUrl}");
                    }
                    else
                    {
                        Console.WriteLine("Bỏ qua xử lý hình ảnh do không có file");
                    }

                    // Đảm bảo City là null để tránh lỗi validation
                    location.City = null;

                    // Khởi tạo các danh sách để tránh lỗi null reference
                    location.Accommodations = new List<Accommodation>();
                    location.Tours = new List<Tour>();
                    location.Panoramas = new List<Panorama360>();
                    location.Reviews = new List<Review>();

                    // Thêm địa điểm vào cơ sở dữ liệu
                    Console.WriteLine("Bắt đầu thêm địa điểm vào cơ sở dữ liệu...");
                    await _locationRepository.AddAsync(location);
                    Console.WriteLine($"Đã thêm địa điểm thành công, ID: {location.Id}");

                    Console.WriteLine("=== KẾT THÚC XỬ LÝ TẠO ĐỊA ĐIỂM - THÀNH CÔNG ===");
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    // Ghi log lỗi chi tiết
                    Console.WriteLine($"LỖI khi thêm mới địa điểm: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");

                    // Thêm lỗi vào ModelState để hiển thị cho người dùng
                    ModelState.AddModelError("", $"Lỗi khi thêm mới: {ex.Message}");
                    Console.WriteLine("=== KẾT THÚC XỬ LÝ TẠO ĐỊA ĐIỂM - THẤT BẠI ===");
                }
            }

            // Nếu có lỗi, chuẩn bị dữ liệu cho view và trả về view
            Console.WriteLine("Chuẩn bị dữ liệu cho view Create...");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", location.CityId);
            return View(location);
        }

        // GET: Admin/Location/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", location.CityId);
            return View(location);
        }

        // POST: Admin/Location/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Location location, IFormFile imageFile)
        {
            Console.WriteLine("=== BẮT ĐẦU XỬ LÝ CẬP NHẬT ĐỊA ĐIỂM ===");
            Console.WriteLine($"Thông tin địa điểm: Id={id}, Tên={location.Name}, CityId={location.CityId}");

            if (id != location.Id)
            {
                Console.WriteLine($"LỖI: Id không khớp: {id} != {location.Id}");
                return NotFound();
            }

            // Kiểm tra xem có file hình ảnh được gửi lên không
            bool hasImageFile = imageFile != null && imageFile.Length > 0;

            // Log thông tin về file hình ảnh
            if (hasImageFile)
            {
                Console.WriteLine($"Đã nhận file hình ảnh: {imageFile.FileName}, Kích thước: {imageFile.Length} bytes, ContentType: {imageFile.ContentType}");
            }
            else
            {
                Console.WriteLine("Không có file hình ảnh được gửi lên");
            }

            // Kiểm tra ModelState
            if (!ModelState.IsValid)
            {
                Console.WriteLine("ModelState không hợp lệ. Các lỗi:");
                foreach (var state in ModelState)
                {
                    if (state.Value.Errors.Count > 0)
                    {
                        Console.WriteLine($"- {state.Key}: {string.Join(", ", state.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                }
            }
            else
            {
                Console.WriteLine("ModelState hợp lệ, tiếp tục xử lý");
            }

            // Xóa lỗi City nếu có
            ModelState.Remove("City");

            if (ModelState.IsValid)
            {
                try
                {
                    Console.WriteLine($"Tìm kiếm địa điểm hiện tại với Id={id}");
                    var existingLocation = await _locationRepository.GetByIdAsync(id);
                    if (existingLocation == null)
                    {
                        Console.WriteLine($"LỖI: Không tìm thấy địa điểm với Id={id}");
                        return NotFound();
                    }
                    Console.WriteLine($"Đã tìm thấy địa điểm: {existingLocation.Name}");

                    // Lưu lại URL hình ảnh hiện tại trước khi cập nhật
                    string currentImageUrl = existingLocation.ImageUrl;
                    Console.WriteLine($"URL hình ảnh hiện tại: {currentImageUrl ?? "Không có"}");

                    // Cập nhật thông tin
                    Console.WriteLine("Cập nhật thông tin địa điểm...");
                    existingLocation.Name = location.Name;
                    existingLocation.Description = location.Description;
                    existingLocation.Address = location.Address;
                    existingLocation.Phone = location.Phone;
                    existingLocation.Email = location.Email;
                    existingLocation.Website = location.Website;
                    existingLocation.CityId = location.CityId;
                    existingLocation.IsFeatured = location.IsFeatured;
                    existingLocation.Latitude = location.Latitude;
                    existingLocation.Longitude = location.Longitude;

                    // Đảm bảo City là null để tránh lỗi validation
                    existingLocation.City = null;

                    // Đảm bảo không mất URL hình ảnh nếu không có file mới
                    if (!hasImageFile && string.IsNullOrEmpty(existingLocation.ImageUrl))
                    {
                        Console.WriteLine("Giữ nguyên URL hình ảnh hiện tại do không có file mới và URL hiện tại trống");
                        existingLocation.ImageUrl = currentImageUrl;
                    }

                    // Xử lý hình ảnh
                    if (hasImageFile)
                    {
                        try
                        {
                            Console.WriteLine("Bắt đầu xử lý hình ảnh mới...");

                            // Xóa hình ảnh cũ nếu có
                            if (!string.IsNullOrEmpty(existingLocation.ImageUrl))
                            {
                                Console.WriteLine($"Xóa hình ảnh cũ: {existingLocation.ImageUrl}");
                                DeleteImage(existingLocation.ImageUrl);
                            }

                            // Lưu hình ảnh mới
                            Console.WriteLine("Lưu hình ảnh mới...");
                            existingLocation.ImageUrl = await SaveImage(imageFile);
                            Console.WriteLine($"Đã lưu hình ảnh mới thành công, URL: {existingLocation.ImageUrl}");
                        }
                        catch (Exception ex)
                        {
                            // Nếu có lỗi khi xử lý hình ảnh, giữ nguyên hình ảnh cũ
                            Console.WriteLine($"LỖI khi xử lý hình ảnh: {ex.Message}");
                            Console.WriteLine($"Stack trace: {ex.StackTrace}");
                            Console.WriteLine("Giữ nguyên URL hình ảnh cũ");
                            existingLocation.ImageUrl = currentImageUrl;
                            ModelState.AddModelError("", $"Lỗi khi xử lý hình ảnh: {ex.Message}");
                        }
                    }
                    else
                    {
                        // Giữ nguyên hình ảnh cũ nếu không có file mới
                        Console.WriteLine("Giữ nguyên URL hình ảnh cũ do không có file mới");
                        existingLocation.ImageUrl = currentImageUrl;
                    }

                    // Cập nhật địa điểm
                    Console.WriteLine("Cập nhật địa điểm vào cơ sở dữ liệu...");

                    // Đảm bảo giữ nguyên các mối quan hệ
                    // Lưu ý: Phương thức UpdateAsync đã được sửa đổi để giữ nguyên các mối quan hệ
                    // nên chúng ta không cần phải đặt lại các thuộc tính Accommodations, Tours, Panoramas ở đây

                    await _locationRepository.UpdateAsync(existingLocation);
                    Console.WriteLine("Đã cập nhật địa điểm thành công");

                    // Kiểm tra nếu là AJAX request
                    bool isAjaxRequest = Request.Headers["X-Requested-With"] == "XMLHttpRequest";
                    Console.WriteLine($"Là AJAX request: {isAjaxRequest}");

                    if (isAjaxRequest)
                    {
                        Console.WriteLine("Trả về kết quả JSON thành công");
                        Console.WriteLine("=== KẾT THÚC XỬ LÝ CẬP NHẬT ĐỊA ĐIỂM - THÀNH CÔNG ===");
                        return Json(new { success = true, message = "Cập nhật thành công" });
                    }

                    Console.WriteLine("=== KẾT THÚC XỬ LÝ CẬP NHẬT ĐỊA ĐIỂM - THÀNH CÔNG ===");
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    Console.WriteLine($"LỖI DbUpdateConcurrencyException: {ex.Message}");

                    if (!await LocationExists(location.Id))
                    {
                        Console.WriteLine($"Không tìm thấy địa điểm với Id={location.Id}");
                        return NotFound();
                    }
                    else
                    {
                        Console.WriteLine("Lỗi cập nhật đồng thời, ném lại ngoại lệ");
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    // Ghi log lỗi chi tiết
                    Console.WriteLine($"LỖI khi cập nhật địa điểm: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");

                    // Thêm lỗi vào ModelState để hiển thị cho người dùng
                    ModelState.AddModelError("", $"Lỗi khi cập nhật: {ex.Message}");

                    // Kiểm tra nếu là AJAX request
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        Console.WriteLine("Trả về lỗi dạng JSON");
                        return BadRequest(new {
                            success = false,
                            message = "Lỗi khi cập nhật",
                            errors = new string[] { ex.Message }
                        });
                    }

                    Console.WriteLine("=== KẾT THÚC XỬ LÝ CẬP NHẬT ĐỊA ĐIỂM - THẤT BẠI ===");
                }
            }

            Console.WriteLine("Chuẩn bị dữ liệu cho view Edit...");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", location.CityId);

            // Kiểm tra nếu là AJAX request
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                var errors = ModelState
                    .Where(x => x.Value.Errors.Count > 0)
                    .SelectMany(x => x.Value.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToArray();

                Console.WriteLine($"Trả về lỗi dạng JSON: {string.Join(", ", errors)}");
                return BadRequest(new { success = false, errors = errors });
            }

            return View(location);
        }

        // GET: Admin/Location/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            return View(location);
        }

        // POST: Admin/Location/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location != null)
            {
                // Xóa hình ảnh
                if (!string.IsNullOrEmpty(location.ImageUrl))
                {
                    DeleteImage(location.ImageUrl);
                }

                // Xóa địa điểm
                await _locationRepository.DeleteAsync(id);
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Admin/Location/ToggleFeatured/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleFeatured(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            // Đảo ngược trạng thái nổi bật
            location.IsFeatured = !location.IsFeatured;

            // Đảm bảo City là null để tránh lỗi validation
            location.City = null;

            // Cập nhật địa điểm
            // Lưu ý: Phương thức UpdateAsync đã được sửa đổi để giữ nguyên các mối quan hệ
            await _locationRepository.UpdateAsync(location);

            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> LocationExists(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            return location != null;
        }

        private async Task<string> SaveImage(IFormFile imageFile)
        {
            Console.WriteLine("=== BẮT ĐẦU XỬ LÝ HÌNH ẢNH ===");

            if (imageFile == null || imageFile.Length == 0)
            {
                Console.WriteLine("LỖI: Không có file hình ảnh được cung cấp hoặc file rỗng");
                throw new ArgumentException("Không có file hình ảnh được cung cấp", nameof(imageFile));
            }

            Console.WriteLine($"Thông tin file: Tên={imageFile.FileName}, Kích thước={imageFile.Length} bytes, ContentType={imageFile.ContentType}");

            // Kiểm tra loại file
            string fileExtension = Path.GetExtension(imageFile.FileName).ToLower();
            string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };

            Console.WriteLine($"Phần mở rộng file: {fileExtension}");

            if (!allowedExtensions.Contains(fileExtension))
            {
                Console.WriteLine($"LỖI: Loại file không được hỗ trợ: {fileExtension}");
                throw new ArgumentException("Loại file không được hỗ trợ. Chỉ chấp nhận: .jpg, .jpeg, .png, .gif, .webp", nameof(imageFile));
            }

            // Kiểm tra kích thước file (giới hạn 10MB)
            if (imageFile.Length > 10 * 1024 * 1024)
            {
                Console.WriteLine($"LỖI: Kích thước file quá lớn: {imageFile.Length} bytes");
                throw new ArgumentException("Kích thước file quá lớn. Giới hạn tối đa là 10MB", nameof(imageFile));
            }

            string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "locations");
            Console.WriteLine($"Thư mục lưu trữ: {uploadsFolder}");

            // Đảm bảo tên file không chứa ký tự đặc biệt
            string originalFileName = Path.GetFileNameWithoutExtension(imageFile.FileName);
            originalFileName = string.Join("_", originalFileName.Split(Path.GetInvalidFileNameChars()));
            Console.WriteLine($"Tên file gốc (đã xử lý): {originalFileName}");

            // Tạo tên file duy nhất
            string uniqueFileName = Guid.NewGuid().ToString() + "_" + originalFileName + fileExtension;
            string filePath = Path.Combine(uploadsFolder, uniqueFileName);
            Console.WriteLine($"Tên file duy nhất: {uniqueFileName}");
            Console.WriteLine($"Đường dẫn đầy đủ: {filePath}");

            // Đảm bảo thư mục tồn tại
            try
            {
                if (!Directory.Exists(uploadsFolder))
                {
                    Console.WriteLine($"Thư mục không tồn tại, đang tạo: {uploadsFolder}");
                    Directory.CreateDirectory(uploadsFolder);
                    Console.WriteLine("Đã tạo thư mục thành công");
                }
                else
                {
                    Console.WriteLine("Thư mục đã tồn tại");
                }

                // Kiểm tra quyền ghi
                try
                {
                    string testFile = Path.Combine(uploadsFolder, "test_write_permission.txt");
                    System.IO.File.WriteAllText(testFile, "Test write permission");
                    System.IO.File.Delete(testFile);
                    Console.WriteLine("Kiểm tra quyền ghi: OK");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"LỖI: Không có quyền ghi vào thư mục: {ex.Message}");
                    throw new Exception($"Không có quyền ghi vào thư mục: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LỖI khi tạo thư mục: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw new Exception($"Không thể tạo thư mục để lưu hình ảnh: {ex.Message}", ex);
            }

            try
            {
                Console.WriteLine("Bắt đầu lưu file...");
                // Lưu file
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(fileStream);
                }
                Console.WriteLine("Đã lưu file thành công");

                // Kiểm tra xem file có tồn tại sau khi lưu không
                if (!System.IO.File.Exists(filePath))
                {
                    Console.WriteLine("LỖI: File không tồn tại sau khi lưu");
                    throw new Exception("Không thể lưu file hình ảnh. File không tồn tại sau khi lưu.");
                }
                else
                {
                    var fileInfo = new FileInfo(filePath);
                    Console.WriteLine($"Kiểm tra file sau khi lưu: Tồn tại, Kích thước={fileInfo.Length} bytes");
                }

                string imageUrl = "/images/locations/" + uniqueFileName;
                Console.WriteLine($"URL hình ảnh: {imageUrl}");
                Console.WriteLine("=== KẾT THÚC XỬ LÝ HÌNH ẢNH - THÀNH CÔNG ===");
                return imageUrl;
            }
            catch (IOException ioEx)
            {
                // Ghi log lỗi IO
                Console.WriteLine($"LỖI IO khi lưu hình ảnh: {ioEx.Message}");
                Console.WriteLine($"Stack trace: {ioEx.StackTrace}");
                throw new Exception($"Lỗi khi ghi file: {ioEx.Message}", ioEx);
            }
            catch (UnauthorizedAccessException uaEx)
            {
                // Ghi log lỗi quyền truy cập
                Console.WriteLine($"LỖI quyền truy cập khi lưu hình ảnh: {uaEx.Message}");
                Console.WriteLine($"Stack trace: {uaEx.StackTrace}");
                throw new Exception($"Không có quyền truy cập để lưu file: {uaEx.Message}", uaEx);
            }
            catch (Exception ex)
            {
                // Ghi log lỗi chung
                Console.WriteLine($"LỖI khi lưu hình ảnh: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw new Exception($"Lỗi khi lưu hình ảnh: {ex.Message}", ex);
            }
        }

        private void DeleteImage(string imageUrl)
        {
            Console.WriteLine($"=== BẮT ĐẦU XÓA HÌNH ẢNH: {imageUrl} ===");

            if (string.IsNullOrEmpty(imageUrl))
            {
                Console.WriteLine("URL hình ảnh trống, bỏ qua xóa");
                return;
            }

            try
            {
                string fileName = imageUrl.Replace("/images/locations/", "");
                string filePath = Path.Combine(_hostEnvironment.WebRootPath, "images", "locations", fileName);
                Console.WriteLine($"Đường dẫn file cần xóa: {filePath}");

                if (System.IO.File.Exists(filePath))
                {
                    Console.WriteLine("File tồn tại, tiến hành xóa...");
                    System.IO.File.Delete(filePath);
                    Console.WriteLine("Đã xóa file thành công");
                }
                else
                {
                    Console.WriteLine("File không tồn tại, bỏ qua xóa");
                }

                Console.WriteLine("=== KẾT THÚC XÓA HÌNH ẢNH - THÀNH CÔNG ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LỖI khi xóa hình ảnh: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Console.WriteLine("=== KẾT THÚC XÓA HÌNH ẢNH - THẤT BẠI ===");
                // Không ném lại ngoại lệ để tránh ảnh hưởng đến luồng chính
            }
        }
    }
}
