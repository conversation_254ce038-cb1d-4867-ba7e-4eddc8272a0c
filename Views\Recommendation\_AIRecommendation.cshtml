@model ViVu.Models.TravelRecommendation

<div id="<EMAIL>" class="recommendation-card animate-on-scroll">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="mb-0">
                <i class="fas fa-robot me-2"></i>@Model.Title
            </h3>
            <span class="badge bg-light text-primary">AI Generated</span>
        </div>
    </div>
    <div class="card-body">
        <p class="lead">@Model.Description</p>

        <div class="mb-4">
            <div class="d-flex flex-wrap gap-2 mb-3">
                @if (Model.IncludesNature)
                {
                    <span class="badge bg-success"><i class="fas fa-tree me-1"></i>Thiên nhiên</span>
                }
                @if (Model.IncludesHistory)
                {
                    <span class="badge bg-secondary"><i class="fas fa-landmark me-1"></i><PERSON><PERSON><PERSON> sử</span>
                }
                @if (Model.IncludesFood)
                {
                    <span class="badge bg-danger"><i class="fas fa-utensils me-1"></i>Ẩm thực</span>
                }
                @if (Model.IncludesAdventure)
                {
                    <span class="badge bg-warning text-dark"><i class="fas fa-hiking me-1"></i>Mạo hiểm</span>
                }
                @if (Model.IncludesRelaxation)
                {
                    <span class="badge bg-info text-dark"><i class="fas fa-spa me-1"></i>Thư giãn</span>
                }
            </div>

            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted d-block">Thời gian đề xuất</small>
                            <strong>@Model.RecommendedDuration ngày</strong>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted d-block">Ngân sách dự kiến</small>
                            <strong>@String.Format("{0:N0}", Model.EstimatedMinBudget) - @String.Format("{0:N0}", Model.EstimatedMaxBudget) VNĐ</strong>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted d-block">Phù hợp với</small>
                            <strong>
                                @if (Model.SuitableForChildren && Model.SuitableForElders)
                                {
                                    <span>Mọi lứa tuổi</span>
                                }
                                else if (Model.SuitableForChildren)
                                {
                                    <span>Gia đình có trẻ em</span>
                                }
                                else if (Model.SuitableForElders)
                                {
                                    <span>Người lớn tuổi</span>
                                }
                                else
                                {
                                    <span>Người trưởng thành</span>
                                }
                            </strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ai-content markdown-content">
            <ul class="nav nav-tabs mb-4" id="<EMAIL>" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="itinerary" aria-selected="true">
                        <i class="fas fa-route me-2"></i>Lịch trình
                    </button>
                </li>
                @if (!string.IsNullOrEmpty(Model.BudgetBreakdownSection))
                {
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="budget" aria-selected="false">
                            <i class="fas fa-money-bill-wave me-2"></i>Chi tiết ngân sách
                        </button>
                    </li>
                }
                @if (!string.IsNullOrEmpty(Model.TipsSection))
                {
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="tips" aria-selected="false">
                            <i class="fas fa-lightbulb me-2"></i>Lưu ý du lịch
                        </button>
                    </li>
                }
            </ul>

            <div class="tab-content" id="<EMAIL>">
                <div class="tab-pane fade show active" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            @Html.Raw(Markdig.Markdown.ToHtml(Model.ItineraryDetails))
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.BudgetBreakdownSection))
                {
                    <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                @Html.Raw(Markdig.Markdown.ToHtml(Model.BudgetBreakdownSection))
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.TipsSection))
                {
                    <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                @Html.Raw(Markdig.Markdown.ToHtml(Model.TipsSection))
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <a asp-controller="Recommendation" asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-primary">
                <i class="fas fa-info-circle me-2"></i>Xem chi tiết
            </a>

            @if (User.Identity.IsAuthenticated)
            {
                <form asp-controller="Recommendation" asp-action="SaveRecommendation" asp-route-id="@Model.Id" method="post">
                    <button type="submit" class="btn btn-outline-success">
                        <i class="fas fa-bookmark me-2"></i>Lưu gợi ý này
                    </button>
                </form>
            }
        </div>
    </div>
</div>
