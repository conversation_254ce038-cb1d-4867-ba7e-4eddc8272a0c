﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class ActivityController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;

        public ActivityController(
            ApplicationDbContext context,
            ILocationRepository locationRepository,
            ICityRepository cityRepository)
        {
            _context = context;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
        }

        // GET: Activity
        public async Task<IActionResult> Index()
        {
            // Trong tương lai, đây sẽ là danh sách các hoạt động
            // Hiện tại, chúng ta sẽ sử dụng danh sách tour làm ví dụ
            var activities = await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .ToListAsync();

            return View(activities);
        }

        // POST: Activity/Search
        [HttpPost]
        public async Task<IActionResult> Search(int? locationId, DateTime? activityDate)
        {
            // Kiểm tra ngày hoạt động
            if (!activityDate.HasValue)
            {
                activityDate = DateTime.Today.AddDays(1);
            }

            // Tìm kiếm hoạt động (hiện tại sử dụng tour làm ví dụ)
            var query = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(t => t.LocationId == locationId.Value);
            }

            var results = await query.ToListAsync();

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.LocationId = locationId;
            ViewBag.ActivityDate = activityDate;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View("Results", results);
        }

        // GET: Activity/Results
        public async Task<IActionResult> Results(int? locationId, DateTime? activityDate)
        {
            // Kiểm tra ngày hoạt động
            if (!activityDate.HasValue)
            {
                activityDate = DateTime.Today.AddDays(1);
            }

            // Tìm kiếm hoạt động (hiện tại sử dụng tour làm ví dụ)
            var query = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(t => t.LocationId == locationId.Value);
            }

            var results = await query.ToListAsync();

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.LocationId = locationId;
            ViewBag.ActivityDate = activityDate;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View(results);
        }
    }
}
