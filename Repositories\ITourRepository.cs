﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface ITourRepository
    {
        Task<IEnumerable<Tour>> GetAllAsync();
        Task<Tour> GetByIdAsync(int id);
        Task<Tour> GetByIdWithDetailsAsync(int id);
        Task<IEnumerable<Tour>> GetFeaturedToursAsync(int count = 6);
        Task<IEnumerable<Tour>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null, 
            DateTime? tourDate = null, decimal? minPrice = null, decimal? maxPrice = null);
        Task AddAsync(Tour tour);
        Task UpdateAsync(Tour tour);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
}
