@{
    ViewData["Title"] = "Xem phố - " + ViewBag.LocationName;
    Layout = "_Layout";
}

<div class="container-fluid p-0">
    <div class="row g-0">
        <div class="col-md-3 bg-light p-3 d-flex flex-column" style="height: calc(100vh - 56px); overflow-y: auto;">
            <h2 class="mb-4">@ViewBag.LocationName</h2>

            <div class="mt-3 mb-4">
                <h5>Thông tin địa điểm</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="bi bi-geo-alt-fill text-primary me-2"></i> V<PERSON> độ: @ViewBag.Latitude</li>
                    <li class="mb-2"><i class="bi bi-geo-alt-fill text-primary me-2"></i> Kinh độ: @ViewBag.Longitude</li>
                </ul>
            </div>

            <div class="mt-auto">
                <a href="javascript:history.back()" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left me-2"></i> Quay lại
                </a>
            </div>
        </div>
        <div class="col-md-9" style="height: calc(100vh - 56px);">
            <div id="street-view" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/googleMapsConfig.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAMqvsRr2toGUxy7yF_HBNuNCifHE3S1UU&callback=initStreetViewPage" async defer></script>
    <script>
        function initStreetViewPage() {
            const lat = @ViewBag.Latitude;
            const lng = @ViewBag.Longitude;

            // Kiểm tra xem có Street View tại vị trí này không
            checkStreetViewAvailability(lat, lng, function(available) {
                if (available) {
                    // Nếu có, hiển thị Street View
                    initStreetView('street-view', lat, lng);
                } else {
                    // Nếu không, hiển thị thông báo và bản đồ thông thường
                    document.getElementById('street-view').innerHTML =
                        '<div class="alert alert-warning text-center p-3 mb-3">' +
                        '<i class="bi bi-exclamation-triangle-fill me-2"></i> ' +
                        'Chế độ xem phố không khả dụng tại vị trí này. Đang hiển thị bản đồ thông thường.' +
                        '</div>' +
                        '<div id="map" style="width: 100%; height: calc(100% - 80px);"></div>';

                    // Hiển thị bản đồ thông thường
                    const map = initMap('map', lat, lng);
                    addMarker(map, lat, lng, '@ViewBag.LocationName', '<strong>@ViewBag.LocationName</strong>');
                }
            });
        }
    </script>
}
