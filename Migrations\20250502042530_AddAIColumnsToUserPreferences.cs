﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ViVu.Migrations
{
    /// <inheritdoc />
    public partial class AddAIColumnsToUserPreferences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AIPrompt",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "UseAI",
                table: "UserPreferences",
                type: "bit",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AIPrompt",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "UseA<PERSON>",
                table: "UserPreferences");
        }
    }
}
