﻿@model ViVu.Models.Category

<div class="container mt-5">
    <h2 class="text-center text-primary">✏ Update Category</h2>

    <form asp-action="Update" method="post" class="card p-4 shadow-lg">
        <div asp-validation-summary="All" class="text-danger"></div>

        <input type="hidden" asp-for="Id" />

        <div class="form-group mb-3">
            <label asp-for="Name" class="fw-bold">Category Name</label>
            <input asp-for="Name" class="form-control" placeholder="Enter category name..." />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>

        <div class="text-center">
            <button type="submit" class="btn btn-success">✅ Save Changes</button>
            <a asp-action="Index" class="btn btn-secondary">🔙 Back</a>

            <button type="submit" formaction="@Url.Action("DeleteConfirmed", "Categories", new { id = Model.Id })"
                    class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this category?');">
                🗑 Delete
            </button>
        </div>
    </form>
</div>
