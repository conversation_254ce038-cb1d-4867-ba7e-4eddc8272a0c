// Sidebar Dropdown Enhanced Functionality
document.addEventListener("DOMContentLoaded", function () {
  // Initialize sidebar dropdowns
  initializeSidebarDropdowns();

  // Handle click outside to close dropdowns
  document.addEventListener("click", function (e) {
    if (!e.target.closest(".sidebar-dropdown")) {
      closeAllDropdowns();
    }
  });
});

function initializeSidebarDropdowns() {
  const dropdownButtons = document.querySelectorAll(
    ".sidebar-dropdown .dropdown-toggle"
  );

  dropdownButtons.forEach((button) => {
    // Remove any existing event listeners
    button.removeEventListener("click", handleDropdownClick);

    // Add click event listener
    button.addEventListener("click", handleDropdownClick);

    // Add keyboard support
    button.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        handleDropdownClick.call(this, e);
      }
    });
  });
}

function handleDropdownClick(e) {
  e.preventDefault();
  e.stopPropagation();

  const button = this;
  const dropdown = button.closest(".sidebar-dropdown");
  const menu = button.nextElementSibling;

  // Check if menu exists and has the correct class
  if (!menu || !menu.classList.contains("sidebar-dropdown-menu")) {
    console.error("Dropdown menu not found or incorrect class");
    return;
  }

  const isOpen = menu.classList.contains("show");

  // Close all other dropdowns first
  closeAllDropdowns();

  if (!isOpen) {
    // Open this dropdown
    openDropdown(button, dropdown, menu);
  }
}

function openDropdown(button, dropdown, menu) {
  console.log("Opening dropdown:", button, dropdown, menu);

  // Add show classes
  button.classList.add("show");
  dropdown.classList.add("show");
  menu.classList.add("show");

  // Set ARIA attributes
  button.setAttribute("aria-expanded", "true");

  // Position the dropdown
  positionDropdown(button, menu);

  // Force display
  menu.style.display = "block";
  menu.style.visibility = "visible";
  menu.style.opacity = "1";

  console.log("Dropdown opened, classes:", menu.classList.toString());
  console.log("Dropdown styles:", {
    display: menu.style.display,
    visibility: menu.style.visibility,
    opacity: menu.style.opacity,
    position: menu.style.position,
    left: menu.style.left,
    top: menu.style.top,
  });

  // Focus management
  setTimeout(() => {
    const firstItem = menu.querySelector(".dropdown-item");
    if (firstItem) {
      firstItem.focus();
    }
  }, 100);
}

function closeAllDropdowns() {
  const dropdowns = document.querySelectorAll(".sidebar-dropdown");

  dropdowns.forEach((dropdown) => {
    const button = dropdown.querySelector(".dropdown-toggle");
    const menu = dropdown.querySelector(".sidebar-dropdown-menu");

    if (button && menu) {
      button.classList.remove("show");
      dropdown.classList.remove("show");
      menu.classList.remove("show");
      button.setAttribute("aria-expanded", "false");
    }
  });
}

function positionDropdown(button, menu) {
  const buttonRect = button.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  const menuWidth = 280;
  const menuHeight = 300; // estimated

  // Reset position
  menu.style.top = "0px";
  menu.style.left = "60px";
  menu.style.right = "auto";
  menu.style.bottom = "auto";

  // Check vertical positioning
  if (buttonRect.top + menuHeight > viewportHeight) {
    // Position above if not enough space below
    const spaceAbove = buttonRect.top;
    const spaceBelow = viewportHeight - buttonRect.bottom;

    if (spaceAbove > spaceBelow) {
      menu.style.top = "auto";
      menu.style.bottom = "0px";
    } else {
      // Keep at top but adjust if needed
      const maxTop = viewportHeight - menuHeight - 20;
      const newTop = Math.min(0, maxTop - buttonRect.top);
      menu.style.top = newTop + "px";
    }
  }

  // Check horizontal positioning
  if (buttonRect.left + 60 + menuWidth > viewportWidth) {
    // Position to the left of the button
    menu.style.left = "auto";
    menu.style.right = "60px";
  }
}

// Enhanced keyboard navigation
document.addEventListener("keydown", function (e) {
  const activeDropdown = document.querySelector(".sidebar-dropdown.show");
  if (!activeDropdown) return;

  const menu = activeDropdown.querySelector(".sidebar-dropdown-menu");
  const items = menu.querySelectorAll(".dropdown-item:not(.disabled)");
  const currentFocus = document.activeElement;
  const currentIndex = Array.from(items).indexOf(currentFocus);

  switch (e.key) {
    case "Escape":
      e.preventDefault();
      closeAllDropdowns();
      activeDropdown.querySelector(".dropdown-toggle").focus();
      break;

    case "ArrowDown":
      e.preventDefault();
      const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
      items[nextIndex].focus();
      break;

    case "ArrowUp":
      e.preventDefault();
      const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
      items[prevIndex].focus();
      break;

    case "Home":
      e.preventDefault();
      items[0].focus();
      break;

    case "End":
      e.preventDefault();
      items[items.length - 1].focus();
      break;
  }
});

// Handle window resize
window.addEventListener("resize", function () {
  const openDropdowns = document.querySelectorAll(".sidebar-dropdown.show");
  openDropdowns.forEach((dropdown) => {
    const button = dropdown.querySelector(".dropdown-toggle");
    const menu = dropdown.querySelector(".sidebar-dropdown-menu");
    if (button && menu) {
      positionDropdown(button, menu);
    }
  });
});

// Export functions for external use
window.SidebarDropdown = {
  init: initializeSidebarDropdowns,
  closeAll: closeAllDropdowns,
  open: openDropdown,
};
