﻿﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TravelRecommendation
    {
        public int Id { get; set; }

        public string? UserId { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        // Recommended itinerary details
        [Required]
        public string ItineraryDetails { get; set; } = string.Empty;

        // Type of recommendation (Guided Tour, Independent Travel, Mixed)
        [Required]
        public string RecommendationType { get; set; } = string.Empty;

        // Estimated budget
        public decimal? EstimatedMinBudget { get; set; }
        public decimal? EstimatedMaxBudget { get; set; }

        // Recommended duration in days
        public int RecommendedDuration { get; set; }

        // Related items (stored as comma-separated IDs)
        public string? RelatedTourIds { get; set; }
        public string? RelatedAccommodationIds { get; set; }
        public string? RelatedServiceIds { get; set; }
        public string? RelatedLocationIds { get; set; }
        public string? RelatedVehicleIds { get; set; }

        // Recommendation categories
        public bool IncludesNature { get; set; } = false;
        public bool IncludesHistory { get; set; } = false;
        public bool IncludesFood { get; set; } = false;
        public bool IncludesAdventure { get; set; } = false;
        public bool IncludesRelaxation { get; set; } = false;

        // Recommendation details
        public bool SuitableForChildren { get; set; } = false;
        public bool SuitableForElders { get; set; } = false;
        public bool RequiresPhysicalEffort { get; set; } = false;
        public bool HasAccessibility { get; set; } = false;

        // Recommendation priority (1-10, higher means more recommended)
        public int Priority { get; set; } = 5;

        // Recommendation source
        public string? RecommendationSource { get; set; } = "AI"; // AI, UserHistory, PopularChoice

        // Detailed sections for the itinerary
        public string? HighlightsSection { get; set; }
        public string? DayByDaySection { get; set; }
        public string? TipsSection { get; set; }
        public string? BudgetBreakdownSection { get; set; }

        // Specific activities
        public bool IncludesCooking { get; set; } = false;
        public bool IncludesCrafts { get; set; } = false;
        public bool IncludesFarming { get; set; } = false;
        public bool IncludesBoating { get; set; } = false;
        public bool IncludesCycling { get; set; } = false;
        public bool IncludesFishing { get; set; } = false;

        // Travel companions
        public bool ForSoloTravelers { get; set; } = false;
        public bool ForCouples { get; set; } = false;
        public bool ForFriends { get; set; } = false;
        public bool ForFamiliesWithChildren { get; set; } = false;
        public bool ForElderlyTravelers { get; set; } = false;

        // Experience level
        public bool ForFirstTimeVisitors { get; set; } = true;
        public bool ForReturnVisitors { get; set; } = false;
        public bool ForExperiencedTravelers { get; set; } = false;

        // Has the user viewed this recommendation?
        public bool IsViewed { get; set; } = false;

        // Has the user saved this recommendation?
        public bool IsSaved { get; set; } = false;

        // User interaction metrics
        public int ViewCount { get; set; } = 0;
        public DateTime? LastViewedDate { get; set; }

        [ForeignKey("UserId")]
        public ApplicationUser? User { get; set; }
    }
}
