@model IEnumerable<ViVu.Models.Accommodation>

@{
    ViewData["Title"] = "Danh sách khách sạn";
}

<div class="container mt-4">
    <div class="section-title" data-aos="fade-up">
        <h2><PERSON><PERSON> sách khách sạn</h2>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4 shadow-sm" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body">
            <form method="get" class="row g-3 modern-form">
                <div class="col-md-3">
                    <label class="form-label">T<PERSON>m kiếm</label>
                    <input type="text" name="searchQuery" class="form-control" placeholder="Tên khách sạn..." value="@ViewBag.SearchQuery">
                </div>
                <div class="col-md-2">
                    <label class="form-label"><PERSON><PERSON><PERSON> điểm</label>
                    <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                        <option value="">Tất cả</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Thành phố</label>
                    <select name="cityId" class="form-select" asp-items="ViewBag.Cities">
                        <option value="">Tất cả</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Giá từ</label>
                    <input type="number" name="minPrice" class="form-control" placeholder="VNĐ" value="@ViewBag.MinPrice">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Giá đến</label>
                    <input type="number" name="maxPrice" class="form-control" placeholder="VNĐ" value="@ViewBag.MaxPrice">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-modern btn-modern-primary w-100">Lọc</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Sort Options -->
    <div class="d-flex justify-content-between mb-4" data-aos="fade-up" data-aos-delay="150">
        <div>
            <span class="me-2">Sắp xếp theo:</span>
            <div class="btn-group">
                <a asp-action="Index" asp-route-sortOrder="price_asc" asp-route-searchQuery="@ViewBag.SearchQuery"
                   asp-route-locationId="@ViewBag.LocationId" asp-route-cityId="@ViewBag.CityId"
                   asp-route-minPrice="@ViewBag.MinPrice" asp-route-maxPrice="@ViewBag.MaxPrice"
                   class="btn btn-modern btn-modern-outline @(ViewBag.CurrentSort == "price_asc" ? "active" : "")">
                    Giá tăng dần
                </a>
                <a asp-action="Index" asp-route-sortOrder="price_desc" asp-route-searchQuery="@ViewBag.SearchQuery"
                   asp-route-locationId="@ViewBag.LocationId" asp-route-cityId="@ViewBag.CityId"
                   asp-route-minPrice="@ViewBag.MinPrice" asp-route-maxPrice="@ViewBag.MaxPrice"
                   class="btn btn-modern btn-modern-outline @(ViewBag.CurrentSort == "price_desc" ? "active" : "")">
                    Giá giảm dần
                </a>
                <a asp-action="Index" asp-route-sortOrder="rating_desc" asp-route-searchQuery="@ViewBag.SearchQuery"
                   asp-route-locationId="@ViewBag.LocationId" asp-route-cityId="@ViewBag.CityId"
                   asp-route-minPrice="@ViewBag.MinPrice" asp-route-maxPrice="@ViewBag.MaxPrice"
                   class="btn btn-modern btn-modern-outline @(ViewBag.CurrentSort == "rating_desc" ? "active" : "")">
                    Đánh giá cao nhất
                </a>
            </div>
        </div>
        @if (User.IsInRole("Admin"))
        {
            <a asp-action="Create" class="btn btn-modern btn-modern-primary">
                <i class="bi bi-plus-circle"></i> Thêm khách sạn mới
            </a>
        }
    </div>

    <!-- Accommodation List -->
    <div class="row">
        @if (Model.Any())
        {
            int index = 0;
            foreach (var accommodation in Model)
            {
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="@(index * 100)">
                    <div class="modern-card h-100">
                        <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default-hotel.jpg" : accommodation.ImageUrl)"
                             class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title mb-0">@accommodation.Name</h5>
                                <div>
                                    @for (int i = 0; i < accommodation.StarRating; i++)
                                    {
                                        <i class="bi bi-star-fill text-warning"></i>
                                    }
                                </div>
                            </div>
                            <p class="text-muted mb-2">
                                <i class="bi bi-geo-alt"></i> @accommodation.Location?.Name, @accommodation.City?.Name
                            </p>
                            <p class="card-text">@(accommodation.Description?.Length > 100 ? accommodation.Description.Substring(0, 100) + "..." : accommodation.Description)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                <a asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-modern btn-modern-outline">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
                index++;
            }
        }
        else
        {
            <div class="col-12 text-center" data-aos="fade-up">
                <p class="alert alert-info">Không tìm thấy khách sạn nào phù hợp với tiêu chí tìm kiếm.</p>
            </div>
        }
    </div>
</div>
