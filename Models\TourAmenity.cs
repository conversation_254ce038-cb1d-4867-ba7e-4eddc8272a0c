﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TourAmenity
    {
        public int Id { get; set; }
        
        public int TourId { get; set; }
        
        public int AmenityId { get; set; }
        
        [ForeignKey("TourId")]
        public Tour Tour { get; set; }
        
        [ForeignKey("AmenityId")]
        public Amenity Amenity { get; set; }
    }
}
