@model ViVu.Models.Panorama360

@{
    ViewData["Title"] = "Chỉnh sửa Du lịch 360°";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chỉnh sửa Du lịch 360°</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý Du lịch 360°</a></li>
        <li class="breadcrumb-item active">Chỉnh sửa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-vr-cardboard me-1"></i>
            Thông tin Panorama 360°
        </div>
        <div class="card-body">
            <form asp-action="Edit" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />
                <input type="hidden" asp-for="ImageUrl" />
                <input type="hidden" asp-for="CreatedAt" />
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Name" class="control-label">Tên</label>
                            <input asp-for="Name" class="form-control" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Type" class="control-label">Loại</label>
                            <select asp-for="Type" class="form-select" asp-items="ViewBag.Types" onchange="showRelatedDropdown()">
                            </select>
                            <span asp-validation-for="Type" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6" id="tourDropdown">
                        <div class="form-group">
                            <label asp-for="TourId" class="control-label">Tour</label>
                            <select asp-for="TourId" class="form-select" asp-items="ViewBag.Tours">
                                <option value="">-- Chọn tour --</option>
                            </select>
                            <span asp-validation-for="TourId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6" id="locationDropdown">
                        <div class="form-group">
                            <label asp-for="LocationId" class="control-label">Địa điểm</label>
                            <select asp-for="LocationId" class="form-select" asp-items="ViewBag.Locations">
                                <option value="">-- Chọn địa điểm --</option>
                            </select>
                            <span asp-validation-for="LocationId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="DisplayOrder" class="control-label">Thứ tự hiển thị</label>
                            <input asp-for="DisplayOrder" class="form-control" />
                            <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>
                
                <div class="form-group mb-3">
                    <label asp-for="HotspotData" class="control-label">Dữ liệu Hotspot (JSON)</label>
                    <textarea asp-for="HotspotData" class="form-control" rows="5" placeholder='[{"x": 1000, "y": 0, "z": -1000, "text": "Điểm thông tin", "linkTo": "/Tour/Details/1"}]'></textarea>
                    <span asp-validation-for="HotspotData" class="text-danger"></span>
                    <small class="form-text text-muted">Định dạng JSON mảng các điểm hotspot với tọa độ x, y, z, text và linkTo (tùy chọn)</small>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Hình ảnh hiện tại</label>
                            <div>
                                <img src="@Model.ImageUrl" alt="@Model.Name" style="max-width: 100%; height: 200px; object-fit: cover;" class="img-thumbnail" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Thay đổi hình ảnh</label>
                            <input type="file" name="imageFile" class="form-control" accept="image/*" />
                            <small class="form-text text-muted">Hình ảnh panorama 360° dạng equirectangular (tỷ lệ 2:1)</small>
                        </div>
                        <div class="form-group form-check mt-3">
                            <input asp-for="IsActive" class="form-check-input" />
                            <label asp-for="IsActive" class="form-check-label">Kích hoạt</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function showRelatedDropdown() {
            const type = document.getElementById('Type').value;
            const tourDropdown = document.getElementById('tourDropdown');
            const locationDropdown = document.getElementById('locationDropdown');
            
            if (type === 'Tour') {
                tourDropdown.style.display = 'block';
                locationDropdown.style.display = 'none';
                document.getElementById('LocationId').value = '';
            } else if (type === 'Location') {
                tourDropdown.style.display = 'none';
                locationDropdown.style.display = 'block';
                document.getElementById('TourId').value = '';
            }
        }
        
        // Gọi hàm khi trang được tải
        document.addEventListener('DOMContentLoaded', function() {
            showRelatedDropdown();
        });
    </script>
}
