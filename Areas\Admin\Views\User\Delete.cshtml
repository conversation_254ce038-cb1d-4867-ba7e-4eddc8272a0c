@model ViVu.Areas.Admin.Controllers.UserViewModel
@{
    ViewData["Title"] = "Xóa người dùng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">X<PERSON>a người dùng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Người dùng</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-times me-1"></i>
            Xác nhận xóa người dùng
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo!</h5>
                <p>Bạn có chắc chắn muốn xóa người dùng này không? Hành động này không thể hoàn tác.</p>
            </div>
            
            <div asp-validation-summary="All" class="text-danger"></div>
            
            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Họ tên:</dt>
                        <dd class="col-sm-8">@Model.User.FullName</dd>
                        
                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.User.Email</dd>
                        
                        <dt class="col-sm-4">Điện thoại:</dt>
                        <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.User.PhoneNumber) ? "Chưa cập nhật" : Model.User.PhoneNumber)</dd>
                        
                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.User.Address) ? "Chưa cập nhật" : Model.User.Address)</dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Vai trò:</dt>
                        <dd class="col-sm-8">
                            @foreach (var role in Model.Roles)
                            {
                                <span class="badge bg-primary me-1">@role</span>
                            }
                        </dd>
                        
                        <dt class="col-sm-4">Ngày tạo:</dt>
                        <dd class="col-sm-8">@Model.User.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                </div>
            </div>
            
            <form asp-action="DeleteConfirmed" method="post">
                <input type="hidden" name="id" value="@Model.User.Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
