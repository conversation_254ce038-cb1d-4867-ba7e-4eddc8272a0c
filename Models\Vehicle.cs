﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class Vehicle
    {
        public int Id { get; set; }

        [Required, StringLength(100)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string Details { get; set; }

        public string? ImageUrl { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PricePerDay { get; set; }

        [NotMapped]
        public decimal RentalPrice => PricePerDay;

        [Required]
        public VehicleType Type { get; set; }

        [Required]
        public string LicensePlate { get; set; }

        [Required]
        [Range(1, 50)]
        public int Capacity { get; set; }

        public bool IsFeatured { get; set; } = false;

        public bool IsAvailable { get; set; } = true;

        public int LocationId { get; set; }

        public int CityId { get; set; }

        [ForeignKey("LocationId")]
        public Location? Location { get; set; }

        [ForeignKey("CityId")]
        public City? City { get; set; }

        public List<VehicleImage>? Images { get; set; }

        public List<Review>? Reviews { get; set; }

        public List<VehicleBookingDetail>? VehicleBookingDetails { get; set; }

        [NotMapped]
        public double AverageRating => Reviews?.Any() == true ? Reviews.Average(r => r.Rating) : 0;
    }

    public enum VehicleType
    {
        Car,
        Motorbike,
        Bicycle,
        Boat,
        Other
    }
}
