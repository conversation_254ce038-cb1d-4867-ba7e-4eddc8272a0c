@model IEnumerable<ViVu.Models.Panorama360>

@{
    ViewData["Title"] = "Du lịch 360° - " + ViewBag.Tour.Name;
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="section-title" data-aos="fade-up">
                <h2>Du lịch 360° - @ViewBag.Tour.Name</h2>
            </div>

            <div class="card mb-4 shadow-sm" data-aos="fade-up" data-aos-delay="100">
                <div class="card-body">
                    <p>@ViewBag.Tour.Description</p>
                    <p>
                        <a href="@Url.Action("Details", "Tour", new { id = ViewBag.Tour.Id })" class="btn btn-modern btn-modern-primary">
                            <i class="fas fa-arrow-left"></i> Quay lại tour
                        </a>
                        <a href="@Url.Action("Guide", "Panorama360")" class="btn btn-modern btn-modern-outline">
                            <i class="fas fa-question-circle"></i> Hướng dẫn sử dụng
                        </a>
                    </p>
                </div>
            </div>

            @if (!Model.Any())
            {
                <div class="alert alert-info" data-aos="fade-up" data-aos-delay="150">
                    Hiện chưa có dữ liệu panorama 360° cho tour này. Vui lòng quay lại sau.
                </div>
            }
            else
            {
                <div class="row">
                    @{
                        int index = 0;
                    }
                    @foreach (var item in Model.Where(p => p.IsActive))
                    {
                        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="@(150 + (index * 100))">
                            <div class="modern-card h-100">
                                <div class="panorama-thumbnail" style="background-image: url('@item.ImageUrl');">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="panorama-link">
                                        <div class="panorama-overlay">
                                            <i class="fas fa-vr-cardboard fa-3x"></i>
                                            <span>Xem 360°</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">@item.Name</h5>
                                    <p class="card-text">@(item.Description.Length > 100 ? item.Description.Substring(0, 100) + "..." : item.Description)</p>
                                </div>
                                <div class="card-footer bg-white border-top-0 text-end">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="btn btn-modern btn-modern-outline">
                                        <i class="fas fa-vr-cardboard"></i> Xem 360°
                                    </a>
                                </div>
                            </div>
                        </div>
                        index++;
                    }
                </div>
            }
        </div>
    </div>
</div>
