﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ViVu.Migrations
{
    /// <inheritdoc />
    public partial class AddAIRecommendationModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SearchHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: true),
                    SearchDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SearchTerm = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LocationId = table.Column<int>(type: "int", nullable: true),
                    CityId = table.Column<int>(type: "int", nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MinPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaxPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StarRating = table.Column<int>(type: "int", nullable: true),
                    SearchType = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SearchHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SearchHistories_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SearchHistories_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SearchHistories_Locations_LocationId",
                        column: x => x.LocationId,
                        principalTable: "Locations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TravelRecommendations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItineraryDetails = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RecommendationType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    EstimatedMinBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    EstimatedMaxBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RecommendedDuration = table.Column<int>(type: "int", nullable: false),
                    RelatedTourIds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelatedAccommodationIds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelatedServiceIds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelatedLocationIds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsViewed = table.Column<bool>(type: "bit", nullable: false),
                    IsSaved = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TravelRecommendations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TravelRecommendations_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserPreferences",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    PrefersTours = table.Column<bool>(type: "bit", nullable: false),
                    PrefersIndependentTravel = table.Column<bool>(type: "bit", nullable: false),
                    MinBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaxBudget = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MinDuration = table.Column<int>(type: "int", nullable: true),
                    MaxDuration = table.Column<int>(type: "int", nullable: true),
                    PreferredLocationIds = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PrefersNature = table.Column<bool>(type: "bit", nullable: false),
                    PrefersHistory = table.Column<bool>(type: "bit", nullable: false),
                    PrefersFood = table.Column<bool>(type: "bit", nullable: false),
                    PrefersAdventure = table.Column<bool>(type: "bit", nullable: false),
                    PrefersRelaxation = table.Column<bool>(type: "bit", nullable: false),
                    PreferredStarRating = table.Column<int>(type: "int", nullable: true),
                    LastUpdated = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPreferences", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserPreferences_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SearchHistories_CityId",
                table: "SearchHistories",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_SearchHistories_LocationId",
                table: "SearchHistories",
                column: "LocationId");

            migrationBuilder.CreateIndex(
                name: "IX_SearchHistories_UserId",
                table: "SearchHistories",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TravelRecommendations_UserId",
                table: "TravelRecommendations",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_UserId",
                table: "UserPreferences",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SearchHistories");

            migrationBuilder.DropTable(
                name: "TravelRecommendations");

            migrationBuilder.DropTable(
                name: "UserPreferences");
        }
    }
}
