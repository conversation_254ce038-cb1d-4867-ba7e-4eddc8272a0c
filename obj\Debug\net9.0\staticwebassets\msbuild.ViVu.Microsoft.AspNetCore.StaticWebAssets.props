﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/admin.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>dxljrfpbru</Fingerprint>
      <Integrity>wdwZ+GOJP2YeOg8uQtRQC6Gg34b2nKGZEBTuvsyvdKg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\ai-loading.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/ai-loading.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3p64rit9j9</Fingerprint>
      <Integrity>541SJlDCkg8GB4R39QRkikBoiCnT2cUuNYe/jJuXFOI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\ai-loading.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/animations.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>7j8yb0ghog</Fingerprint>
      <Integrity>TXWysZOFWRXwb2POsHFYrW6rwbe0H1WXcZ4Zo2laSTA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\pannellum.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/pannellum.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jg0bvj93ve</Fingerprint>
      <Integrity>7S16LgMnZW3JpyAl7EjLBx+c2+Pmgtom5zrTG6ZL7vY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\pannellum.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\panorama360.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/panorama360.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5zp14ypy8s</Fingerprint>
      <Integrity>wZUmdIyUF4piY5XSNBkbbYqqjWc0TU73WasEc6F/wJE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\panorama360.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/site.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4i9j4omsgm</Fingerprint>
      <Integrity>JAUK50ZnGZLc35OLCvoWSpgEIvIPgsaeEALAtyeQsao=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>css/style.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rha8patjeh</Fingerprint>
      <Integrity>H5imjUHfhR85rRbyk+K3gX9c0K4nvvd5y8ygZk3QJ9I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>favicon.ico</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>61n19gt1b8</Fingerprint>
      <Integrity>Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\480795064_663735616004425_8146582297492019421_n.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/480795064_663735616004425_8146582297492019421_n.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>32ad5966ih</Fingerprint>
      <Integrity>1cJATDRATWirJaEJVjYPBdGd1Emm2rIBH4krWdZZGj0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\480795064_663735616004425_8146582297492019421_n.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>hm9jdghkkh</Fingerprint>
      <Integrity>eTQamAqv2y0eBBROuUYQr9WU48w2oFaA7HiwCttkpvI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ncmrcvan7i</Fingerprint>
      <Integrity>53F+Sx+59S8LwfoeBFkSmNIAotOyDAy4uDGiVckfqek=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>7vj59tyyd3</Fingerprint>
      <Integrity>09CMt4VCgKtIwnR6nIrOMFMvy0r5D9H9CqBR9ER9eMc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>c24wsd0dd1</Fingerprint>
      <Integrity>lAyJlXaSktMicL/LfJ03cFSso7zDu1b78Dw6qjuoUs4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_garden.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fn1nh4ztjd</Fingerprint>
      <Integrity>GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_garden.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_pool.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/amenities/hotel_ben-tre-riverside_pool.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kiiyxi4psp</Fingerprint>
      <Integrity>PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_pool.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kfeholhxuy</Fingerprint>
      <Integrity>UD/fd/VQggkE1jVbmcqpqOOeY4WCspIxUzxrwdkNKy8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom2_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/bedroom2_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vu9tnbmojk</Fingerprint>
      <Integrity>J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh+T5I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom2_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/bedroom_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d0c046feaf</Fingerprint>
      <Integrity>zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>i2rgeyjk10</Fingerprint>
      <Integrity>9kwXP0Dkqji2uxHmF7jhYGt9xh58A3C23Mq0WEs5dtQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\deluxe_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/deluxe_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>mcpscl1u7j</Fingerprint>
      <Integrity>Vv+UbZ52qPIxZ6VukXYVwpHHJNqg8Nebp6i1lEUAUNo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\deluxe_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\DIAMOND STARS BEN TRE HOTEL.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/DIAMOND STARS BEN TRE HOTEL.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2ovkft7spb</Fingerprint>
      <Integrity>UlAB/FuQOFALJgIVERTdXwi0eoGGH7X4v5GXbaW/PeM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\DIAMOND STARS BEN TRE HOTEL.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\elite-suite-room_diamon hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/elite-suite-room_diamon hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zi2waiznw4</Fingerprint>
      <Integrity>y5atjFdviTFKCLtP70opcOlXSx4mo/DadxrA27Jx26E=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\elite-suite-room_diamon hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3ecs43jd2n</Fingerprint>
      <Integrity>uw2QDjlgBsIeJ37knnSQuRwjTpzr8iXso+ccdYEYQkw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\front-hotel_Viet Uc Hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/front-hotel_Viet Uc Hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lbfybsxo9f</Fingerprint>
      <Integrity>75LX3UnoIvmGcSvL4Y8C80ToDLFU437minOyZzb7FHw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\front-hotel_Viet Uc Hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\garden_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/garden_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fn1nh4ztjd</Fingerprint>
      <Integrity>GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\garden_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\kham-pha-phong-suite_Viet Uc Hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/kham-pha-phong-suite_Viet Uc Hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ddj172fscs</Fingerprint>
      <Integrity>UbfWnu3shKp++5+fm5LHlJQPMGO3VRFL/nJR+xNSwzM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\kham-pha-phong-suite_Viet Uc Hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\living-room_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/living-room_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>sca8becvv6</Fingerprint>
      <Integrity>bElKHSKssKFWDy/aoJ1kmf9iN4DIXg/1iOjU3itWYTc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\living-room_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/oasis-hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4f7qrtlctl</Fingerprint>
      <Integrity>8HgsR8MSSCOudHZUQMo4R17QO/RAh48RSBEh1U++S5g=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel-seen-from.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/oasis-hotel-seen-from.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kd4hduw3cy</Fingerprint>
      <Integrity>mKq2f1SYaqWm0LfEOB3bB5YU5FCewHorKysBumHv8TY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel-seen-from.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kiiyxi4psp</Fingerprint>
      <Integrity>PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\photo4jpg_Viet Uc Hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/photo4jpg_Viet Uc Hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>41oiskb6dv</Fingerprint>
      <Integrity>KOYDt4tB0gMEW+bAEcp0psGAg9w2KaBMr/qY7FolIXE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\photo4jpg_Viet Uc Hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\presidential-suite-room_diamonhotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/presidential-suite-room_diamonhotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5w0zt4jh66</Fingerprint>
      <Integrity>u9N2J8cJ4TD3qTk5OldFZ9ZioM7HM/wuGY5uou+tp60=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\presidential-suite-room_diamonhotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\prime-suite-room_diamon hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/prime-suite-room_diamon hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zgcvtg552r</Fingerprint>
      <Integrity>412IdcLFIC1TwLQtYT8n2EQ+ZyoT6gZLKNpSrpt8MJg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\prime-suite-room_diamon hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\reception-2_Viet Uc Hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/reception-2_Viet Uc Hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>l5h99w04tm</Fingerprint>
      <Integrity>GegPBxgTSssx0BnnumWdUTqg1UuoPzFJeJg0d+YTm9E=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\reception-2_Viet Uc Hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_deluxe_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vu9tnbmojk</Fingerprint>
      <Integrity>J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh+T5I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_deluxe_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_standard_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d0c046feaf</Fingerprint>
      <Integrity>zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_standard_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\signature-deluxe-king_diamon hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/signature-deluxe-king_diamon hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ef7gz65mg4</Fingerprint>
      <Integrity>mjQRP9N+djNu42fVLI+iV/gtAkRU8n4Px9P+XT8LLjU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\signature-deluxe-king_diamon hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\suite_Ben Tre Riverside Resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/suite_Ben Tre Riverside Resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>1oxjvtgbjd</Fingerprint>
      <Integrity>z/s9XYe6MdGovekSM7kp6sp02aYWBdvZsSbM8qbbsGo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\suite_Ben Tre Riverside Resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\Viet Uc Hotel1.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/Viet Uc Hotel1.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jhsfwb17r4</Fingerprint>
      <Integrity>CcbdC5vql/bm8pkWTqAM+uvowPcozRdSO+QbcF9OhNk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\Viet Uc Hotel1.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\view-from-hotel-bentre riverside resort.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/accommodations/view-from-hotel-bentre riverside resort.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ba42dmjnf0</Fingerprint>
      <Integrity>M7fd+F6s5ywAdsu7WFozq5dED8nch4fAdFg9sz+EtmI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\view-from-hotel-bentre riverside resort.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\ai-recommendation.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/ai-recommendation.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lw6nk6b2t0</Fingerprint>
      <Integrity>tR5QSIYqNGMhKbMr+NavS3orLZr3YCia6W0G8Uv1TQA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\ai-recommendation.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\anh-lang-hoa-cho-lach-ben-tre_022739971.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/anh-lang-hoa-cho-lach-ben-tre_022739971.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>dyphqtxwt0</Fingerprint>
      <Integrity>W2S+zGOtivyR2EHs+KqoLB3/qjW8f70e0tB4+rzSxD4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\anh-lang-hoa-cho-lach-ben-tre_022739971.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/banner_home_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lw6nk6b2t0</Fingerprint>
      <Integrity>tR5QSIYqNGMhKbMr+NavS3orLZr3YCia6W0G8Uv1TQA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_02.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/banner_home_02.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lgwnj7f1qr</Fingerprint>
      <Integrity>FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_02.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\bến-tre.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/bến-tre.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>80cil27d7z</Fingerprint>
      <Integrity>SDryTuy3zd0/R12V7wEFO4vVQlxjVXFJy8p+2jIKN7c=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\bến-tre.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lw6nk6b2t0</Fingerprint>
      <Integrity>tR5QSIYqNGMhKbMr+NavS3orLZr3YCia6W0G8Uv1TQA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lgwnj7f1qr</Fingerprint>
      <Integrity>FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>xr1lgk8s6k</Fingerprint>
      <Integrity>w6PkXnsxGdCAY+kkR5CH5DTLT16zTO/D2cvRNwW8lQs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README.txt'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/README.txt</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47o5cqoj5o</Fingerprint>
      <Integrity>wH1JrHrCZIUMo3JmqU2IcTjUA4KWmDfS+TkJ86hXK54=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README.txt'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README_AI.txt'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/banners/README_AI.txt</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>npgoq9ygt0</Fingerprint>
      <Integrity>m6gswSODiVwiD0T6dOKfftj/qC4VeCnOWUePiH4zCzw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README_AI.txt'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\cuisine\amthucbentre.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/cuisine/amthucbentre.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zfjffteuy9</Fingerprint>
      <Integrity>L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\cuisine\amthucbentre.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\culture\doncataitur.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/culture/doncataitur.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zfjffteuy9</Fingerprint>
      <Integrity>L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\culture\doncataitur.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-activity.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/default/default-activity.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>brjrgch06q</Fingerprint>
      <Integrity>x2xrvfP4KRVMnAOFiSnEEmrsw3z5BSB+0ovAy/PVX8w=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-activity.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-hotel.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/default/default-hotel.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ya2z8zzef9</Fingerprint>
      <Integrity>luU7eMunRrfW1xFLdskKQjlQCMgqVaZ+BcZ8LFN71xc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-hotel.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-tour.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/default/default-tour.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rhlt30nd2j</Fingerprint>
      <Integrity>8Vr8S1Cqb/yn46fy4tPr9vqxSGnseYdyZKxyl8/qU18=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-tour.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\conphung.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/conphung.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zfjffteuy9</Fingerprint>
      <Integrity>L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\conphung.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\crafts\craft_lang-keo-dua_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/crafts/craft_lang-keo-dua_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>l4orteeb3m</Fingerprint>
      <Integrity>5Ydq9eSRsaknuNvjFimGRdpXhq+AlwuRN2Ysd6PsMBo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\crafts\craft_lang-keo-dua_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\craft_lang-keo-dua_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/craft_lang-keo-dua_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>l4orteeb3m</Fingerprint>
      <Integrity>5Ydq9eSRsaknuNvjFimGRdpXhq+AlwuRN2Ysd6PsMBo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\craft_lang-keo-dua_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\cuisine\food_banh-trang-dua_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/cuisine/food_banh-trang-dua_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>68m6pn2o1o</Fingerprint>
      <Integrity>+rTgG2KRiZm5j5tqiLPmkD2+4w19dXs917yApQi1qzQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\cuisine\food_banh-trang-dua_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\culaothoison.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/culaothoison.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zfjffteuy9</Fingerprint>
      <Integrity>L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\culaothoison.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\dest_con-phung_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/dest_con-phung_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fx7eukk01c</Fingerprint>
      <Integrity>XfKRl+h1hbiKdF6ZX5SgBxazH4VVkLydntalU/t7Ob4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\dest_con-phung_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\food_banh-trang-dua_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/food_banh-trang-dua_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>68m6pn2o1o</Fingerprint>
      <Integrity>+rTgG2KRiZm5j5tqiLPmkD2+4w19dXs917yApQi1qzQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\food_banh-trang-dua_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\langnghedua.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/langnghedua.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zfjffteuy9</Fingerprint>
      <Integrity>L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\langnghedua.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\README.html'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/README.html</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>h4lhfj2oe8</Fingerprint>
      <Integrity>z78W5fun6D3WeX/qVNwTEPw8RWg/oirm5V/XONwrZOg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\README.html'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\vuoncaimon.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/destinations/vuoncaimon.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>g9pml4w7tk</Fingerprint>
      <Integrity>nFF4rlb9LC+t+VebxUo6td9VHH5V4NUcmZ8DXi3EedI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\vuoncaimon.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\download.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/download.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>29t50sped2</Fingerprint>
      <Integrity>ViOj06v3M9wV/zLZ3EjrkZ/p6qH/6+zTQSeobRjldtU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\download.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\experiences\exp_lang-hoa-cho-lach_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/experiences/exp_lang-hoa-cho-lach_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>dyphqtxwt0</Fingerprint>
      <Integrity>W2S+zGOtivyR2EHs+KqoLB3/qjW8f70e0tB4+rzSxD4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\experiences\exp_lang-hoa-cho-lach_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\image1.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/image1.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>874hy2cbfn</Fingerprint>
      <Integrity>fKZqCHvJO3Lc3YTDoNQdKU5W2JP8H8UC/40jQa1xCnw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\image1.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\Laptop.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/Laptop.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2y66sg7zog</Fingerprint>
      <Integrity>Votf537DqzWjkDhYCvojc3rhRDKcYuCOLGNVMFTRj28=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\Laptop.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ii0mxv9slh</Fingerprint>
      <Integrity>GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>gehn6ps2p2</Fingerprint>
      <Integrity>KD5oU5YpiYC/KrqOwTLSIhR22OFfgkWEAtUQIpnp/Wo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>seh97pt6zn</Fingerprint>
      <Integrity>nISilFxEHsLybRq4I7WF0LDuIj3od0kSLTqnnZRrrL4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ii0mxv9slh</Fingerprint>
      <Integrity>GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vee31lktom</Fingerprint>
      <Integrity>twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4kuqo9z1lu</Fingerprint>
      <Integrity>zJb7FheE2vOkqBArT6LA7eZNrsTXH297lbNkK8bFa4k=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>s8deirdmoq</Fingerprint>
      <Integrity>dNeLBuJwuDfkgpgW075u+ccX7dzNWEpKm/IjLlrRpGE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qr6ycrn3gi</Fingerprint>
      <Integrity>OqGnZmR5ceX3jgFbD2mUG4R2k/rJtVJAKhLqKZQvkuo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\92f54106-9211-4d39-aa73-ea16167c52c2_R.png'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/92f54106-9211-4d39-aa73-ea16167c52c2_R.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jaom6mqkix</Fingerprint>
      <Integrity>hV3xHVzONE7MMWHOeVo6IAeVaRBV9LYMalzSte9Lc3Y=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\92f54106-9211-4d39-aa73-ea16167c52c2_R.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ii0mxv9slh</Fingerprint>
      <Integrity>GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/locations/a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>xkzopq8y0y</Fingerprint>
      <Integrity>ISt8Z6rDHHtjv/IInlAWWR50lX2Ow6972iUEPP2V6v4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\msi.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/msi.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>xgtz2xutzj</Fingerprint>
      <Integrity>8QWcqS/HPfJBBVVYKjLelEVQJiqbEvytWPkUl8A3N1I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\msi.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\20839908-55fe-4bde-a806-267056a30d42.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/panoramas/20839908-55fe-4bde-a806-267056a30d42.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>061ohg3dcj</Fingerprint>
      <Integrity>GIPCwPA4fXDTxqT1pzdsyVaqTkhvej4wCwna+PCpR04=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\20839908-55fe-4bde-a806-267056a30d42.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\4c2df1da-5385-4201-af7c-dd42540b3a07.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/panoramas/4c2df1da-5385-4201-af7c-dd42540b3a07.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>69jfowzfhs</Fingerprint>
      <Integrity>xmiDnWEIW2nZVafbVRS8KUrqiW0PdAPfLByBIbyDse4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\4c2df1da-5385-4201-af7c-dd42540b3a07.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/panoramas/6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pvxwnlymcu</Fingerprint>
      <Integrity>hf5B43Ym7LRV+yC+RUrU0pDeACM8cMpWHxL7WEgQb4g=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\c83348e1-5da8-4856-9657-6872e8210b4e.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/panoramas/c83348e1-5da8-4856-9657-6872e8210b4e.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>9vxkbw864s</Fingerprint>
      <Integrity>i6yQNCM8AKWDhC24QNcGaWc+0MbKSxlhlWYQV/K4qCI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\c83348e1-5da8-4856-9657-6872e8210b4e.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\panorama-banner.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/panoramas/panorama-banner.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pvxwnlymcu</Fingerprint>
      <Integrity>hf5B43Ym7LRV+yC+RUrU0pDeACM8cMpWHxL7WEgQb4g=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\panorama-banner.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vjdf3zcc1l</Fingerprint>
      <Integrity>pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\34c294a6-de82-4bec-904c-ac56320e62d3.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/34c294a6-de82-4bec-904c-ac56320e62d3.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r53tyor372</Fingerprint>
      <Integrity>P+fLMkPe/6dxE9+S+JdSgwZALIaAiXDV+1mfrBBJ1vY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\34c294a6-de82-4bec-904c-ac56320e62d3.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vjdf3zcc1l</Fingerprint>
      <Integrity>pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\b9743ea5-5728-468f-8fb2-600321c6f203.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/b9743ea5-5728-468f-8fb2-600321c6f203.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vjdf3zcc1l</Fingerprint>
      <Integrity>pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\b9743ea5-5728-468f-8fb2-600321c6f203.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ujwpgnn702</Fingerprint>
      <Integrity>Cm3nYWnZQ7EldsxQfA6CdA6nYLHCXD/ib5vD31B3eQ0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tonji5updp</Fingerprint>
      <Integrity>raSZQ4++od5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\fe561fa6-3db1-4eca-9096-e16971e24fec.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/services/fe561fa6-3db1-4eca-9096-e16971e24fec.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>oc5edvure5</Fingerprint>
      <Integrity>mLvXp32i5LhQttOD5VwPz+1S8mtBj7AcwoGa4eidaec=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\fe561fa6-3db1-4eca-9096-e16971e24fec.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5t177jw6gk</Fingerprint>
      <Integrity>F+jFMQywCxTdHTWOzXHqlHYJfR3m1O/JWnTUcXeT9RU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tonji5updp</Fingerprint>
      <Integrity>raSZQ4++od5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\4fa99da7-7971-4e69-87bf-05c7977224b0.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/4fa99da7-7971-4e69-87bf-05c7977224b0.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>f4f5a2wze7</Fingerprint>
      <Integrity>MdJnkm10dSTVtZooPK7ZZ+82HywTCjrCuD/EuDPgQxU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\4fa99da7-7971-4e69-87bf-05c7977224b0.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\91e28151-4091-444b-b82b-7e1fc90f6dc8.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/91e28151-4091-444b-b82b-7e1fc90f6dc8.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ym8fgfuch8</Fingerprint>
      <Integrity>hppLxTgCgpnL8HkuAS0NdyxKTzaCUDLjsOYPskGmoTU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\91e28151-4091-444b-b82b-7e1fc90f6dc8.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vee31lktom</Fingerprint>
      <Integrity>twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\anh-ben-tre-dep-binh-di_022739098.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/anh-ben-tre-dep-binh-di_022739098.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>sgk5wnnwwu</Fingerprint>
      <Integrity>3EIXHh3uswgGf9S3z0/Vz289sv+ftDvteB1C9RBMIKY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\anh-ben-tre-dep-binh-di_022739098.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Authentic Discover the Mekong Delta's Charms _01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>zkphb1h05s</Fingerprint>
      <Integrity>wzoGCxoHCzpKt9OZ85gK0vf7l78nMsLftb525udpA94=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _09.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Authentic Discover the Mekong Delta's Charms _09.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4ac2glevd9</Fingerprint>
      <Integrity>VDG3bjN5H27vT248AzMxEkLis+pl7YzxLdZh8UHwAWs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _09.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _92.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Authentic Discover the Mekong Delta's Charms _92.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>uiqcsdw1xh</Fingerprint>
      <Integrity>ur6ZzOK40pyRgwJRjcYMop2knD/flhzntC5k/VP6yPM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _92.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _a6.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Authentic Discover the Mekong Delta's Charms _a6.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n1me3r0vj8</Fingerprint>
      <Integrity>G1YzfKsCsVsaFoXn7O6bQmVyudLeeVmMIhmOweTqeLA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _a6.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\banners\tour_kham-pha-ben-tre_banner.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/banners/tour_kham-pha-ben-tre_banner.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>sgk5wnnwwu</Fingerprint>
      <Integrity>3EIXHh3uswgGf9S3z0/Vz289sv+ftDvteB1C9RBMIKY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\banners\tour_kham-pha-ben-tre_banner.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rq26cv2yqe</Fingerprint>
      <Integrity>Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r4q3mv2yy9</Fingerprint>
      <Integrity>C/lYF8T1crYghEV+/Zyh+16pTwC+OVG35mufLQOUgTg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rqw29wzbrh</Fingerprint>
      <Integrity>vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91+9M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ruq0evhfkn</Fingerprint>
      <Integrity>8wEHM+p4qpswg6jHFbiikYbPM4MQ4TNAcwAd471OKNE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n5k33b75u1</Fingerprint>
      <Integrity>Ndh4/Ut0Q6S3hvVjK9npogwFaDkI4Vihx3c4Xu5jbYc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>uzdq4eygzy</Fingerprint>
      <Integrity>ppqSrwVqC1iMtJH6fauzAm8ek97v6dSdsuG1draQ4GQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/details/tour_ben-tre-half-day_detail_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r4q3mv2yy9</Fingerprint>
      <Integrity>C/lYF8T1crYghEV+/Zyh+16pTwC+OVG35mufLQOUgTg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_02.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/details/tour_ben-tre-half-day_detail_02.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rqw29wzbrh</Fingerprint>
      <Integrity>vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91+9M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_02.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Mekong Delta Small-Group Tour.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>w38givrlmx</Fingerprint>
      <Integrity>9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour2.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Mekong Delta Small-Group Tour2.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>om2dn28cjz</Fingerprint>
      <Integrity>cHJBN/W/EiLli+4ifMrkIcIBBs+5srTMZS491Vo2txQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour2.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour_a1.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/Mekong Delta Small-Group Tour_a1.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>hi4qg12pv3</Fingerprint>
      <Integrity>p1J7Zjc296LSHUepeJk2HrMcm9qUD+utpWPW+Uy2sFU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour_a1.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\R.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/R.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>0gy8pipkc5</Fingerprint>
      <Integrity>YDG8i1JDVPUN6oXrlaHBR4A1ufoidIAG+J037mkFR/w=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\R.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\README.txt'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/README.txt</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>on25or1ax7</Fingerprint>
      <Integrity>TG97ZpeVyYXXUUSe+vlckUry/GxlZ0aBZHUOxZX+Nmg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\README.txt'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_ben-tre-half-day_thumb_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/tour_ben-tre-half-day_thumb_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rq26cv2yqe</Fingerprint>
      <Integrity>Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_ben-tre-half-day_thumb_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_mekong-delta-group_thumb_01.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/tours/tour_mekong-delta-group_thumb_01.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>w38givrlmx</Fingerprint>
      <Integrity>9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_mekong-delta-group_thumb_01.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\03c0af66-a785-459b-a4da-3823e3d2c50a.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/03c0af66-a785-459b-a4da-3823e3d2c50a.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>9o8vhjcf54</Fingerprint>
      <Integrity>V2XcApsRMwmOpdZL0CTAZT8HHitVn2Nj14t9J8bBOAE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\03c0af66-a785-459b-a4da-3823e3d2c50a.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>uscgjnd228</Fingerprint>
      <Integrity>Mlz27GbK5ceXn/uDVBhaY7uDMAAYcg2wtqB0pTWYqcM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\11919ea3-b66c-45a0-a15b-b61391605099.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/11919ea3-b66c-45a0-a15b-b61391605099.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>th01zfywpn</Fingerprint>
      <Integrity>P4iRFksoHQKEVgTfmdU2RFvoXa/5TvXtPcI3x9WU2Tk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\11919ea3-b66c-45a0-a15b-b61391605099.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>o2bdksavzm</Fingerprint>
      <Integrity>sKLgnwt7oabio3V7OR7q4Rpa+fdE2nNh75j7vJqmjBg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tkhlzxatyc</Fingerprint>
      <Integrity>w6yqs3faQ/a7H5BjgYmRD8Dy98RPsyE5O5OAAsGMRlo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>duzffxyobf</Fingerprint>
      <Integrity>y+nHrUXx42mJla77VYoWkbeJ0KdmfMyr1xbj36jWXYQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>images/vehicles/c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tonji5updp</Fingerprint>
      <Integrity>raSZQ4++od5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\ai-loading.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/ai-loading.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4ei8dbqzam</Fingerprint>
      <Integrity>HKi09+HafG9llcjyHn+UIQUn9g3Bh+vcsXQ0TRInvvo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\ai-loading.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-price-validation.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/disable-price-validation.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>h1w5hvpvrt</Fingerprint>
      <Integrity>dQ6jtBm41jM+bu3lA3KB0pSKIkhlCIn+FnnMjiVGxVg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-price-validation.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\googleMapsConfig.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/googleMapsConfig.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>h2cdso25g9</Fingerprint>
      <Integrity>pyWAbKv4FgSS8A66q1Sn0/vCzFi/L4AfHHubo9fp/hk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\googleMapsConfig.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\pannellum.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/pannellum.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>k35ysw6w1t</Fingerprint>
      <Integrity>UMrBqZJuye13N2eIjAWEBoc2/wtmiWRr9AsuHHiRFjI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\pannellum.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\panolens.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/panolens.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fq7ex2r51z</Fingerprint>
      <Integrity>pxcYzc8HLdwRd6Ad93CHx9yoYesWdA4tedW3EEjQQzQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\panolens.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\recommendation.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/recommendation.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d8ki4dutwx</Fingerprint>
      <Integrity>7S6ToEJIDbYXFqXZJhU6QZhpNmsoTJJwl4MHEsm2ZRk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\recommendation.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/site.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tch3nzi1fh</Fingerprint>
      <Integrity>nb2EmcdMrZlUrvDg0fQdf5OUS9QqOtoJwYRQZYy3wH0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\three.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>js/three.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5bugo3o98h</Fingerprint>
      <Integrity>ccHYXTeDFGJbExuZVPQbShyOsUD/Kueaw8GtaazfZX0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\three.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>s35ty4nyc5</Fingerprint>
      <Integrity>GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>bqjiyaj88i</Fingerprint>
      <Integrity>Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>c2jlpeoesf</Fingerprint>
      <Integrity>xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>erw9l3u2r3</Fingerprint>
      <Integrity>5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>aexeepp0ev</Fingerprint>
      <Integrity>kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.rtl.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d7shbmvgxk</Fingerprint>
      <Integrity>CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ausgxo2sd3</Fingerprint>
      <Integrity>/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>k8d9w2qqmf</Fingerprint>
      <Integrity>vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>cosvhxvwiu</Fingerprint>
      <Integrity>7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ub07r2b239</Fingerprint>
      <Integrity>lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fvhpjtyr6v</Fingerprint>
      <Integrity>RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>b7pk76d08c</Fingerprint>
      <Integrity>l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>fsbi9cje9m</Fingerprint>
      <Integrity>0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.rtl.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rzd6atqjts</Fingerprint>
      <Integrity>V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ee0r1s7dh0</Fingerprint>
      <Integrity>OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>dxx9fxp4il</Fingerprint>
      <Integrity>/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jd9uben2k1</Fingerprint>
      <Integrity>910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>khv3u5hwcm</Fingerprint>
      <Integrity>2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r4e9w2rdcm</Fingerprint>
      <Integrity>Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lcd1t2u6c8</Fingerprint>
      <Integrity>KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>c2oey78nd0</Fingerprint>
      <Integrity>rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.rtl.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tdbxkamptv</Fingerprint>
      <Integrity>H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>j5mq2jizvt</Fingerprint>
      <Integrity>p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>06098lyss8</Fingerprint>
      <Integrity>GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>nvvlpmu67g</Fingerprint>
      <Integrity>o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pj5nd1wqec</Fingerprint>
      <Integrity>KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>46ein0sx1k</Fingerprint>
      <Integrity>PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>v0zj4ognzu</Fingerprint>
      <Integrity>8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.rtl.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>37tfw0ft22</Fingerprint>
      <Integrity>j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.rtl.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>hrwsygsryq</Fingerprint>
      <Integrity>3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.rtl.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pk9g2wxc8p</Fingerprint>
      <Integrity>h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/css/bootstrap.rtl.min.css.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ft3s53vfgj</Fingerprint>
      <Integrity>rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>notf2xhcfb</Fingerprint>
      <Integrity>+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.bundle.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6cfz1n2cew</Fingerprint>
      <Integrity>mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.bundle.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6pdc2jztkx</Fingerprint>
      <Integrity>Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.bundle.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>493y06b0oq</Fingerprint>
      <Integrity>CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.bundle.min.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>iovd86k7lj</Fingerprint>
      <Integrity>Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.esm.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vr1egmr9el</Fingerprint>
      <Integrity>exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.esm.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kbrnm935zg</Fingerprint>
      <Integrity>EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.esm.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jj8uyg4cgr</Fingerprint>
      <Integrity>QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.esm.min.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>y7v9cxd14o</Fingerprint>
      <Integrity>Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>h1s4sie4z3</Fingerprint>
      <Integrity>9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>63fj8s7r0e</Fingerprint>
      <Integrity>3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/dist/js/bootstrap.min.js.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>0j3bgjxly4</Fingerprint>
      <Integrity>ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/bootstrap/LICENSE</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>81b7ukuj9c</Fingerprint>
      <Integrity>ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47otxtyo56</Fingerprint>
      <Integrity>wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4v8eqarkd7</Fingerprint>
      <Integrity>YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation-unobtrusive/LICENSE.txt</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>356vix0kms</Fingerprint>
      <Integrity>16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation/dist/additional-methods.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>83jwlth58m</Fingerprint>
      <Integrity>XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation/dist/additional-methods.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>mrlpezrjn3</Fingerprint>
      <Integrity>jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation/dist/jquery.validate.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lzl9nlhx6b</Fingerprint>
      <Integrity>kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation/dist/jquery.validate.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ag7o75518u</Fingerprint>
      <Integrity>umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery-validation/LICENSE.md</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>x0q3zqp4vz</Fingerprint>
      <Integrity>geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>0i3buxo5is</Fingerprint>
      <Integrity>eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>o1o13a6vjx</Fingerprint>
      <Integrity>/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.min.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ttgo8qnofa</Fingerprint>
      <Integrity>z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.slim.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2z0ns9nrw6</Fingerprint>
      <Integrity>UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.slim.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>muycvpuwrr</Fingerprint>
      <Integrity>kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.map'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/dist/jquery.slim.min.map</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>87fc7y1x7t</Fingerprint>
      <Integrity>9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.map'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/jquery/LICENSE.txt</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>mlv21k5csn</Fingerprint>
      <Integrity>hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\pannellum\pannellum.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>lib/pannellum/pannellum.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n7rwetgxxg</Fingerprint>
      <Integrity>PQxFYpNGYpSptCno8rCjsn0Wx1KBatcV1TE1n4Qx++Q=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\pannellum\pannellum.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ViVu.tdadp1kl73.bundle.scp.css'))">
      <SourceType>Package</SourceType>
      <SourceId>ViVu</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/ViVu</BasePath>
      <RelativePath>ViVu.tdadp1kl73.bundle.scp.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>Reference</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName>ScopedCss</AssetTraitName>
      <AssetTraitValue>ProjectBundle</AssetTraitValue>
      <Fingerprint>tdadp1kl73</Fingerprint>
      <Integrity>L60/de/ykz6gINCM4lRD7nafe7GAFKDIGIOUWzN2dC0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ViVu.tdadp1kl73.bundle.scp.css'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>