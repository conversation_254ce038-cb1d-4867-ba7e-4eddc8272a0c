﻿﻿using ViVu.Models;

namespace ViVu.Data
{
    public static class LocationSeedData
    {
        public static void SeedLocations(ApplicationDbContext context)
        {
            // Kiểm tra xem đã có dữ liệu địa điểm nào chưa
            if (context.Locations.Any())
            {
                // Cập nhật các địa điểm hiện có
                UpdateExistingLocations(context);
                
                // Thêm các địa điểm mới
                AddNewLocations(context);
                
                return;
            }
        }

        private static void UpdateExistingLocations(ApplicationDbContext context)
        {
            // Cập nhật thông tin chi tiết cho các địa điểm hiện có
            var conPhung = context.Locations.FirstOrDefault(l => l.Name.Contains("Cồn Phụng"));
            if (conPhung != null)
            {
                conPhung.Description = "Cồn Phụng (hay còn gọi là đảo Ông Đạo Dừa) là một trong những điểm du lịch nổi tiếng nhất của Bến Tre. Nơi đây nổi tiếng với khu di tích <PERSON>ạ<PERSON> Dừ<PERSON>, các hoạt động trải nghiệm văn hóa địa phương, và ẩm thực đặc sản miền sông nước.";
                conPhung.Address = "Ấp Phụng, xã Tân Thạch, huyện Châu Thành, tỉnh Bến Tre";
                conPhung.Phone = "0273 3123 456";
                conPhung.Email = "<EMAIL>";
                conPhung.Website = "https://dulichbentre.com.vn/con-phung";
                conPhung.Latitude = 10.2845;
                conPhung.Longitude = 106.3456;
                conPhung.IsFeatured = true;
                
                context.Update(conPhung);
            }

            var langNgheDua = context.Locations.FirstOrDefault(l => l.Name.Contains("Làng nghề dừa"));
            if (langNgheDua != null)
            {
                langNgheDua.Description = "Làng nghề dừa Bến Tre là nơi du khách có thể tham quan và trải nghiệm quy trình chế biến các sản phẩm từ dừa. Tại đây, bạn sẽ được tận mắt chứng kiến sự khéo léo của người dân địa phương trong việc biến tấu cây dừa thành hàng trăm sản phẩm hữu ích.";
                langNgheDua.Address = "Xã Phú Phụng, huyện Chợ Lách, tỉnh Bến Tre";
                langNgheDua.Phone = "0273 3567 890";
                langNgheDua.Email = "<EMAIL>";
                langNgheDua.Website = "https://dulichbentre.com.vn/lang-nghe-dua";
                langNgheDua.Latitude = 10.2156;
                langNgheDua.Longitude = 106.1789;
                langNgheDua.IsFeatured = true;
                
                context.Update(langNgheDua);
            }

            var conOc = context.Locations.FirstOrDefault(l => l.Name.Contains("Cồn Ốc"));
            if (conOc != null)
            {
                conOc.Description = "Cồn Ốc là điểm du lịch sinh thái với vườn trái cây, sông nước và ẩm thực đặc sắc của miền Tây. Đây là nơi lý tưởng để trải nghiệm cuộc sống sông nước và thưởng thức các loại trái cây theo mùa.";
                conOc.Address = "Xã Tân Thạch, huyện Châu Thành, tỉnh Bến Tre";
                conOc.Phone = "0273 3891 234";
                conOc.Email = "<EMAIL>";
                conOc.Website = "https://dulichbentre.com.vn/con-oc";
                conOc.Latitude = 10.3012;
                conOc.Longitude = 106.3789;
                conOc.IsFeatured = true;
                
                context.Update(conOc);
            }

            var chuaAng = context.Locations.FirstOrDefault(l => l.Name.Contains("Chùa Âng"));
            if (chuaAng != null)
            {
                chuaAng.Description = "Chùa Âng là ngôi chùa Khmer cổ với kiến trúc độc đáo và nhiều giá trị văn hóa, lịch sử. Ngôi chùa này là một trong những điểm tham quan không thể bỏ qua khi đến Trà Vinh.";
                chuaAng.Address = "Phường 2, thành phố Trà Vinh, tỉnh Trà Vinh";
                chuaAng.Phone = "0294 3456 789";
                chuaAng.Email = "<EMAIL>";
                chuaAng.Website = "https://dulichtravinh.com.vn/chua-ang";
                chuaAng.Latitude = 9.9345;
                chuaAng.Longitude = 106.3456;
                chuaAng.IsFeatured = true;
                
                context.Update(chuaAng);
            }

            context.SaveChanges();
        }

        private static void AddNewLocations(ApplicationDbContext context)
        {
            // Lấy thành phố Bến Tre
            var benTreCity = context.Cities.FirstOrDefault(c => c.Name.Contains("Bến Tre"));
            if (benTreCity == null)
                return;

            // Kiểm tra xem các địa điểm mới đã tồn tại chưa
            if (context.Locations.Any(l => l.Name.Contains("Vườn trái cây Cái Mơn")))
                return;

            // Thêm các địa điểm mới
            var newLocations = new List<Location>
            {
                new Location
                {
                    Name = "Vườn trái cây Cái Mơn",
                    Description = "Vườn trái cây Cái Mơn là một trong những vườn trái cây nổi tiếng nhất ở Bến Tre, nơi du khách có thể tham quan và thưởng thức nhiều loại trái cây đặc sản như sầu riêng, chôm chôm, măng cụt, bưởi, cam, quýt theo mùa.",
                    Address = "Xã Vĩnh Thành, huyện Chợ Lách, tỉnh Bến Tre",
                    Phone = "0273 3678 901",
                    Email = "<EMAIL>",
                    Website = "https://dulichbentre.com.vn/vuon-cai-mon",
                    ImageUrl = "/images/destinations/vuoncaimon.jpg",
                    Latitude = 10.2567,
                    Longitude = 106.2123,
                    IsFeatured = true,
                    CityId = benTreCity.Id
                },
                new Location
                {
                    Name = "Khu du lịch Phú An Khang",
                    Description = "Khu du lịch Phú An Khang là điểm đến lý tưởng cho những ai muốn tìm hiểu về văn hóa và cuộc sống của người dân miền Tây. Tại đây, du khách có thể tham gia các hoạt động như chèo xuồng ba lá, câu cá, hái trái cây và thưởng thức ẩm thực đặc sản.",
                    Address = "Ấp An Thuận, xã Phú An Hòa, huyện Châu Thành, tỉnh Bến Tre",
                    Phone = "0273 3456 789",
                    Email = "<EMAIL>",
                    Website = "https://phuankhang.com.vn",
                    ImageUrl = "/images/destinations/phuankhang.jpg",
                    Latitude = 10.3045,
                    Longitude = 106.3789,
                    IsFeatured = true,
                    CityId = benTreCity.Id
                },
                new Location
                {
                    Name = "Làng nghề đan lát Phú Sơn",
                    Description = "Làng nghề đan lát Phú Sơn là nơi lưu giữ và phát triển nghề đan lát truyền thống của người dân Bến Tre. Du khách đến đây có thể tham quan quy trình sản xuất và mua sắm các sản phẩm thủ công mỹ nghệ đặc sắc.",
                    Address = "Xã Phú Sơn, huyện Chợ Lách, tỉnh Bến Tre",
                    Phone = "0273 3234 567",
                    Email = "<EMAIL>",
                    Website = "https://dulichbentre.com.vn/lang-nghe-dan-lat",
                    ImageUrl = "/images/destinations/langnghedanlat.jpg",
                    Latitude = 10.2345,
                    Longitude = 106.1987,
                    IsFeatured = false,
                    CityId = benTreCity.Id
                },
                new Location
                {
                    Name = "Cồn Quy (Cồn Rùa)",
                    Description = "Cồn Quy (hay còn gọi là Cồn Rùa) là một hòn đảo nhỏ nằm giữa sông Tiền, nơi du khách có thể tham quan vườn cây ăn trái, thưởng thức đặc sản địa phương và trải nghiệm cuộc sống yên bình của người dân miền sông nước.",
                    Address = "Xã Hưng Phong, huyện Giồng Trôm, tỉnh Bến Tre",
                    Phone = "0273 3789 012",
                    Email = "<EMAIL>",
                    Website = "https://dulichbentre.com.vn/con-quy",
                    ImageUrl = "/images/destinations/conquy.jpg",
                    Latitude = 10.2789,
                    Longitude = 106.4123,
                    IsFeatured = false,
                    CityId = benTreCity.Id
                }
            };

            context.Locations.AddRange(newLocations);
            context.SaveChanges();
        }
    }
}
