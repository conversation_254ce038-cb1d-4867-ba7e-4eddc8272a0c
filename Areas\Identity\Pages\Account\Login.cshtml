﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Log in";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-5">
            <div class="card shadow-lg border-0 rounded-3">
                <div class="card-header bg-primary text-white text-center">
                    <h2 class="mb-0">Login to Your Account</h2>
                </div>
                <div class="card-body p-4">
                    <form id="account" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input asp-for="Input.Email" class="form-control" autocomplete="username" placeholder="<EMAIL>" />
                            </div>
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input asp-for="Input.Password" type="password" class="form-control" autocomplete="current-password" placeholder="••••••••" />
                            </div>
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            <label asp-for="Input.RememberMe" class="form-check-label">
                                Remember me
                            </label>
                        </div>

                        <button id="login-submit" type="submit" class="btn btn-primary w-100 fw-bold">
                            <i class="bi bi-box-arrow-in-right"></i> Log in
                        </button>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p><a id="forgot-password" asp-page="./ForgotPassword">Forgot your password?</a></p>
                    <p><a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Register as a new user</a></p>
                    <p><a id="resend-confirmation" asp-page="./ResendEmailConfirmation">Resend email confirmation</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
