﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/admin.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wdwZ\u002BGOJP2YeOg8uQtRQC6Gg34b2nKGZEBTuvsyvdKg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5993"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022wdwZ\u002BGOJP2YeOg8uQtRQC6Gg34b2nKGZEBTuvsyvdKg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:30:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/admin.dxljrfpbru.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dxljrfpbru"},{"Name":"integrity","Value":"sha256-wdwZ\u002BGOJP2YeOg8uQtRQC6Gg34b2nKGZEBTuvsyvdKg="},{"Name":"label","Value":"_content/ViVu/css/admin.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5993"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022wdwZ\u002BGOJP2YeOg8uQtRQC6Gg34b2nKGZEBTuvsyvdKg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:30:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/ai-loading.3p64rit9j9.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\ai-loading.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3p64rit9j9"},{"Name":"integrity","Value":"sha256-541SJlDCkg8GB4R39QRkikBoiCnT2cUuNYe/jJuXFOI="},{"Name":"label","Value":"_content/ViVu/css/ai-loading.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2844"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022541SJlDCkg8GB4R39QRkikBoiCnT2cUuNYe/jJuXFOI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 10:14:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/ai-loading.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\ai-loading.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-541SJlDCkg8GB4R39QRkikBoiCnT2cUuNYe/jJuXFOI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2844"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022541SJlDCkg8GB4R39QRkikBoiCnT2cUuNYe/jJuXFOI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 10:14:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/animations.7j8yb0ghog.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7j8yb0ghog"},{"Name":"integrity","Value":"sha256-TXWysZOFWRXwb2POsHFYrW6rwbe0H1WXcZ4Zo2laSTA="},{"Name":"label","Value":"_content/ViVu/css/animations.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5021"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022TXWysZOFWRXwb2POsHFYrW6rwbe0H1WXcZ4Zo2laSTA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 11 May 2025 04:24:36 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/animations.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-TXWysZOFWRXwb2POsHFYrW6rwbe0H1WXcZ4Zo2laSTA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5021"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022TXWysZOFWRXwb2POsHFYrW6rwbe0H1WXcZ4Zo2laSTA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 11 May 2025 04:24:36 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/pannellum.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\pannellum.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7S16LgMnZW3JpyAl7EjLBx\u002Bc2\u002BPmgtom5zrTG6ZL7vY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"223"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00227S16LgMnZW3JpyAl7EjLBx\u002Bc2\u002BPmgtom5zrTG6ZL7vY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:06:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/pannellum.jg0bvj93ve.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\pannellum.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jg0bvj93ve"},{"Name":"integrity","Value":"sha256-7S16LgMnZW3JpyAl7EjLBx\u002Bc2\u002BPmgtom5zrTG6ZL7vY="},{"Name":"label","Value":"_content/ViVu/css/pannellum.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"223"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00227S16LgMnZW3JpyAl7EjLBx\u002Bc2\u002BPmgtom5zrTG6ZL7vY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:06:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/panorama360.5zp14ypy8s.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\panorama360.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5zp14ypy8s"},{"Name":"integrity","Value":"sha256-wZUmdIyUF4piY5XSNBkbbYqqjWc0TU73WasEc6F/wJE="},{"Name":"label","Value":"_content/ViVu/css/panorama360.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1491"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022wZUmdIyUF4piY5XSNBkbbYqqjWc0TU73WasEc6F/wJE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/panorama360.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\panorama360.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wZUmdIyUF4piY5XSNBkbbYqqjWc0TU73WasEc6F/wJE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1491"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022wZUmdIyUF4piY5XSNBkbbYqqjWc0TU73WasEc6F/wJE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/site.4i9j4omsgm.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4i9j4omsgm"},{"Name":"integrity","Value":"sha256-JAUK50ZnGZLc35OLCvoWSpgEIvIPgsaeEALAtyeQsao="},{"Name":"label","Value":"_content/ViVu/css/site.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2560"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JAUK50ZnGZLc35OLCvoWSpgEIvIPgsaeEALAtyeQsao=\u0022"},{"Name":"Last-Modified","Value":"Fri, 02 May 2025 03:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/site.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JAUK50ZnGZLc35OLCvoWSpgEIvIPgsaeEALAtyeQsao="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2560"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JAUK50ZnGZLc35OLCvoWSpgEIvIPgsaeEALAtyeQsao=\u0022"},{"Name":"Last-Modified","Value":"Fri, 02 May 2025 03:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/style.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H5imjUHfhR85rRbyk\u002BK3gX9c0K4nvvd5y8ygZk3QJ9I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"32720"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H5imjUHfhR85rRbyk\u002BK3gX9c0K4nvvd5y8ygZk3QJ9I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 11 May 2025 06:05:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/css/style.rha8patjeh.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rha8patjeh"},{"Name":"integrity","Value":"sha256-H5imjUHfhR85rRbyk\u002BK3gX9c0K4nvvd5y8ygZk3QJ9I="},{"Name":"label","Value":"_content/ViVu/css/style.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"32720"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H5imjUHfhR85rRbyk\u002BK3gX9c0K4nvvd5y8ygZk3QJ9I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 11 May 2025 06:05:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/ViVu/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/480795064_663735616004425_8146582297492019421_n.32ad5966ih.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\480795064_663735616004425_8146582297492019421_n.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"32ad5966ih"},{"Name":"integrity","Value":"sha256-1cJATDRATWirJaEJVjYPBdGd1Emm2rIBH4krWdZZGj0="},{"Name":"label","Value":"_content/ViVu/images/480795064_663735616004425_8146582297492019421_n.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"118625"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00221cJATDRATWirJaEJVjYPBdGd1Emm2rIBH4krWdZZGj0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 26 Feb 2025 04:12:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/480795064_663735616004425_8146582297492019421_n.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\480795064_663735616004425_8146582297492019421_n.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1cJATDRATWirJaEJVjYPBdGd1Emm2rIBH4krWdZZGj0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"118625"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00221cJATDRATWirJaEJVjYPBdGd1Emm2rIBH4krWdZZGj0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 26 Feb 2025 04:12:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/11b29610-4311-4719-a891-f0feaba19d86_405889067.hm9jdghkkh.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hm9jdghkkh"},{"Name":"integrity","Value":"sha256-eTQamAqv2y0eBBROuUYQr9WU48w2oFaA7HiwCttkpvI="},{"Name":"label","Value":"_content/ViVu/images/accommodations/11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"136260"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022eTQamAqv2y0eBBROuUYQr9WU48w2oFaA7HiwCttkpvI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:15:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\11b29610-4311-4719-a891-f0feaba19d86_405889067.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eTQamAqv2y0eBBROuUYQr9WU48w2oFaA7HiwCttkpvI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"136260"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022eTQamAqv2y0eBBROuUYQr9WU48w2oFaA7HiwCttkpvI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:15:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.ncmrcvan7i.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ncmrcvan7i"},{"Name":"integrity","Value":"sha256-53F\u002BSx\u002B59S8LwfoeBFkSmNIAotOyDAy4uDGiVckfqek="},{"Name":"label","Value":"_content/ViVu/images/accommodations/7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"213892"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u002253F\u002BSx\u002B59S8LwfoeBFkSmNIAotOyDAy4uDGiVckfqek=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:13:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\7d8200d4-4943-4cc6-9b1a-2fc82e6adb96_d2ee10d670bfc9cad3c6f1be0195ad9e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-53F\u002BSx\u002B59S8LwfoeBFkSmNIAotOyDAy4uDGiVckfqek="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"213892"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u002253F\u002BSx\u002B59S8LwfoeBFkSmNIAotOyDAy4uDGiVckfqek=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:13:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.7vj59tyyd3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7vj59tyyd3"},{"Name":"integrity","Value":"sha256-09CMt4VCgKtIwnR6nIrOMFMvy0r5D9H9CqBR9ER9eMc="},{"Name":"label","Value":"_content/ViVu/images/accommodations/869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1079714"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002209CMt4VCgKtIwnR6nIrOMFMvy0r5D9H9CqBR9ER9eMc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:29:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\869b8cd1-1a96-4afa-b78b-6da4dba53d12_z5913240417910_9bc36df5994f78c0794f0a150a45c8e5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-09CMt4VCgKtIwnR6nIrOMFMvy0r5D9H9CqBR9ER9eMc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1079714"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002209CMt4VCgKtIwnR6nIrOMFMvy0r5D9H9CqBR9ER9eMc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:29:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.c24wsd0dd1.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c24wsd0dd1"},{"Name":"integrity","Value":"sha256-lAyJlXaSktMicL/LfJ03cFSso7zDu1b78Dw6qjuoUs4="},{"Name":"label","Value":"_content/ViVu/images/accommodations/9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"176430"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022lAyJlXaSktMicL/LfJ03cFSso7zDu1b78Dw6qjuoUs4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:14:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\9e23c68f-1b29-4c7c-b320-3ea10bf7747d_c263bbfa55bc8d3a2eb8764fd134c07e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lAyJlXaSktMicL/LfJ03cFSso7zDu1b78Dw6qjuoUs4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"176430"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022lAyJlXaSktMicL/LfJ03cFSso7zDu1b78Dw6qjuoUs4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:14:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_garden.fn1nh4ztjd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_garden.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fn1nh4ztjd"},{"Name":"integrity","Value":"sha256-GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY="},{"Name":"label","Value":"_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"266773"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_garden.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"266773"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_pool.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_pool.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"262072"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_pool.kiiyxi4psp.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\amenities\hotel_ben-tre-riverside_pool.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kiiyxi4psp"},{"Name":"integrity","Value":"sha256-PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw="},{"Name":"label","Value":"_content/ViVu/images/accommodations/amenities/hotel_ben-tre-riverside_pool.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"262072"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UD/fd/VQggkE1jVbmcqpqOOeY4WCspIxUzxrwdkNKy8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"107677"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UD/fd/VQggkE1jVbmcqpqOOeY4WCspIxUzxrwdkNKy8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:18:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.kfeholhxuy.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kfeholhxuy"},{"Name":"integrity","Value":"sha256-UD/fd/VQggkE1jVbmcqpqOOeY4WCspIxUzxrwdkNKy8="},{"Name":"label","Value":"_content/ViVu/images/accommodations/bc92909b-3f26-408c-86f2-d4f1482ec2cd_615066349.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107677"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UD/fd/VQggkE1jVbmcqpqOOeY4WCspIxUzxrwdkNKy8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:18:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bedroom2_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom2_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137246"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:33:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bedroom2_Ben Tre Riverside Resort.vu9tnbmojk.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom2_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vu9tnbmojk"},{"Name":"integrity","Value":"sha256-J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I="},{"Name":"label","Value":"_content/ViVu/images/accommodations/bedroom2_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137246"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:33:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bedroom_Ben Tre Riverside Resort.d0c046feaf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d0c046feaf"},{"Name":"integrity","Value":"sha256-zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ="},{"Name":"label","Value":"_content/ViVu/images/accommodations/bedroom_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"111179"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/bedroom_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\bedroom_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"111179"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.i2rgeyjk10.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i2rgeyjk10"},{"Name":"integrity","Value":"sha256-9kwXP0Dkqji2uxHmF7jhYGt9xh58A3C23Mq0WEs5dtQ="},{"Name":"label","Value":"_content/ViVu/images/accommodations/c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"80731"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229kwXP0Dkqji2uxHmF7jhYGt9xh58A3C23Mq0WEs5dtQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:17:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\c1ea11ce-0c42-4a4a-98a6-8c3051881756_645189534.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9kwXP0Dkqji2uxHmF7jhYGt9xh58A3C23Mq0WEs5dtQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"80731"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229kwXP0Dkqji2uxHmF7jhYGt9xh58A3C23Mq0WEs5dtQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:17:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/deluxe_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\deluxe_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Vv\u002BUbZ52qPIxZ6VukXYVwpHHJNqg8Nebp6i1lEUAUNo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"121784"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Vv\u002BUbZ52qPIxZ6VukXYVwpHHJNqg8Nebp6i1lEUAUNo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/deluxe_Ben Tre Riverside Resort.mcpscl1u7j.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\deluxe_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mcpscl1u7j"},{"Name":"integrity","Value":"sha256-Vv\u002BUbZ52qPIxZ6VukXYVwpHHJNqg8Nebp6i1lEUAUNo="},{"Name":"label","Value":"_content/ViVu/images/accommodations/deluxe_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"121784"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Vv\u002BUbZ52qPIxZ6VukXYVwpHHJNqg8Nebp6i1lEUAUNo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/DIAMOND STARS BEN TRE HOTEL.2ovkft7spb.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\DIAMOND STARS BEN TRE HOTEL.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2ovkft7spb"},{"Name":"integrity","Value":"sha256-UlAB/FuQOFALJgIVERTdXwi0eoGGH7X4v5GXbaW/PeM="},{"Name":"label","Value":"_content/ViVu/images/accommodations/DIAMOND STARS BEN TRE HOTEL.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"394990"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UlAB/FuQOFALJgIVERTdXwi0eoGGH7X4v5GXbaW/PeM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:26:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/DIAMOND STARS BEN TRE HOTEL.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\DIAMOND STARS BEN TRE HOTEL.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UlAB/FuQOFALJgIVERTdXwi0eoGGH7X4v5GXbaW/PeM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"394990"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UlAB/FuQOFALJgIVERTdXwi0eoGGH7X4v5GXbaW/PeM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:26:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/elite-suite-room_diamon hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\elite-suite-room_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-y5atjFdviTFKCLtP70opcOlXSx4mo/DadxrA27Jx26E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"176184"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022y5atjFdviTFKCLtP70opcOlXSx4mo/DadxrA27Jx26E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:28:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/elite-suite-room_diamon hotel.zi2waiznw4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\elite-suite-room_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zi2waiznw4"},{"Name":"integrity","Value":"sha256-y5atjFdviTFKCLtP70opcOlXSx4mo/DadxrA27Jx26E="},{"Name":"label","Value":"_content/ViVu/images/accommodations/elite-suite-room_diamon hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"176184"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022y5atjFdviTFKCLtP70opcOlXSx4mo/DadxrA27Jx26E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:28:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.3ecs43jd2n.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3ecs43jd2n"},{"Name":"integrity","Value":"sha256-uw2QDjlgBsIeJ37knnSQuRwjTpzr8iXso\u002BccdYEYQkw="},{"Name":"label","Value":"_content/ViVu/images/accommodations/fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"133954"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022uw2QDjlgBsIeJ37knnSQuRwjTpzr8iXso\u002BccdYEYQkw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:15:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\fa0e5d29-ec0b-4c37-b960-1ec1ca95d9dd_465917207.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uw2QDjlgBsIeJ37knnSQuRwjTpzr8iXso\u002BccdYEYQkw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"133954"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022uw2QDjlgBsIeJ37knnSQuRwjTpzr8iXso\u002BccdYEYQkw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:15:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/front-hotel_Viet Uc Hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\front-hotel_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-75LX3UnoIvmGcSvL4Y8C80ToDLFU437minOyZzb7FHw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"176137"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002275LX3UnoIvmGcSvL4Y8C80ToDLFU437minOyZzb7FHw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:50:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/front-hotel_Viet Uc Hotel.lbfybsxo9f.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\front-hotel_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lbfybsxo9f"},{"Name":"integrity","Value":"sha256-75LX3UnoIvmGcSvL4Y8C80ToDLFU437minOyZzb7FHw="},{"Name":"label","Value":"_content/ViVu/images/accommodations/front-hotel_Viet Uc Hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"176137"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002275LX3UnoIvmGcSvL4Y8C80ToDLFU437minOyZzb7FHw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:50:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/garden_Ben Tre Riverside Resort.fn1nh4ztjd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\garden_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fn1nh4ztjd"},{"Name":"integrity","Value":"sha256-GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY="},{"Name":"label","Value":"_content/ViVu/images/accommodations/garden_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"266773"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/garden_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\garden_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"266773"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GxmCXIGf1T4AfRvnmWSHtVbwtIXvn22Xy75OREOYXHY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/kham-pha-phong-suite_Viet Uc Hotel.ddj172fscs.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\kham-pha-phong-suite_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ddj172fscs"},{"Name":"integrity","Value":"sha256-UbfWnu3shKp\u002B\u002B5\u002Bfm5LHlJQPMGO3VRFL/nJR\u002BxNSwzM="},{"Name":"label","Value":"_content/ViVu/images/accommodations/kham-pha-phong-suite_Viet Uc Hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"82494"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UbfWnu3shKp\u002B\u002B5\u002Bfm5LHlJQPMGO3VRFL/nJR\u002BxNSwzM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/kham-pha-phong-suite_Viet Uc Hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\kham-pha-phong-suite_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UbfWnu3shKp\u002B\u002B5\u002Bfm5LHlJQPMGO3VRFL/nJR\u002BxNSwzM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"82494"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UbfWnu3shKp\u002B\u002B5\u002Bfm5LHlJQPMGO3VRFL/nJR\u002BxNSwzM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/living-room_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\living-room_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bElKHSKssKFWDy/aoJ1kmf9iN4DIXg/1iOjU3itWYTc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"120491"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022bElKHSKssKFWDy/aoJ1kmf9iN4DIXg/1iOjU3itWYTc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/living-room_Ben Tre Riverside Resort.sca8becvv6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\living-room_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sca8becvv6"},{"Name":"integrity","Value":"sha256-bElKHSKssKFWDy/aoJ1kmf9iN4DIXg/1iOjU3itWYTc="},{"Name":"label","Value":"_content/ViVu/images/accommodations/living-room_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"120491"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022bElKHSKssKFWDy/aoJ1kmf9iN4DIXg/1iOjU3itWYTc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/oasis-hotel-seen-from.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel-seen-from.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mKq2f1SYaqWm0LfEOB3bB5YU5FCewHorKysBumHv8TY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"145562"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mKq2f1SYaqWm0LfEOB3bB5YU5FCewHorKysBumHv8TY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:27:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/oasis-hotel-seen-from.kd4hduw3cy.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel-seen-from.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kd4hduw3cy"},{"Name":"integrity","Value":"sha256-mKq2f1SYaqWm0LfEOB3bB5YU5FCewHorKysBumHv8TY="},{"Name":"label","Value":"_content/ViVu/images/accommodations/oasis-hotel-seen-from.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"145562"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mKq2f1SYaqWm0LfEOB3bB5YU5FCewHorKysBumHv8TY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:27:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/oasis-hotel.4f7qrtlctl.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4f7qrtlctl"},{"Name":"integrity","Value":"sha256-8HgsR8MSSCOudHZUQMo4R17QO/RAh48RSBEh1U\u002B\u002BS5g="},{"Name":"label","Value":"_content/ViVu/images/accommodations/oasis-hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"105023"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228HgsR8MSSCOudHZUQMo4R17QO/RAh48RSBEh1U\u002B\u002BS5g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:28:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/oasis-hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\oasis-hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8HgsR8MSSCOudHZUQMo4R17QO/RAh48RSBEh1U\u002B\u002BS5g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"105023"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228HgsR8MSSCOudHZUQMo4R17QO/RAh48RSBEh1U\u002B\u002BS5g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:28:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"262072"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/outdoor-swimming-poo_Ben Tre Riverside Resortl.kiiyxi4psp.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kiiyxi4psp"},{"Name":"integrity","Value":"sha256-PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw="},{"Name":"label","Value":"_content/ViVu/images/accommodations/outdoor-swimming-poo_Ben Tre Riverside Resortl.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"262072"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PFs2qVuu8ToSyNE5VtnA5q1dWZglN8FdaUergUCGFrw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/photo4jpg_Viet Uc Hotel.41oiskb6dv.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\photo4jpg_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"41oiskb6dv"},{"Name":"integrity","Value":"sha256-KOYDt4tB0gMEW\u002BbAEcp0psGAg9w2KaBMr/qY7FolIXE="},{"Name":"label","Value":"_content/ViVu/images/accommodations/photo4jpg_Viet Uc Hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"153709"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KOYDt4tB0gMEW\u002BbAEcp0psGAg9w2KaBMr/qY7FolIXE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/photo4jpg_Viet Uc Hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\photo4jpg_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KOYDt4tB0gMEW\u002BbAEcp0psGAg9w2KaBMr/qY7FolIXE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"153709"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KOYDt4tB0gMEW\u002BbAEcp0psGAg9w2KaBMr/qY7FolIXE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/presidential-suite-room_diamonhotel.5w0zt4jh66.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\presidential-suite-room_diamonhotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5w0zt4jh66"},{"Name":"integrity","Value":"sha256-u9N2J8cJ4TD3qTk5OldFZ9ZioM7HM/wuGY5uou\u002Btp60="},{"Name":"label","Value":"_content/ViVu/images/accommodations/presidential-suite-room_diamonhotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"191285"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022u9N2J8cJ4TD3qTk5OldFZ9ZioM7HM/wuGY5uou\u002Btp60=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:29:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/presidential-suite-room_diamonhotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\presidential-suite-room_diamonhotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-u9N2J8cJ4TD3qTk5OldFZ9ZioM7HM/wuGY5uou\u002Btp60="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"191285"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022u9N2J8cJ4TD3qTk5OldFZ9ZioM7HM/wuGY5uou\u002Btp60=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:29:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/prime-suite-room_diamon hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\prime-suite-room_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-412IdcLFIC1TwLQtYT8n2EQ\u002BZyoT6gZLKNpSrpt8MJg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137291"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022412IdcLFIC1TwLQtYT8n2EQ\u002BZyoT6gZLKNpSrpt8MJg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:29:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/prime-suite-room_diamon hotel.zgcvtg552r.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\prime-suite-room_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zgcvtg552r"},{"Name":"integrity","Value":"sha256-412IdcLFIC1TwLQtYT8n2EQ\u002BZyoT6gZLKNpSrpt8MJg="},{"Name":"label","Value":"_content/ViVu/images/accommodations/prime-suite-room_diamon hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137291"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022412IdcLFIC1TwLQtYT8n2EQ\u002BZyoT6gZLKNpSrpt8MJg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:29:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/reception-2_Viet Uc Hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\reception-2_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GegPBxgTSssx0BnnumWdUTqg1UuoPzFJeJg0d\u002BYTm9E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137062"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GegPBxgTSssx0BnnumWdUTqg1UuoPzFJeJg0d\u002BYTm9E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/reception-2_Viet Uc Hotel.l5h99w04tm.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\reception-2_Viet Uc Hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l5h99w04tm"},{"Name":"integrity","Value":"sha256-GegPBxgTSssx0BnnumWdUTqg1UuoPzFJeJg0d\u002BYTm9E="},{"Name":"label","Value":"_content/ViVu/images/accommodations/reception-2_Viet Uc Hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137062"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GegPBxgTSssx0BnnumWdUTqg1UuoPzFJeJg0d\u002BYTm9E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:34:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_deluxe_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137246"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:33:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.vu9tnbmojk.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_deluxe_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vu9tnbmojk"},{"Name":"integrity","Value":"sha256-J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I="},{"Name":"label","Value":"_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137246"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022J7qr3qHLGA4ZMz0rRYEqZlqgC703Tycqs7Z4fEh\u002BT5I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:33:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.d0c046feaf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_standard_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d0c046feaf"},{"Name":"integrity","Value":"sha256-zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ="},{"Name":"label","Value":"_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"111179"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\rooms\hotel_ben-tre-riverside_room_standard_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"111179"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zQjWddQ6X9FNRlxz1dZZSkFDrrsT0/MqAORCe0JnDnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/signature-deluxe-king_diamon hotel.ef7gz65mg4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\signature-deluxe-king_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ef7gz65mg4"},{"Name":"integrity","Value":"sha256-mjQRP9N\u002BdjNu42fVLI\u002BiV/gtAkRU8n4Px9P\u002BXT8LLjU="},{"Name":"label","Value":"_content/ViVu/images/accommodations/signature-deluxe-king_diamon hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"200527"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mjQRP9N\u002BdjNu42fVLI\u002BiV/gtAkRU8n4Px9P\u002BXT8LLjU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:30:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/signature-deluxe-king_diamon hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\signature-deluxe-king_diamon hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mjQRP9N\u002BdjNu42fVLI\u002BiV/gtAkRU8n4Px9P\u002BXT8LLjU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"200527"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mjQRP9N\u002BdjNu42fVLI\u002BiV/gtAkRU8n4Px9P\u002BXT8LLjU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:30:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/suite_Ben Tre Riverside Resort.1oxjvtgbjd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\suite_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1oxjvtgbjd"},{"Name":"integrity","Value":"sha256-z/s9XYe6MdGovekSM7kp6sp02aYWBdvZsSbM8qbbsGo="},{"Name":"label","Value":"_content/ViVu/images/accommodations/suite_Ben Tre Riverside Resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"112880"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022z/s9XYe6MdGovekSM7kp6sp02aYWBdvZsSbM8qbbsGo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/suite_Ben Tre Riverside Resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\suite_Ben Tre Riverside Resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-z/s9XYe6MdGovekSM7kp6sp02aYWBdvZsSbM8qbbsGo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"112880"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022z/s9XYe6MdGovekSM7kp6sp02aYWBdvZsSbM8qbbsGo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:32:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/Viet Uc Hotel1.jhsfwb17r4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\Viet Uc Hotel1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jhsfwb17r4"},{"Name":"integrity","Value":"sha256-CcbdC5vql/bm8pkWTqAM\u002BuvowPcozRdSO\u002BQbcF9OhNk="},{"Name":"label","Value":"_content/ViVu/images/accommodations/Viet Uc Hotel1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"102520"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022CcbdC5vql/bm8pkWTqAM\u002BuvowPcozRdSO\u002BQbcF9OhNk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:50:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/Viet Uc Hotel1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\Viet Uc Hotel1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CcbdC5vql/bm8pkWTqAM\u002BuvowPcozRdSO\u002BQbcF9OhNk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"102520"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022CcbdC5vql/bm8pkWTqAM\u002BuvowPcozRdSO\u002BQbcF9OhNk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:50:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/view-from-hotel-bentre riverside resort.ba42dmjnf0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\view-from-hotel-bentre riverside resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ba42dmjnf0"},{"Name":"integrity","Value":"sha256-M7fd\u002BF6s5ywAdsu7WFozq5dED8nch4fAdFg9sz\u002BEtmI="},{"Name":"label","Value":"_content/ViVu/images/accommodations/view-from-hotel-bentre riverside resort.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"226429"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022M7fd\u002BF6s5ywAdsu7WFozq5dED8nch4fAdFg9sz\u002BEtmI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/accommodations/view-from-hotel-bentre riverside resort.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\accommodations\view-from-hotel-bentre riverside resort.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-M7fd\u002BF6s5ywAdsu7WFozq5dED8nch4fAdFg9sz\u002BEtmI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"226429"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022M7fd\u002BF6s5ywAdsu7WFozq5dED8nch4fAdFg9sz\u002BEtmI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/ai-recommendation.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\ai-recommendation.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/ai-recommendation.lw6nk6b2t0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\ai-recommendation.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lw6nk6b2t0"},{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="},{"Name":"label","Value":"_content/ViVu/images/banners/ai-recommendation.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/anh-lang-hoa-cho-lach-ben-tre_022739971.dyphqtxwt0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\anh-lang-hoa-cho-lach-ben-tre_022739971.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dyphqtxwt0"},{"Name":"integrity","Value":"sha256-W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4="},{"Name":"label","Value":"_content/ViVu/images/banners/anh-lang-hoa-cho-lach-ben-tre_022739971.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1106432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/anh-lang-hoa-cho-lach-ben-tre_022739971.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\anh-lang-hoa-cho-lach-ben-tre_022739971.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1106432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/banner_home_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/banner_home_01.lw6nk6b2t0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lw6nk6b2t0"},{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="},{"Name":"label","Value":"_content/ViVu/images/banners/banner_home_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/banner_home_02.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"634938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/banner_home_02.lgwnj7f1qr.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\banner_home_02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lgwnj7f1qr"},{"Name":"integrity","Value":"sha256-FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw="},{"Name":"label","Value":"_content/ViVu/images/banners/banner_home_02.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"634938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/bến-tre.80cil27d7z.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\bến-tre.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"80cil27d7z"},{"Name":"integrity","Value":"sha256-SDryTuy3zd0/R12V7wEFO4vVQlxjVXFJy8p\u002B2jIKN7c="},{"Name":"label","Value":"_content/ViVu/images/banners/b\u1EBFn-tre.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"143664"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022SDryTuy3zd0/R12V7wEFO4vVQlxjVXFJy8p\u002B2jIKN7c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:55:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/bến-tre.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\bến-tre.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SDryTuy3zd0/R12V7wEFO4vVQlxjVXFJy8p\u002B2jIKN7c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"143664"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022SDryTuy3zd0/R12V7wEFO4vVQlxjVXFJy8p\u002B2jIKN7c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:55:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.lw6nk6b2t0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lw6nk6b2t0"},{"Name":"integrity","Value":"sha256-tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA="},{"Name":"label","Value":"_content/ViVu/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"964001"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022tR5QSIYqNGMhKbMr\u002BNavS3orLZr3YCia6W0G8Uv1TQA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"634938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.lgwnj7f1qr.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lgwnj7f1qr"},{"Name":"integrity","Value":"sha256-FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw="},{"Name":"label","Value":"_content/ViVu/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"634938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022FaMoSmCgxb0jdK22R/mcZBrw2YF2RdfF7ZDIxYq6DMw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-w6PkXnsxGdCAY\u002BkkR5CH5DTLT16zTO/D2cvRNwW8lQs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"511954"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022w6PkXnsxGdCAY\u002BkkR5CH5DTLT16zTO/D2cvRNwW8lQs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.xr1lgk8s6k.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xr1lgk8s6k"},{"Name":"integrity","Value":"sha256-w6PkXnsxGdCAY\u002BkkR5CH5DTLT16zTO/D2cvRNwW8lQs="},{"Name":"label","Value":"_content/ViVu/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"511954"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022w6PkXnsxGdCAY\u002BkkR5CH5DTLT16zTO/D2cvRNwW8lQs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/README.47o5cqoj5o.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"47o5cqoj5o"},{"Name":"integrity","Value":"sha256-wH1JrHrCZIUMo3JmqU2IcTjUA4KWmDfS\u002BTkJ86hXK54="},{"Name":"label","Value":"_content/ViVu/images/banners/README.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"79"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022wH1JrHrCZIUMo3JmqU2IcTjUA4KWmDfS\u002BTkJ86hXK54=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:53:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/README.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wH1JrHrCZIUMo3JmqU2IcTjUA4KWmDfS\u002BTkJ86hXK54="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"79"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022wH1JrHrCZIUMo3JmqU2IcTjUA4KWmDfS\u002BTkJ86hXK54=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:53:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/README_AI.npgoq9ygt0.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README_AI.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"npgoq9ygt0"},{"Name":"integrity","Value":"sha256-m6gswSODiVwiD0T6dOKfftj/qC4VeCnOWUePiH4zCzw="},{"Name":"label","Value":"_content/ViVu/images/banners/README_AI.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"344"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022m6gswSODiVwiD0T6dOKfftj/qC4VeCnOWUePiH4zCzw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 08 May 2025 14:50:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/banners/README_AI.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\banners\README_AI.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-m6gswSODiVwiD0T6dOKfftj/qC4VeCnOWUePiH4zCzw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"344"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022m6gswSODiVwiD0T6dOKfftj/qC4VeCnOWUePiH4zCzw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 08 May 2025 14:50:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/cuisine/amthucbentre.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\cuisine\amthucbentre.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:42:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/cuisine/amthucbentre.zfjffteuy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\cuisine\amthucbentre.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zfjffteuy9"},{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="},{"Name":"label","Value":"_content/ViVu/images/cuisine/amthucbentre.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:42:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/culture/doncataitur.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\culture\doncataitur.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:42:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/culture/doncataitur.zfjffteuy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\culture\doncataitur.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zfjffteuy9"},{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="},{"Name":"label","Value":"_content/ViVu/images/culture/doncataitur.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:42:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-activity.brjrgch06q.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-activity.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"brjrgch06q"},{"Name":"integrity","Value":"sha256-x2xrvfP4KRVMnAOFiSnEEmrsw3z5BSB\u002B0ovAy/PVX8w="},{"Name":"label","Value":"_content/ViVu/images/default/default-activity.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022x2xrvfP4KRVMnAOFiSnEEmrsw3z5BSB\u002B0ovAy/PVX8w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:15:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-activity.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-activity.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-x2xrvfP4KRVMnAOFiSnEEmrsw3z5BSB\u002B0ovAy/PVX8w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"48"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022x2xrvfP4KRVMnAOFiSnEEmrsw3z5BSB\u002B0ovAy/PVX8w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:15:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-hotel.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-luU7eMunRrfW1xFLdskKQjlQCMgqVaZ\u002BBcZ8LFN71xc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"45"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022luU7eMunRrfW1xFLdskKQjlQCMgqVaZ\u002BBcZ8LFN71xc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:16:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-hotel.ya2z8zzef9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-hotel.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ya2z8zzef9"},{"Name":"integrity","Value":"sha256-luU7eMunRrfW1xFLdskKQjlQCMgqVaZ\u002BBcZ8LFN71xc="},{"Name":"label","Value":"_content/ViVu/images/default/default-hotel.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"45"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022luU7eMunRrfW1xFLdskKQjlQCMgqVaZ\u002BBcZ8LFN71xc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:16:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-tour.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-tour.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8Vr8S1Cqb/yn46fy4tPr9vqxSGnseYdyZKxyl8/qU18="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"44"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228Vr8S1Cqb/yn46fy4tPr9vqxSGnseYdyZKxyl8/qU18=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:15:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/default/default-tour.rhlt30nd2j.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\default\default-tour.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rhlt30nd2j"},{"Name":"integrity","Value":"sha256-8Vr8S1Cqb/yn46fy4tPr9vqxSGnseYdyZKxyl8/qU18="},{"Name":"label","Value":"_content/ViVu/images/default/default-tour.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"44"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228Vr8S1Cqb/yn46fy4tPr9vqxSGnseYdyZKxyl8/qU18=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 15:15:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/conphung.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\conphung.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/conphung.zfjffteuy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\conphung.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zfjffteuy9"},{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="},{"Name":"label","Value":"_content/ViVu/images/destinations/conphung.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/crafts/craft_lang-keo-dua_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\crafts\craft_lang-keo-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"83415"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/crafts/craft_lang-keo-dua_01.l4orteeb3m.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\crafts\craft_lang-keo-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l4orteeb3m"},{"Name":"integrity","Value":"sha256-5Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo="},{"Name":"label","Value":"_content/ViVu/images/destinations/crafts/craft_lang-keo-dua_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"83415"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/craft_lang-keo-dua_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\craft_lang-keo-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"83415"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/craft_lang-keo-dua_01.l4orteeb3m.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\craft_lang-keo-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l4orteeb3m"},{"Name":"integrity","Value":"sha256-5Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo="},{"Name":"label","Value":"_content/ViVu/images/destinations/craft_lang-keo-dua_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"83415"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225Ydq9eSRsaknuNvjFimGRdpXhq\u002BAlwuRN2Ysd6PsMBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:58:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/cuisine/food_banh-trang-dua_01.68m6pn2o1o.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\cuisine\food_banh-trang-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"68m6pn2o1o"},{"Name":"integrity","Value":"sha256-\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ="},{"Name":"label","Value":"_content/ViVu/images/destinations/cuisine/food_banh-trang-dua_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"506799"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:59:26 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/cuisine/food_banh-trang-dua_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\cuisine\food_banh-trang-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"506799"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:59:26 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/culaothoison.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\culaothoison.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/culaothoison.zfjffteuy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\culaothoison.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zfjffteuy9"},{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="},{"Name":"label","Value":"_content/ViVu/images/destinations/culaothoison.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/dest_con-phung_01.fx7eukk01c.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\dest_con-phung_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fx7eukk01c"},{"Name":"integrity","Value":"sha256-XfKRl\u002Bh1hbiKdF6ZX5SgBxazH4VVkLydntalU/t7Ob4="},{"Name":"label","Value":"_content/ViVu/images/destinations/dest_con-phung_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16962"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022XfKRl\u002Bh1hbiKdF6ZX5SgBxazH4VVkLydntalU/t7Ob4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:57:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/dest_con-phung_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\dest_con-phung_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XfKRl\u002Bh1hbiKdF6ZX5SgBxazH4VVkLydntalU/t7Ob4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16962"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022XfKRl\u002Bh1hbiKdF6ZX5SgBxazH4VVkLydntalU/t7Ob4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:57:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/food_banh-trang-dua_01.68m6pn2o1o.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\food_banh-trang-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"68m6pn2o1o"},{"Name":"integrity","Value":"sha256-\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ="},{"Name":"label","Value":"_content/ViVu/images/destinations/food_banh-trang-dua_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"506799"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:59:26 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/food_banh-trang-dua_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\food_banh-trang-dua_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"506799"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002BrTgG2KRiZm5j5tqiLPmkD2\u002B4w19dXs917yApQi1qzQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:59:26 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/langnghedua.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\langnghedua.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/langnghedua.zfjffteuy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\langnghedua.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zfjffteuy9"},{"Name":"integrity","Value":"sha256-L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI="},{"Name":"label","Value":"_content/ViVu/images/destinations/langnghedua.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L3M0nPxGMCVTGcbI38G0aomWrOnRTY4HVjsWWRWRjsI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 10:43:13 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/README.h4lhfj2oe8.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\README.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h4lhfj2oe8"},{"Name":"integrity","Value":"sha256-z78W5fun6D3WeX/qVNwTEPw8RWg/oirm5V/XONwrZOg="},{"Name":"label","Value":"_content/ViVu/images/destinations/README.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3541"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022z78W5fun6D3WeX/qVNwTEPw8RWg/oirm5V/XONwrZOg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:29:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/README.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\README.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-z78W5fun6D3WeX/qVNwTEPw8RWg/oirm5V/XONwrZOg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3541"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022z78W5fun6D3WeX/qVNwTEPw8RWg/oirm5V/XONwrZOg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:29:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/vuoncaimon.g9pml4w7tk.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\vuoncaimon.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g9pml4w7tk"},{"Name":"integrity","Value":"sha256-nFF4rlb9LC\u002Bt\u002BVebxUo6td9VHH5V4NUcmZ8DXi3EedI="},{"Name":"label","Value":"_content/ViVu/images/destinations/vuoncaimon.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1221"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nFF4rlb9LC\u002Bt\u002BVebxUo6td9VHH5V4NUcmZ8DXi3EedI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:26:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/destinations/vuoncaimon.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\destinations\vuoncaimon.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nFF4rlb9LC\u002Bt\u002BVebxUo6td9VHH5V4NUcmZ8DXi3EedI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1221"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nFF4rlb9LC\u002Bt\u002BVebxUo6td9VHH5V4NUcmZ8DXi3EedI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:26:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/download.29t50sped2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\download.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"29t50sped2"},{"Name":"integrity","Value":"sha256-ViOj06v3M9wV/zLZ3EjrkZ/p6qH/6\u002BzTQSeobRjldtU="},{"Name":"label","Value":"_content/ViVu/images/download.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5314"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ViOj06v3M9wV/zLZ3EjrkZ/p6qH/6\u002BzTQSeobRjldtU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 03:38:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/download.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\download.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ViOj06v3M9wV/zLZ3EjrkZ/p6qH/6\u002BzTQSeobRjldtU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5314"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ViOj06v3M9wV/zLZ3EjrkZ/p6qH/6\u002BzTQSeobRjldtU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 03:38:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/experiences/exp_lang-hoa-cho-lach_01.dyphqtxwt0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\experiences\exp_lang-hoa-cho-lach_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dyphqtxwt0"},{"Name":"integrity","Value":"sha256-W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4="},{"Name":"label","Value":"_content/ViVu/images/experiences/exp_lang-hoa-cho-lach_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1106432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/experiences/exp_lang-hoa-cho-lach_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\experiences\exp_lang-hoa-cho-lach_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1106432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022W2S\u002BzGOtivyR2EHs\u002BKqoLB3/qjW8f70e0tB4\u002BrzSxD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/image1.874hy2cbfn.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\image1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"874hy2cbfn"},{"Name":"integrity","Value":"sha256-fKZqCHvJO3Lc3YTDoNQdKU5W2JP8H8UC/40jQa1xCnw="},{"Name":"label","Value":"_content/ViVu/images/image1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7274"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022fKZqCHvJO3Lc3YTDoNQdKU5W2JP8H8UC/40jQa1xCnw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 04:01:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/image1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\image1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fKZqCHvJO3Lc3YTDoNQdKU5W2JP8H8UC/40jQa1xCnw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7274"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022fKZqCHvJO3Lc3YTDoNQdKU5W2JP8H8UC/40jQa1xCnw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 04:01:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/Laptop.2y66sg7zog.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\Laptop.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2y66sg7zog"},{"Name":"integrity","Value":"sha256-Votf537DqzWjkDhYCvojc3rhRDKcYuCOLGNVMFTRj28="},{"Name":"label","Value":"_content/ViVu/images/Laptop.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7660"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Votf537DqzWjkDhYCvojc3rhRDKcYuCOLGNVMFTRj28=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 05:19:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/Laptop.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\Laptop.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Votf537DqzWjkDhYCvojc3rhRDKcYuCOLGNVMFTRj28="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7660"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Votf537DqzWjkDhYCvojc3rhRDKcYuCOLGNVMFTRj28=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 05:19:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.ii0mxv9slh.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ii0mxv9slh"},{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="},{"Name":"label","Value":"_content/ViVu/images/locations/13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:16:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\13b506a1-7341-4b2a-b13b-44abd672b1c1_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:16:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.gehn6ps2p2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gehn6ps2p2"},{"Name":"integrity","Value":"sha256-KD5oU5YpiYC/KrqOwTLSIhR22OFfgkWEAtUQIpnp/Wo="},{"Name":"label","Value":"_content/ViVu/images/locations/25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75110"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KD5oU5YpiYC/KrqOwTLSIhR22OFfgkWEAtUQIpnp/Wo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:23:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\25744a23-0125-4ef7-96e7-d5b697d20ab4_Khu-du-lich-sinh-thai-phu-an-khang-8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KD5oU5YpiYC/KrqOwTLSIhR22OFfgkWEAtUQIpnp/Wo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"75110"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022KD5oU5YpiYC/KrqOwTLSIhR22OFfgkWEAtUQIpnp/Wo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:23:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nISilFxEHsLybRq4I7WF0LDuIj3od0kSLTqnnZRrrL4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"38709"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nISilFxEHsLybRq4I7WF0LDuIj3od0kSLTqnnZRrrL4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:22:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).seh97pt6zn.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"seh97pt6zn"},{"Name":"integrity","Value":"sha256-nISilFxEHsLybRq4I7WF0LDuIj3od0kSLTqnnZRrrL4="},{"Name":"label","Value":"_content/ViVu/images/locations/35a05cd1-ccf6-468a-8364-39b9375361b0_OIP (2).jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"38709"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nISilFxEHsLybRq4I7WF0LDuIj3od0kSLTqnnZRrrL4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:22:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.ii0mxv9slh.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ii0mxv9slh"},{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="},{"Name":"label","Value":"_content/ViVu/images/locations/4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:11:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\4e78006f-9cc6-48d8-b3eb-d24cf9add702_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:11:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"79867"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:20:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).vee31lktom.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vee31lktom"},{"Name":"integrity","Value":"sha256-twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk="},{"Name":"label","Value":"_content/ViVu/images/locations/6ab978da-cce9-421d-b908-002568593546_Khu-du-lich-Lang-Be (1).jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"79867"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:20:38 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.4kuqo9z1lu.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4kuqo9z1lu"},{"Name":"integrity","Value":"sha256-zJb7FheE2vOkqBArT6LA7eZNrsTXH297lbNkK8bFa4k="},{"Name":"label","Value":"_content/ViVu/images/locations/7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"695892"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022zJb7FheE2vOkqBArT6LA7eZNrsTXH297lbNkK8bFa4k=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:24:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\7c5ad11a-5f58-4580-a5b7-dee888818c90_13-thodon1-16876978730001063951110.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zJb7FheE2vOkqBArT6LA7eZNrsTXH297lbNkK8bFa4k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"695892"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022zJb7FheE2vOkqBArT6LA7eZNrsTXH297lbNkK8bFa4k=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:24:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dNeLBuJwuDfkgpgW075u\u002BccX7dzNWEpKm/IjLlrRpGE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"98213"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022dNeLBuJwuDfkgpgW075u\u002BccX7dzNWEpKm/IjLlrRpGE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:21:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.s8deirdmoq.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"s8deirdmoq"},{"Name":"integrity","Value":"sha256-dNeLBuJwuDfkgpgW075u\u002BccX7dzNWEpKm/IjLlrRpGE="},{"Name":"label","Value":"_content/ViVu/images/locations/915e8dbf-3516-4d2f-a712-ca66f8550619_p6etb8ywt9u4nqgk9qbq.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"98213"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022dNeLBuJwuDfkgpgW075u\u002BccX7dzNWEpKm/IjLlrRpGE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:21:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OqGnZmR5ceX3jgFbD2mUG4R2k/rJtVJAKhLqKZQvkuo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"86956"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022OqGnZmR5ceX3jgFbD2mUG4R2k/rJtVJAKhLqKZQvkuo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:22:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.qr6ycrn3gi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qr6ycrn3gi"},{"Name":"integrity","Value":"sha256-OqGnZmR5ceX3jgFbD2mUG4R2k/rJtVJAKhLqKZQvkuo="},{"Name":"label","Value":"_content/ViVu/images/locations/9298edd9-117b-4235-9fc3-cb57ee9a20b6_Kham-pha-Khu-du-lich-Bien-Ba-Dong-Tra-Vinh-2022-930x620.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"86956"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022OqGnZmR5ceX3jgFbD2mUG4R2k/rJtVJAKhLqKZQvkuo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:22:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/92f54106-9211-4d39-aa73-ea16167c52c2_R.jaom6mqkix.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\92f54106-9211-4d39-aa73-ea16167c52c2_R.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jaom6mqkix"},{"Name":"integrity","Value":"sha256-hV3xHVzONE7MMWHOeVo6IAeVaRBV9LYMalzSte9Lc3Y="},{"Name":"label","Value":"_content/ViVu/images/locations/92f54106-9211-4d39-aa73-ea16167c52c2_R.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"223754"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hV3xHVzONE7MMWHOeVo6IAeVaRBV9LYMalzSte9Lc3Y=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:21:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/92f54106-9211-4d39-aa73-ea16167c52c2_R.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\92f54106-9211-4d39-aa73-ea16167c52c2_R.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hV3xHVzONE7MMWHOeVo6IAeVaRBV9LYMalzSte9Lc3Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"223754"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hV3xHVzONE7MMWHOeVo6IAeVaRBV9LYMalzSte9Lc3Y=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:21:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.ii0mxv9slh.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ii0mxv9slh"},{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="},{"Name":"label","Value":"_content/ViVu/images/locations/94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:19:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\94b2dea5-7e03-498d-a8a7-a45a4e8438ef_khu-du-lich-con-phung-4-1024x682.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"141350"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022GtnGMClovl5LjvDj4IvVm30pSy5ybRqS1Xd/xJjxWBE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:19:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ISt8Z6rDHHtjv/IInlAWWR50lX2Ow6972iUEPP2V6v4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"98547"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ISt8Z6rDHHtjv/IInlAWWR50lX2Ow6972iUEPP2V6v4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:23:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/locations/a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).xkzopq8y0y.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\locations\a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xkzopq8y0y"},{"Name":"integrity","Value":"sha256-ISt8Z6rDHHtjv/IInlAWWR50lX2Ow6972iUEPP2V6v4="},{"Name":"label","Value":"_content/ViVu/images/locations/a56bc584-8d6a-4110-b6f3-9337f9980019_20161020080406-vuon-trai-cay-cai-mon-gody (5).jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"98547"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ISt8Z6rDHHtjv/IInlAWWR50lX2Ow6972iUEPP2V6v4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 14:23:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/msi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\msi.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8QWcqS/HPfJBBVVYKjLelEVQJiqbEvytWPkUl8A3N1I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6798"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228QWcqS/HPfJBBVVYKjLelEVQJiqbEvytWPkUl8A3N1I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 26 Feb 2025 04:11:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/msi.xgtz2xutzj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\msi.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xgtz2xutzj"},{"Name":"integrity","Value":"sha256-8QWcqS/HPfJBBVVYKjLelEVQJiqbEvytWPkUl8A3N1I="},{"Name":"label","Value":"_content/ViVu/images/msi.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6798"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228QWcqS/HPfJBBVVYKjLelEVQJiqbEvytWPkUl8A3N1I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 26 Feb 2025 04:11:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/20839908-55fe-4bde-a806-267056a30d42.061ohg3dcj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\20839908-55fe-4bde-a806-267056a30d42.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"061ohg3dcj"},{"Name":"integrity","Value":"sha256-GIPCwPA4fXDTxqT1pzdsyVaqTkhvej4wCwna\u002BPCpR04="},{"Name":"label","Value":"_content/ViVu/images/panoramas/20839908-55fe-4bde-a806-267056a30d42.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"778422"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GIPCwPA4fXDTxqT1pzdsyVaqTkhvej4wCwna\u002BPCpR04=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:08:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/20839908-55fe-4bde-a806-267056a30d42.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\20839908-55fe-4bde-a806-267056a30d42.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GIPCwPA4fXDTxqT1pzdsyVaqTkhvej4wCwna\u002BPCpR04="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"778422"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GIPCwPA4fXDTxqT1pzdsyVaqTkhvej4wCwna\u002BPCpR04=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:08:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/4c2df1da-5385-4201-af7c-dd42540b3a07.69jfowzfhs.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\4c2df1da-5385-4201-af7c-dd42540b3a07.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"69jfowzfhs"},{"Name":"integrity","Value":"sha256-xmiDnWEIW2nZVafbVRS8KUrqiW0PdAPfLByBIbyDse4="},{"Name":"label","Value":"_content/ViVu/images/panoramas/4c2df1da-5385-4201-af7c-dd42540b3a07.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"60470"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022xmiDnWEIW2nZVafbVRS8KUrqiW0PdAPfLByBIbyDse4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:41:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/4c2df1da-5385-4201-af7c-dd42540b3a07.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\4c2df1da-5385-4201-af7c-dd42540b3a07.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xmiDnWEIW2nZVafbVRS8KUrqiW0PdAPfLByBIbyDse4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"60470"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022xmiDnWEIW2nZVafbVRS8KUrqiW0PdAPfLByBIbyDse4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:41:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"565176"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:05:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/6af366fa-0503-4b30-9baf-ad60ee0e30da.pvxwnlymcu.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pvxwnlymcu"},{"Name":"integrity","Value":"sha256-hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g="},{"Name":"label","Value":"_content/ViVu/images/panoramas/6af366fa-0503-4b30-9baf-ad60ee0e30da.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"565176"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:05:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/c83348e1-5da8-4856-9657-6872e8210b4e.9vxkbw864s.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\c83348e1-5da8-4856-9657-6872e8210b4e.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9vxkbw864s"},{"Name":"integrity","Value":"sha256-i6yQNCM8AKWDhC24QNcGaWc\u002B0MbKSxlhlWYQV/K4qCI="},{"Name":"label","Value":"_content/ViVu/images/panoramas/c83348e1-5da8-4856-9657-6872e8210b4e.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"110175"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022i6yQNCM8AKWDhC24QNcGaWc\u002B0MbKSxlhlWYQV/K4qCI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:09:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/c83348e1-5da8-4856-9657-6872e8210b4e.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\c83348e1-5da8-4856-9657-6872e8210b4e.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-i6yQNCM8AKWDhC24QNcGaWc\u002B0MbKSxlhlWYQV/K4qCI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"110175"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022i6yQNCM8AKWDhC24QNcGaWc\u002B0MbKSxlhlWYQV/K4qCI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:09:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/panorama-banner.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\panorama-banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"565176"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:00:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/panoramas/panorama-banner.pvxwnlymcu.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\panoramas\panorama-banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pvxwnlymcu"},{"Name":"integrity","Value":"sha256-hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g="},{"Name":"label","Value":"_content/ViVu/images/panoramas/panorama-banner.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"565176"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022hf5B43Ym7LRV\u002ByC\u002BRUrU0pDeACM8cMpWHxL7WEgQb4g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:00:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:00:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/27415b2e-9f44-4f37-a82c-cc7fe7f88057.vjdf3zcc1l.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vjdf3zcc1l"},{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="},{"Name":"label","Value":"_content/ViVu/images/services/27415b2e-9f44-4f37-a82c-cc7fe7f88057.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:00:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/34c294a6-de82-4bec-904c-ac56320e62d3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\34c294a6-de82-4bec-904c-ac56320e62d3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-P\u002BfLMkPe/6dxE9\u002BS\u002BJdSgwZALIaAiXDV\u002B1mfrBBJ1vY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"159483"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022P\u002BfLMkPe/6dxE9\u002BS\u002BJdSgwZALIaAiXDV\u002B1mfrBBJ1vY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:03:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/34c294a6-de82-4bec-904c-ac56320e62d3.r53tyor372.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\34c294a6-de82-4bec-904c-ac56320e62d3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r53tyor372"},{"Name":"integrity","Value":"sha256-P\u002BfLMkPe/6dxE9\u002BS\u002BJdSgwZALIaAiXDV\u002B1mfrBBJ1vY="},{"Name":"label","Value":"_content/ViVu/images/services/34c294a6-de82-4bec-904c-ac56320e62d3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"159483"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022P\u002BfLMkPe/6dxE9\u002BS\u002BJdSgwZALIaAiXDV\u002B1mfrBBJ1vY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:03:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:01:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/7d962f34-7b8b-40c0-870f-39cdb95c188f.vjdf3zcc1l.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vjdf3zcc1l"},{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="},{"Name":"label","Value":"_content/ViVu/images/services/7d962f34-7b8b-40c0-870f-39cdb95c188f.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:01:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/b9743ea5-5728-468f-8fb2-600321c6f203.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\b9743ea5-5728-468f-8fb2-600321c6f203.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:01:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/b9743ea5-5728-468f-8fb2-600321c6f203.vjdf3zcc1l.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\b9743ea5-5728-468f-8fb2-600321c6f203.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vjdf3zcc1l"},{"Name":"integrity","Value":"sha256-pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM="},{"Name":"label","Value":"_content/ViVu/images/services/b9743ea5-5728-468f-8fb2-600321c6f203.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"167553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pf7OmOh/YaaDzLXSNQIzExE9dp6NfoRAU7caa4HKUIM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:01:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Cm3nYWnZQ7EldsxQfA6CdA6nYLHCXD/ib5vD31B3eQ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"159831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Cm3nYWnZQ7EldsxQfA6CdA6nYLHCXD/ib5vD31B3eQ0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:03:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.ujwpgnn702.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ujwpgnn702"},{"Name":"integrity","Value":"sha256-Cm3nYWnZQ7EldsxQfA6CdA6nYLHCXD/ib5vD31B3eQ0="},{"Name":"label","Value":"_content/ViVu/images/services/edcb0bc2-01c4-4a20-8bfa-2e52a70fc8a0.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"159831"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Cm3nYWnZQ7EldsxQfA6CdA6nYLHCXD/ib5vD31B3eQ0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:03:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:43:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/faf3f9f7-d0bd-4909-98f3-3e323e0801cf.tonji5updp.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tonji5updp"},{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="},{"Name":"label","Value":"_content/ViVu/images/services/faf3f9f7-d0bd-4909-98f3-3e323e0801cf.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:43:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/fe561fa6-3db1-4eca-9096-e16971e24fec.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\fe561fa6-3db1-4eca-9096-e16971e24fec.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mLvXp32i5LhQttOD5VwPz\u002B1S8mtBj7AcwoGa4eidaec="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"302697"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mLvXp32i5LhQttOD5VwPz\u002B1S8mtBj7AcwoGa4eidaec=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:02:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/services/fe561fa6-3db1-4eca-9096-e16971e24fec.oc5edvure5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\services\fe561fa6-3db1-4eca-9096-e16971e24fec.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oc5edvure5"},{"Name":"integrity","Value":"sha256-mLvXp32i5LhQttOD5VwPz\u002B1S8mtBj7AcwoGa4eidaec="},{"Name":"label","Value":"_content/ViVu/images/services/fe561fa6-3db1-4eca-9096-e16971e24fec.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"302697"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022mLvXp32i5LhQttOD5VwPz\u002B1S8mtBj7AcwoGa4eidaec=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:02:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/125de0e6-f758-41a2-a16c-2efe7d3d96fc.5t177jw6gk.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5t177jw6gk"},{"Name":"integrity","Value":"sha256-F\u002BjFMQywCxTdHTWOzXHqlHYJfR3m1O/JWnTUcXeT9RU="},{"Name":"label","Value":"_content/ViVu/images/tours/125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1195084"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022F\u002BjFMQywCxTdHTWOzXHqlHYJfR3m1O/JWnTUcXeT9RU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\125de0e6-f758-41a2-a16c-2efe7d3d96fc.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-F\u002BjFMQywCxTdHTWOzXHqlHYJfR3m1O/JWnTUcXeT9RU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1195084"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022F\u002BjFMQywCxTdHTWOzXHqlHYJfR3m1O/JWnTUcXeT9RU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:35:41 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/3e48eb6f-744e-4e5a-88ad-9bc4bc02face.tonji5updp.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tonji5updp"},{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="},{"Name":"label","Value":"_content/ViVu/images/tours/3e48eb6f-744e-4e5a-88ad-9bc4bc02face.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:35:41 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/4fa99da7-7971-4e69-87bf-05c7977224b0.f4f5a2wze7.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\4fa99da7-7971-4e69-87bf-05c7977224b0.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"f4f5a2wze7"},{"Name":"integrity","Value":"sha256-MdJnkm10dSTVtZooPK7ZZ\u002B82HywTCjrCuD/EuDPgQxU="},{"Name":"label","Value":"_content/ViVu/images/tours/4fa99da7-7971-4e69-87bf-05c7977224b0.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"299078"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MdJnkm10dSTVtZooPK7ZZ\u002B82HywTCjrCuD/EuDPgQxU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:04:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/4fa99da7-7971-4e69-87bf-05c7977224b0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\4fa99da7-7971-4e69-87bf-05c7977224b0.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MdJnkm10dSTVtZooPK7ZZ\u002B82HywTCjrCuD/EuDPgQxU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"299078"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MdJnkm10dSTVtZooPK7ZZ\u002B82HywTCjrCuD/EuDPgQxU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:04:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/91e28151-4091-444b-b82b-7e1fc90f6dc8.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\91e28151-4091-444b-b82b-7e1fc90f6dc8.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hppLxTgCgpnL8HkuAS0NdyxKTzaCUDLjsOYPskGmoTU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"81254"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022hppLxTgCgpnL8HkuAS0NdyxKTzaCUDLjsOYPskGmoTU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/91e28151-4091-444b-b82b-7e1fc90f6dc8.ym8fgfuch8.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\91e28151-4091-444b-b82b-7e1fc90f6dc8.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ym8fgfuch8"},{"Name":"integrity","Value":"sha256-hppLxTgCgpnL8HkuAS0NdyxKTzaCUDLjsOYPskGmoTU="},{"Name":"label","Value":"_content/ViVu/images/tours/91e28151-4091-444b-b82b-7e1fc90f6dc8.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"81254"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022hppLxTgCgpnL8HkuAS0NdyxKTzaCUDLjsOYPskGmoTU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"79867"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/9d8ad0e5-c85b-4590-ae72-a41c4d447509.vee31lktom.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vee31lktom"},{"Name":"integrity","Value":"sha256-twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk="},{"Name":"label","Value":"_content/ViVu/images/tours/9d8ad0e5-c85b-4590-ae72-a41c4d447509.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"79867"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022twCmk5gkibAqLSIxXinFgUUxjU9d17NzwiLXxgdA3bk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:05:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/anh-ben-tre-dep-binh-di_022739098.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\anh-ben-tre-dep-binh-di_022739098.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1095986"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00223EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/anh-ben-tre-dep-binh-di_022739098.sgk5wnnwwu.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\anh-ben-tre-dep-binh-di_022739098.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sgk5wnnwwu"},{"Name":"integrity","Value":"sha256-3EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY="},{"Name":"label","Value":"_content/ViVu/images/tours/anh-ben-tre-dep-binh-di_022739098.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1095986"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00223EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wzoGCxoHCzpKt9OZ85gK0vf7l78nMsLftb525udpA94="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"117970"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wzoGCxoHCzpKt9OZ85gK0vf7l78nMsLftb525udpA94=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _01.zkphb1h05s.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zkphb1h05s"},{"Name":"integrity","Value":"sha256-wzoGCxoHCzpKt9OZ85gK0vf7l78nMsLftb525udpA94="},{"Name":"label","Value":"_content/ViVu/images/tours/Authentic Discover the Mekong Delta\u0027s Charms _01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"117970"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wzoGCxoHCzpKt9OZ85gK0vf7l78nMsLftb525udpA94=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _09.4ac2glevd9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _09.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ac2glevd9"},{"Name":"integrity","Value":"sha256-VDG3bjN5H27vT248AzMxEkLis\u002Bpl7YzxLdZh8UHwAWs="},{"Name":"label","Value":"_content/ViVu/images/tours/Authentic Discover the Mekong Delta\u0027s Charms _09.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"136496"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VDG3bjN5H27vT248AzMxEkLis\u002Bpl7YzxLdZh8UHwAWs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _09.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _09.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VDG3bjN5H27vT248AzMxEkLis\u002Bpl7YzxLdZh8UHwAWs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"136496"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VDG3bjN5H27vT248AzMxEkLis\u002Bpl7YzxLdZh8UHwAWs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _92.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _92.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ur6ZzOK40pyRgwJRjcYMop2knD/flhzntC5k/VP6yPM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"96964"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ur6ZzOK40pyRgwJRjcYMop2knD/flhzntC5k/VP6yPM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _92.uiqcsdw1xh.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _92.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uiqcsdw1xh"},{"Name":"integrity","Value":"sha256-ur6ZzOK40pyRgwJRjcYMop2knD/flhzntC5k/VP6yPM="},{"Name":"label","Value":"_content/ViVu/images/tours/Authentic Discover the Mekong Delta\u0027s Charms _92.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"96964"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ur6ZzOK40pyRgwJRjcYMop2knD/flhzntC5k/VP6yPM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _a6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _a6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G1YzfKsCsVsaFoXn7O6bQmVyudLeeVmMIhmOweTqeLA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"58413"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022G1YzfKsCsVsaFoXn7O6bQmVyudLeeVmMIhmOweTqeLA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Authentic Discover the Mekong Delta's Charms _a6.n1me3r0vj8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Authentic Discover the Mekong Delta's Charms _a6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n1me3r0vj8"},{"Name":"integrity","Value":"sha256-G1YzfKsCsVsaFoXn7O6bQmVyudLeeVmMIhmOweTqeLA="},{"Name":"label","Value":"_content/ViVu/images/tours/Authentic Discover the Mekong Delta\u0027s Charms _a6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"58413"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022G1YzfKsCsVsaFoXn7O6bQmVyudLeeVmMIhmOweTqeLA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:55:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/banners/tour_kham-pha-ben-tre_banner.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\banners\tour_kham-pha-ben-tre_banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1095986"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00223EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/banners/tour_kham-pha-ben-tre_banner.sgk5wnnwwu.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\banners\tour_kham-pha-ben-tre_banner.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sgk5wnnwwu"},{"Name":"integrity","Value":"sha256-3EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY="},{"Name":"label","Value":"_content/ViVu/images/tours/banners/tour_kham-pha-ben-tre_banner.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1095986"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00223EIXHh3uswgGf9S3z0/Vz289sv\u002BftDvteB1C9RBMIKY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"73135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.rq26cv2yqe.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rq26cv2yqe"},{"Name":"integrity","Value":"sha256-Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"73135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"120471"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.r4q3mv2yy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r4q3mv2yy9"},{"Name":"integrity","Value":"sha256-C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_26.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"120471"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"121326"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.rqw29wzbrh.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rqw29wzbrh"},{"Name":"integrity","Value":"sha256-vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_53.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"121326"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8wEHM\u002Bp4qpswg6jHFbiikYbPM4MQ4TNAcwAd471OKNE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"123220"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228wEHM\u002Bp4qpswg6jHFbiikYbPM4MQ4TNAcwAd471OKNE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.ruq0evhfkn.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ruq0evhfkn"},{"Name":"integrity","Value":"sha256-8wEHM\u002Bp4qpswg6jHFbiikYbPM4MQ4TNAcwAd471OKNE="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_5c.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"123220"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00228wEHM\u002Bp4qpswg6jHFbiikYbPM4MQ4TNAcwAd471OKNE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ndh4/Ut0Q6S3hvVjK9npogwFaDkI4Vihx3c4Xu5jbYc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"100706"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Ndh4/Ut0Q6S3hvVjK9npogwFaDkI4Vihx3c4Xu5jbYc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.n5k33b75u1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n5k33b75u1"},{"Name":"integrity","Value":"sha256-Ndh4/Ut0Q6S3hvVjK9npogwFaDkI4Vihx3c4Xu5jbYc="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_87.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"100706"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Ndh4/Ut0Q6S3hvVjK9npogwFaDkI4Vihx3c4Xu5jbYc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ppqSrwVqC1iMtJH6fauzAm8ek97v6dSdsuG1draQ4GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"101874"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ppqSrwVqC1iMtJH6fauzAm8ek97v6dSdsuG1draQ4GQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.uzdq4eygzy.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uzdq4eygzy"},{"Name":"integrity","Value":"sha256-ppqSrwVqC1iMtJH6fauzAm8ek97v6dSdsuG1draQ4GQ="},{"Name":"label","Value":"_content/ViVu/images/tours/Ben Tre Half Day Tour with Scooter, Sailboat and Mekong Food_e2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"101874"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ppqSrwVqC1iMtJH6fauzAm8ek97v6dSdsuG1draQ4GQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:54:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"120471"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_01.r4q3mv2yy9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r4q3mv2yy9"},{"Name":"integrity","Value":"sha256-C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg="},{"Name":"label","Value":"_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"120471"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022C/lYF8T1crYghEV\u002B/Zyh\u002B16pTwC\u002BOVG35mufLQOUgTg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_02.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"121326"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_02.rqw29wzbrh.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\details\tour_ben-tre-half-day_detail_02.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rqw29wzbrh"},{"Name":"integrity","Value":"sha256-vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M="},{"Name":"label","Value":"_content/ViVu/images/tours/details/tour_ben-tre-half-day_detail_02.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"121326"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vdTH9LxMVB/7vl2rJ1Y6Ddqx0SsqkBB8Lqyn8x91\u002B9M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"114020"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour.w38givrlmx.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w38givrlmx"},{"Name":"integrity","Value":"sha256-9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0="},{"Name":"label","Value":"_content/ViVu/images/tours/Mekong Delta Small-Group Tour.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"114020"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cHJBN/W/EiLli\u002B4ifMrkIcIBBs\u002B5srTMZS491Vo2txQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"112512"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022cHJBN/W/EiLli\u002B4ifMrkIcIBBs\u002B5srTMZS491Vo2txQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour2.om2dn28cjz.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"om2dn28cjz"},{"Name":"integrity","Value":"sha256-cHJBN/W/EiLli\u002B4ifMrkIcIBBs\u002B5srTMZS491Vo2txQ="},{"Name":"label","Value":"_content/ViVu/images/tours/Mekong Delta Small-Group Tour2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"112512"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022cHJBN/W/EiLli\u002B4ifMrkIcIBBs\u002B5srTMZS491Vo2txQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour_a1.hi4qg12pv3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour_a1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hi4qg12pv3"},{"Name":"integrity","Value":"sha256-p1J7Zjc296LSHUepeJk2HrMcm9qUD\u002ButpWPW\u002BUy2sFU="},{"Name":"label","Value":"_content/ViVu/images/tours/Mekong Delta Small-Group Tour_a1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"118256"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022p1J7Zjc296LSHUepeJk2HrMcm9qUD\u002ButpWPW\u002BUy2sFU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:01 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/Mekong Delta Small-Group Tour_a1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\Mekong Delta Small-Group Tour_a1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p1J7Zjc296LSHUepeJk2HrMcm9qUD\u002ButpWPW\u002BUy2sFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"118256"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022p1J7Zjc296LSHUepeJk2HrMcm9qUD\u002ButpWPW\u002BUy2sFU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:01 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/R.0gy8pipkc5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\R.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0gy8pipkc5"},{"Name":"integrity","Value":"sha256-YDG8i1JDVPUN6oXrlaHBR4A1ufoidIAG\u002BJ037mkFR/w="},{"Name":"label","Value":"_content/ViVu/images/tours/R.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"625363"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022YDG8i1JDVPUN6oXrlaHBR4A1ufoidIAG\u002BJ037mkFR/w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/R.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\R.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YDG8i1JDVPUN6oXrlaHBR4A1ufoidIAG\u002BJ037mkFR/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"625363"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022YDG8i1JDVPUN6oXrlaHBR4A1ufoidIAG\u002BJ037mkFR/w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:58:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/README.on25or1ax7.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\README.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"on25or1ax7"},{"Name":"integrity","Value":"sha256-TG97ZpeVyYXXUUSe\u002BvlckUry/GxlZ0aBZHUOxZX\u002BNmg="},{"Name":"label","Value":"_content/ViVu/images/tours/README.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"188"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022TG97ZpeVyYXXUUSe\u002BvlckUry/GxlZ0aBZHUOxZX\u002BNmg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/README.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\README.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-TG97ZpeVyYXXUUSe\u002BvlckUry/GxlZ0aBZHUOxZX\u002BNmg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"188"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022TG97ZpeVyYXXUUSe\u002BvlckUry/GxlZ0aBZHUOxZX\u002BNmg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:57:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/tour_ben-tre-half-day_thumb_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_ben-tre-half-day_thumb_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"73135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/tour_ben-tre-half-day_thumb_01.rq26cv2yqe.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_ben-tre-half-day_thumb_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rq26cv2yqe"},{"Name":"integrity","Value":"sha256-Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw="},{"Name":"label","Value":"_content/ViVu/images/tours/tour_ben-tre-half-day_thumb_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"73135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Y1v2VxByfJ8DGLHYQdBCv5cU30unlSryw9KgGoQPsgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:53:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/tour_mekong-delta-group_thumb_01.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_mekong-delta-group_thumb_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"114020"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/tours/tour_mekong-delta-group_thumb_01.w38givrlmx.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\tours\tour_mekong-delta-group_thumb_01.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w38givrlmx"},{"Name":"integrity","Value":"sha256-9FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0="},{"Name":"label","Value":"_content/ViVu/images/tours/tour_mekong-delta-group_thumb_01.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"114020"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00229FQxUa/TW9XSQFr1VuDw6EGW6fLIRjVTuNJYNGBeQY0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 09:52:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/03c0af66-a785-459b-a4da-3823e3d2c50a.9o8vhjcf54.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\03c0af66-a785-459b-a4da-3823e3d2c50a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9o8vhjcf54"},{"Name":"integrity","Value":"sha256-V2XcApsRMwmOpdZL0CTAZT8HHitVn2Nj14t9J8bBOAE="},{"Name":"label","Value":"_content/ViVu/images/vehicles/03c0af66-a785-459b-a4da-3823e3d2c50a.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"36463"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022V2XcApsRMwmOpdZL0CTAZT8HHitVn2Nj14t9J8bBOAE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:06:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/03c0af66-a785-459b-a4da-3823e3d2c50a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\03c0af66-a785-459b-a4da-3823e3d2c50a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-V2XcApsRMwmOpdZL0CTAZT8HHitVn2Nj14t9J8bBOAE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"36463"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022V2XcApsRMwmOpdZL0CTAZT8HHitVn2Nj14t9J8bBOAE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:06:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Mlz27GbK5ceXn/uDVBhaY7uDMAAYcg2wtqB0pTWYqcM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"648808"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Mlz27GbK5ceXn/uDVBhaY7uDMAAYcg2wtqB0pTWYqcM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/0a39189c-f495-4ed5-937e-7b09f2a36c8a.uscgjnd228.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uscgjnd228"},{"Name":"integrity","Value":"sha256-Mlz27GbK5ceXn/uDVBhaY7uDMAAYcg2wtqB0pTWYqcM="},{"Name":"label","Value":"_content/ViVu/images/vehicles/0a39189c-f495-4ed5-937e-7b09f2a36c8a.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"648808"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Mlz27GbK5ceXn/uDVBhaY7uDMAAYcg2wtqB0pTWYqcM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/11919ea3-b66c-45a0-a15b-b61391605099.th01zfywpn.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\11919ea3-b66c-45a0-a15b-b61391605099.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"th01zfywpn"},{"Name":"integrity","Value":"sha256-P4iRFksoHQKEVgTfmdU2RFvoXa/5TvXtPcI3x9WU2Tk="},{"Name":"label","Value":"_content/ViVu/images/vehicles/11919ea3-b66c-45a0-a15b-b61391605099.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"264985"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022P4iRFksoHQKEVgTfmdU2RFvoXa/5TvXtPcI3x9WU2Tk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/11919ea3-b66c-45a0-a15b-b61391605099.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\11919ea3-b66c-45a0-a15b-b61391605099.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-P4iRFksoHQKEVgTfmdU2RFvoXa/5TvXtPcI3x9WU2Tk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"264985"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022P4iRFksoHQKEVgTfmdU2RFvoXa/5TvXtPcI3x9WU2Tk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.o2bdksavzm.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o2bdksavzm"},{"Name":"integrity","Value":"sha256-sKLgnwt7oabio3V7OR7q4Rpa\u002BfdE2nNh75j7vJqmjBg="},{"Name":"label","Value":"_content/ViVu/images/vehicles/4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"466198"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022sKLgnwt7oabio3V7OR7q4Rpa\u002BfdE2nNh75j7vJqmjBg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:09:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\4affa6ca-fc4f-4fa2-9d3f-0e3a9064a3e6.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sKLgnwt7oabio3V7OR7q4Rpa\u002BfdE2nNh75j7vJqmjBg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"466198"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022sKLgnwt7oabio3V7OR7q4Rpa\u002BfdE2nNh75j7vJqmjBg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:09:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/638ca0d6-49a4-403c-b57d-b50222fcdd1e.tkhlzxatyc.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tkhlzxatyc"},{"Name":"integrity","Value":"sha256-w6yqs3faQ/a7H5BjgYmRD8Dy98RPsyE5O5OAAsGMRlo="},{"Name":"label","Value":"_content/ViVu/images/vehicles/638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"335778"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022w6yqs3faQ/a7H5BjgYmRD8Dy98RPsyE5O5OAAsGMRlo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:07:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\638ca0d6-49a4-403c-b57d-b50222fcdd1e.webp'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-w6yqs3faQ/a7H5BjgYmRD8Dy98RPsyE5O5OAAsGMRlo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"335778"},{"Name":"Content-Type","Value":"image/webp"},{"Name":"ETag","Value":"\u0022w6yqs3faQ/a7H5BjgYmRD8Dy98RPsyE5O5OAAsGMRlo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:07:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/a5f5ea33-2b45-4af5-a152-b96f332d6041.duzffxyobf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"duzffxyobf"},{"Name":"integrity","Value":"sha256-y\u002BnHrUXx42mJla77VYoWkbeJ0KdmfMyr1xbj36jWXYQ="},{"Name":"label","Value":"_content/ViVu/images/vehicles/a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"758659"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022y\u002BnHrUXx42mJla77VYoWkbeJ0KdmfMyr1xbj36jWXYQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\a5f5ea33-2b45-4af5-a152-b96f332d6041.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-y\u002BnHrUXx42mJla77VYoWkbeJ0KdmfMyr1xbj36jWXYQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"758659"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022y\u002BnHrUXx42mJla77VYoWkbeJ0KdmfMyr1xbj36jWXYQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 10:08:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:43:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/images/vehicles/c0a7c16a-63b0-4bf0-9623-36d4800b7834.tonji5updp.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\vehicles\c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tonji5updp"},{"Name":"integrity","Value":"sha256-raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs="},{"Name":"label","Value":"_content/ViVu/images/vehicles/c0a7c16a-63b0-4bf0-9623-36d4800b7834.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192322"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022raSZQ4\u002B\u002Bod5SwCZz9X8cHIolNjO0dNuok90ZJbC7wqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:43:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/ai-loading.4ei8dbqzam.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\ai-loading.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ei8dbqzam"},{"Name":"integrity","Value":"sha256-HKi09\u002BHafG9llcjyHn\u002BUIQUn9g3Bh\u002BvcsXQ0TRInvvo="},{"Name":"label","Value":"_content/ViVu/js/ai-loading.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2706"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HKi09\u002BHafG9llcjyHn\u002BUIQUn9g3Bh\u002BvcsXQ0TRInvvo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 10:14:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/ai-loading.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\ai-loading.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HKi09\u002BHafG9llcjyHn\u002BUIQUn9g3Bh\u002BvcsXQ0TRInvvo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2706"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HKi09\u002BHafG9llcjyHn\u002BUIQUn9g3Bh\u002BvcsXQ0TRInvvo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 10:14:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/disable-price-validation.h1w5hvpvrt.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-price-validation.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h1w5hvpvrt"},{"Name":"integrity","Value":"sha256-dQ6jtBm41jM\u002Bbu3lA3KB0pSKIkhlCIn\u002BFnnMjiVGxVg="},{"Name":"label","Value":"_content/ViVu/js/disable-price-validation.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1642"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dQ6jtBm41jM\u002Bbu3lA3KB0pSKIkhlCIn\u002BFnnMjiVGxVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:59:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/disable-price-validation.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-price-validation.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dQ6jtBm41jM\u002Bbu3lA3KB0pSKIkhlCIn\u002BFnnMjiVGxVg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1642"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dQ6jtBm41jM\u002Bbu3lA3KB0pSKIkhlCIn\u002BFnnMjiVGxVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 09:59:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/googleMapsConfig.h2cdso25g9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\googleMapsConfig.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h2cdso25g9"},{"Name":"integrity","Value":"sha256-pyWAbKv4FgSS8A66q1Sn0/vCzFi/L4AfHHubo9fp/hk="},{"Name":"label","Value":"_content/ViVu/js/googleMapsConfig.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1660"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pyWAbKv4FgSS8A66q1Sn0/vCzFi/L4AfHHubo9fp/hk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 05:18:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/googleMapsConfig.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\googleMapsConfig.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pyWAbKv4FgSS8A66q1Sn0/vCzFi/L4AfHHubo9fp/hk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1660"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pyWAbKv4FgSS8A66q1Sn0/vCzFi/L4AfHHubo9fp/hk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 05:18:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/pannellum.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\pannellum.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UMrBqZJuye13N2eIjAWEBoc2/wtmiWRr9AsuHHiRFjI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"219"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022UMrBqZJuye13N2eIjAWEBoc2/wtmiWRr9AsuHHiRFjI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:06:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/pannellum.min.k35ysw6w1t.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\pannellum.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k35ysw6w1t"},{"Name":"integrity","Value":"sha256-UMrBqZJuye13N2eIjAWEBoc2/wtmiWRr9AsuHHiRFjI="},{"Name":"label","Value":"_content/ViVu/js/pannellum.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"219"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022UMrBqZJuye13N2eIjAWEBoc2/wtmiWRr9AsuHHiRFjI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 08:06:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/panolens.min.fq7ex2r51z.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\panolens.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fq7ex2r51z"},{"Name":"integrity","Value":"sha256-pxcYzc8HLdwRd6Ad93CHx9yoYesWdA4tedW3EEjQQzQ="},{"Name":"label","Value":"_content/ViVu/js/panolens.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"233"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pxcYzc8HLdwRd6Ad93CHx9yoYesWdA4tedW3EEjQQzQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/panolens.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\panolens.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pxcYzc8HLdwRd6Ad93CHx9yoYesWdA4tedW3EEjQQzQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"233"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pxcYzc8HLdwRd6Ad93CHx9yoYesWdA4tedW3EEjQQzQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/recommendation.d8ki4dutwx.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\recommendation.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d8ki4dutwx"},{"Name":"integrity","Value":"sha256-7S6ToEJIDbYXFqXZJhU6QZhpNmsoTJJwl4MHEsm2ZRk="},{"Name":"label","Value":"_content/ViVu/js/recommendation.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4552"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227S6ToEJIDbYXFqXZJhU6QZhpNmsoTJJwl4MHEsm2ZRk=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 08:26:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/recommendation.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\recommendation.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7S6ToEJIDbYXFqXZJhU6QZhpNmsoTJJwl4MHEsm2ZRk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4552"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227S6ToEJIDbYXFqXZJhU6QZhpNmsoTJJwl4MHEsm2ZRk=\u0022"},{"Name":"Last-Modified","Value":"Fri, 09 May 2025 08:26:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/site.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nb2EmcdMrZlUrvDg0fQdf5OUS9QqOtoJwYRQZYy3wH0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7111"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022nb2EmcdMrZlUrvDg0fQdf5OUS9QqOtoJwYRQZYy3wH0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:33:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/site.tch3nzi1fh.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tch3nzi1fh"},{"Name":"integrity","Value":"sha256-nb2EmcdMrZlUrvDg0fQdf5OUS9QqOtoJwYRQZYy3wH0="},{"Name":"label","Value":"_content/ViVu/js/site.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7111"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022nb2EmcdMrZlUrvDg0fQdf5OUS9QqOtoJwYRQZYy3wH0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 30 Apr 2025 08:33:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/three.min.5bugo3o98h.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\three.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5bugo3o98h"},{"Name":"integrity","Value":"sha256-ccHYXTeDFGJbExuZVPQbShyOsUD/Kueaw8GtaazfZX0="},{"Name":"label","Value":"_content/ViVu/js/three.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"212"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ccHYXTeDFGJbExuZVPQbShyOsUD/Kueaw8GtaazfZX0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/js/three.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\three.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ccHYXTeDFGJbExuZVPQbShyOsUD/Kueaw8GtaazfZX0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"212"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ccHYXTeDFGJbExuZVPQbShyOsUD/Kueaw8GtaazfZX0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 02:50:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bqjiyaj88i"},{"Name":"integrity","Value":"sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6\u002BOjzrLmJIsC2Wy4H8="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"70329"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Yy5/hBqRmmU2MJ1TKwP2aXoTO6\u002BOjzrLmJIsC2Wy4H8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6\u002BOjzrLmJIsC2Wy4H8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"70329"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Yy5/hBqRmmU2MJ1TKwP2aXoTO6\u002BOjzrLmJIsC2Wy4H8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c2jlpeoesf"},{"Name":"integrity","Value":"sha256-xAT\u002Bn25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"203221"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xAT\u002Bn25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xAT\u002Bn25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"203221"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xAT\u002Bn25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51795"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aexeepp0ev"},{"Name":"integrity","Value":"sha256-kgL\u002BxwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"115986"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022kgL\u002BxwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kgL\u002BxwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"115986"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022kgL\u002BxwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"erw9l3u2r3"},{"Name":"integrity","Value":"sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51795"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"70403"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ausgxo2sd3"},{"Name":"integrity","Value":"sha256-/siQUA8yX830j\u002BcL4amKHY3yBtn3n8z3Eg\u002BVZ15f90k="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"203225"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022/siQUA8yX830j\u002BcL4amKHY3yBtn3n8z3Eg\u002BVZ15f90k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/siQUA8yX830j\u002BcL4amKHY3yBtn3n8z3Eg\u002BVZ15f90k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"203225"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022/siQUA8yX830j\u002BcL4amKHY3yBtn3n8z3Eg\u002BVZ15f90k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d7shbmvgxk"},{"Name":"integrity","Value":"sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"70403"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51870"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cosvhxvwiu"},{"Name":"integrity","Value":"sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"116063"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00227GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"116063"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00227GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k8d9w2qqmf"},{"Name":"integrity","Value":"sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51870"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lo9YI82OF03vojdu\u002BXOR3\u002BDRrLIpMhpzZNmHbM5CDMA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12065"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022lo9YI82OF03vojdu\u002BXOR3\u002BDRrLIpMhpzZNmHbM5CDMA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fvhpjtyr6v"},{"Name":"integrity","Value":"sha256-RXJ/QZiBfHXoPtXR2EgC\u002BbFo2pe3GtbZO722RtiLGzQ="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"129371"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022RXJ/QZiBfHXoPtXR2EgC\u002BbFo2pe3GtbZO722RtiLGzQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RXJ/QZiBfHXoPtXR2EgC\u002BbFo2pe3GtbZO722RtiLGzQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"129371"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022RXJ/QZiBfHXoPtXR2EgC\u002BbFo2pe3GtbZO722RtiLGzQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b7pk76d08c"},{"Name":"integrity","Value":"sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10126"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10126"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fsbi9cje9m"},{"Name":"integrity","Value":"sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51369"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00220eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51369"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00220eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn\u002BGg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12058"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn\u002BGg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ee0r1s7dh0"},{"Name":"integrity","Value":"sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"129386"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"129386"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10198"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jd9uben2k1"},{"Name":"integrity","Value":"sha256-910zw\u002BrMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"63943"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022910zw\u002BrMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-910zw\u002BrMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"63943"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022910zw\u002BrMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dxx9fxp4il"},{"Name":"integrity","Value":"sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10198"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rzd6atqjts"},{"Name":"integrity","Value":"sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn\u002BGg="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12058"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn\u002BGg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ub07r2b239"},{"Name":"integrity","Value":"sha256-lo9YI82OF03vojdu\u002BXOR3\u002BDRrLIpMhpzZNmHbM5CDMA="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-reboot.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12065"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022lo9YI82OF03vojdu\u002BXOR3\u002BDRrLIpMhpzZNmHbM5CDMA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM\u002Bh\u002Byo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"107823"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00222BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM\u002Bh\u002Byo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q\u002BLhL\u002Bz9553O0cY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"267535"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q\u002BLhL\u002Bz9553O0cY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r4e9w2rdcm"},{"Name":"integrity","Value":"sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q\u002BLhL\u002Bz9553O0cY="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"267535"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q\u002BLhL\u002Bz9553O0cY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"khv3u5hwcm"},{"Name":"integrity","Value":"sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM\u002Bh\u002Byo="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107823"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00222BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM\u002Bh\u002Byo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"85352"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c2oey78nd0"},{"Name":"integrity","Value":"sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"180381"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"180381"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lcd1t2u6c8"},{"Name":"integrity","Value":"sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"85352"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H6wkBbSwjua2veJoThJo4uy161jp\u002BDOiZTloUlcZ6qQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"107691"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H6wkBbSwjua2veJoThJo4uy161jp\u002BDOiZTloUlcZ6qQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j5mq2jizvt"},{"Name":"integrity","Value":"sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"267476"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"267476"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"06098lyss8"},{"Name":"integrity","Value":"sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"85281"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"85281"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"180217"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nvvlpmu67g"},{"Name":"integrity","Value":"sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"180217"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tdbxkamptv"},{"Name":"integrity","Value":"sha256-H6wkBbSwjua2veJoThJo4uy161jp\u002BDOiZTloUlcZ6qQ="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107691"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022H6wkBbSwjua2veJoThJo4uy161jp\u002BDOiZTloUlcZ6qQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"281046"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"679755"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pj5nd1wqec"},{"Name":"integrity","Value":"sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"679755"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"46ein0sx1k"},{"Name":"integrity","Value":"sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"232803"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"232803"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP\u002BGXYc3V1WwFs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"589892"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00228SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP\u002BGXYc3V1WwFs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v0zj4ognzu"},{"Name":"integrity","Value":"sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP\u002BGXYc3V1WwFs="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"589892"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00228SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP\u002BGXYc3V1WwFs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"37tfw0ft22"},{"Name":"integrity","Value":"sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"280259"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"280259"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hrwsygsryq"},{"Name":"integrity","Value":"sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"679615"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"679615"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"232911"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ft3s53vfgj"},{"Name":"integrity","Value":"sha256-rTzXlnepcb/vgFAiB\u002BU7ODQAfOlJLfM3gY6IU7eIANk="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"589087"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022rTzXlnepcb/vgFAiB\u002BU7ODQAfOlJLfM3gY6IU7eIANk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rTzXlnepcb/vgFAiB\u002BU7ODQAfOlJLfM3gY6IU7eIANk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"589087"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022rTzXlnepcb/vgFAiB\u002BU7ODQAfOlJLfM3gY6IU7eIANk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pk9g2wxc8p"},{"Name":"integrity","Value":"sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"232911"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"s35ty4nyc5"},{"Name":"integrity","Value":"sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/css/bootstrap.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"281046"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6cfz1n2cew"},{"Name":"integrity","Value":"sha256-mkoRoV24jV\u002BrCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"207819"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mkoRoV24jV\u002BrCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mkoRoV24jV\u002BrCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"207819"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mkoRoV24jV\u002BrCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6pdc2jztkx"},{"Name":"integrity","Value":"sha256-Wq4aWW1rQdJ\u002B6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"444579"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Wq4aWW1rQdJ\u002B6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Wq4aWW1rQdJ\u002B6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"444579"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Wq4aWW1rQdJ\u002B6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"493y06b0oq"},{"Name":"integrity","Value":"sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC\u002BmjoJimHGw="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"80721"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC\u002BmjoJimHGw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC\u002BmjoJimHGw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"80721"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC\u002BmjoJimHGw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iovd86k7lj"},{"Name":"integrity","Value":"sha256-Xj4HYxZBQ7qqHKBwa2EAugRS\u002BRHWzpcTtI49vgezUSU="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"332090"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Xj4HYxZBQ7qqHKBwa2EAugRS\u002BRHWzpcTtI49vgezUSU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Xj4HYxZBQ7qqHKBwa2EAugRS\u002BRHWzpcTtI49vgezUSU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"332090"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Xj4HYxZBQ7qqHKBwa2EAugRS\u002BRHWzpcTtI49vgezUSU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-exiXZNJDwucXfuje3CbXPbuS6\u002BEry3z9sP\u002Bpgmvh8nA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"135829"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022exiXZNJDwucXfuje3CbXPbuS6\u002BEry3z9sP\u002Bpgmvh8nA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kbrnm935zg"},{"Name":"integrity","Value":"sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"305438"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"305438"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jj8uyg4cgr"},{"Name":"integrity","Value":"sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa\u002BsPe6h794sFRQ="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"73935"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa\u002BsPe6h794sFRQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa\u002BsPe6h794sFRQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"73935"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa\u002BsPe6h794sFRQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"222455"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y7v9cxd14o"},{"Name":"integrity","Value":"sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"222455"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vr1egmr9el"},{"Name":"integrity","Value":"sha256-exiXZNJDwucXfuje3CbXPbuS6\u002BEry3z9sP\u002Bpgmvh8nA="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.esm.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"135829"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022exiXZNJDwucXfuje3CbXPbuS6\u002BEry3z9sP\u002Bpgmvh8nA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BUW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"145401"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022\u002BUW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h1s4sie4z3"},{"Name":"integrity","Value":"sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"306606"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00229Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"306606"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00229Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"63fj8s7r0e"},{"Name":"integrity","Value":"sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"60635"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"60635"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0j3bgjxly4"},{"Name":"integrity","Value":"sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"220561"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"220561"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"notf2xhcfb"},{"Name":"integrity","Value":"sha256-\u002BUW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/dist/js/bootstrap.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"145401"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022\u002BUW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/LICENSE">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/bootstrap/LICENSE.81b7ukuj9c">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"81b7ukuj9c"},{"Name":"integrity","Value":"sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk="},{"Name":"label","Value":"_content/ViVu/lib/bootstrap/LICENSE"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"47otxtyo56"},{"Name":"integrity","Value":"sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19385"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19385"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4v8eqarkd7"},{"Name":"integrity","Value":"sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5824"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5824"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"356vix0kms"},{"Name":"integrity","Value":"sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation-unobtrusive/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1139"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002216aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation-unobtrusive/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1139"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002216aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/additional-methods.83jwlth58m.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"83jwlth58m"},{"Name":"integrity","Value":"sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation/dist/additional-methods.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"53033"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/additional-methods.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"53033"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/additional-methods.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jhvKRxZo6eW/PyCe\u002B4rjBLzqesJlE8rnyQGEjk8l2k8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22125"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jhvKRxZo6eW/PyCe\u002B4rjBLzqesJlE8rnyQGEjk8l2k8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mrlpezrjn3"},{"Name":"integrity","Value":"sha256-jhvKRxZo6eW/PyCe\u002B4rjBLzqesJlE8rnyQGEjk8l2k8="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation/dist/additional-methods.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22125"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jhvKRxZo6eW/PyCe\u002B4rjBLzqesJlE8rnyQGEjk8l2k8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/jquery.validate.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"52536"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lzl9nlhx6b"},{"Name":"integrity","Value":"sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation/dist/jquery.validate.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"52536"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ag7o75518u"},{"Name":"integrity","Value":"sha256-umbTaFxP31Fv6O1itpLS/3\u002Bv5fOAWDLOUzlmvOGaKV4="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation/dist/jquery.validate.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25308"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022umbTaFxP31Fv6O1itpLS/3\u002Bv5fOAWDLOUzlmvOGaKV4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/dist/jquery.validate.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-umbTaFxP31Fv6O1itpLS/3\u002Bv5fOAWDLOUzlmvOGaKV4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"25308"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022umbTaFxP31Fv6O1itpLS/3\u002Bv5fOAWDLOUzlmvOGaKV4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/LICENSE.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery-validation/LICENSE.x0q3zqp4vz.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x0q3zqp4vz"},{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="},{"Name":"label","Value":"_content/ViVu/lib/jquery-validation/LICENSE.md"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.0i3buxo5is.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0i3buxo5is"},{"Name":"integrity","Value":"sha256-eKhayi8LEQwp4NKxN\u002BCfCh\u002B3qOVUtJn3QNZ0TciWLP4="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"285314"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022eKhayi8LEQwp4NKxN\u002BCfCh\u002B3qOVUtJn3QNZ0TciWLP4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eKhayi8LEQwp4NKxN\u002BCfCh\u002B3qOVUtJn3QNZ0TciWLP4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"285314"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022eKhayi8LEQwp4NKxN\u002BCfCh\u002B3qOVUtJn3QNZ0TciWLP4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"87533"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.min.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"134755"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.min.o1o13a6vjx.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o1o13a6vjx"},{"Name":"integrity","Value":"sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"87533"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.min.ttgo8qnofa.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ttgo8qnofa"},{"Name":"integrity","Value":"sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.min.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"134755"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.2z0ns9nrw6.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2z0ns9nrw6"},{"Name":"integrity","Value":"sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.slim.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"232015"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"232015"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"87fc7y1x7t"},{"Name":"integrity","Value":"sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.slim.min.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107143"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00229FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kmHvs0B\u002BOpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"70264"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022kmHvs0B\u002BOpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.min.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"107143"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00229FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/dist/jquery.slim.min.muycvpuwrr.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.slim.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"muycvpuwrr"},{"Name":"integrity","Value":"sha256-kmHvs0B\u002BOpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="},{"Name":"label","Value":"_content/ViVu/lib/jquery/dist/jquery.slim.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"70264"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022kmHvs0B\u002BOpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/LICENSE.mlv21k5csn.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mlv21k5csn"},{"Name":"integrity","Value":"sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="},{"Name":"label","Value":"_content/ViVu/lib/jquery/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/jquery/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 22 Feb 2025 01:50:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/pannellum/pannellum.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\pannellum\pannellum.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PQxFYpNGYpSptCno8rCjsn0Wx1KBatcV1TE1n4Qx\u002B\u002BQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6403"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PQxFYpNGYpSptCno8rCjsn0Wx1KBatcV1TE1n4Qx\u002B\u002BQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 05:25:41 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/lib/pannellum/pannellum.n7rwetgxxg.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\pannellum\pannellum.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n7rwetgxxg"},{"Name":"integrity","Value":"sha256-PQxFYpNGYpSptCno8rCjsn0Wx1KBatcV1TE1n4Qx\u002B\u002BQ="},{"Name":"label","Value":"_content/ViVu/lib/pannellum/pannellum.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6403"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PQxFYpNGYpSptCno8rCjsn0Wx1KBatcV1TE1n4Qx\u002B\u002BQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 05:25:41 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/ViVu.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ViVu.tdadp1kl73.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L60/de/ykz6gINCM4lRD7nafe7GAFKDIGIOUWzN2dC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1122"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022L60/de/ykz6gINCM4lRD7nafe7GAFKDIGIOUWzN2dC0=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Apr 2025 07:16:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ViVu/ViVu.tdadp1kl73.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ViVu.tdadp1kl73.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tdadp1kl73"},{"Name":"integrity","Value":"sha256-L60/de/ykz6gINCM4lRD7nafe7GAFKDIGIOUWzN2dC0="},{"Name":"label","Value":"_content/ViVu/ViVu.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1122"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022L60/de/ykz6gINCM4lRD7nafe7GAFKDIGIOUWzN2dC0=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Apr 2025 07:16:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>