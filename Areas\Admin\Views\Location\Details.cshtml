@model ViVu.Models.Location
@{
    ViewData["Title"] = "Chi tiết địa điểm";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết địa điểm</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý địa điểm</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Chi tiết địa điểm: @Model.Name
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Tên địa điểm:</dt>
                        <dd class="col-sm-8">@Model.Name</dd>

                        <dt class="col-sm-4">Thành phố:</dt>
                        <dd class="col-sm-8">@Model.City?.Name</dd>

                        <dt class="col-sm-4">Quốc gia:</dt>
                        <dd class="col-sm-8">@Model.City?.Country?.Name</dd>

                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8">@Model.Address</dd>

                        <dt class="col-sm-4">Số điện thoại:</dt>
                        <dd class="col-sm-8">@Model.Phone</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.Email</dd>

                        <dt class="col-sm-4">Website:</dt>
                        <dd class="col-sm-8">
                            @if (!string.IsNullOrEmpty(Model.Website))
                            {
                                <a href="@Model.Website" target="_blank">@Model.Website</a>
                            }
                        </dd>

                        <dt class="col-sm-4">Tọa độ:</dt>
                        <dd class="col-sm-8">@Model.Latitude, @Model.Longitude</dd>

                        <dt class="col-sm-4">Nổi bật:</dt>
                        <dd class="col-sm-8">
                            @if (Model.IsFeatured)
                            {
                                <span class="badge bg-success">Có</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Không</span>
                            }
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" style="max-height: 300px;" />
                    }
                    else
                    {
                        <img src="/images/default-location.jpg" alt="Default Image" class="img-fluid rounded" style="max-height: 300px;" />
                    }

                    <div class="mt-3">
                        <h5>Mô tả:</h5>
                        <p>@Model.Description</p>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <h5>Thống kê:</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">@(Model.Accommodations?.Count ?? 0)</h5>
                                <p class="card-text">Chỗ ở</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">@(Model.Tours?.Count ?? 0)</h5>
                                <p class="card-text">Tour</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">@(Model.Panoramas?.Count ?? 0)</h5>
                                <p class="card-text">Panorama 360°</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h5 class="card-title">@(Model.Reviews?.Count ?? 0)</h5>
                                <p class="card-text">Đánh giá</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">Chỉnh sửa</a>
                <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
            </div>
        </div>
    </div>
</div>
