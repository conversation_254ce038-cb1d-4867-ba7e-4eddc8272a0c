﻿﻿﻿using Microsoft.EntityFrameworkCore;
using System.Text;
using ViVu.Models;
using ViVu.Repositories;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System;

namespace ViVu.Services
{
    public class RecommendationService : IRecommendationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ITourRepository _tourRepository;
        private readonly IAccommodationRepository _accommodationRepository;
        private readonly IServiceRepository _serviceRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly IVehicleRepository _vehicleRepository;
        private readonly OpenAIService _openAIService;
        private readonly OllamaService _ollamaService;

        public RecommendationService(
            ApplicationDbContext context,
            ITourRepository tourRepository,
            IAccommodationRepository accommodationRepository,
            IServiceRepository serviceRepository,
            ILocationRepository locationRepository,
            IVehicleRepository vehicleRepository,
            OpenAIService openAIService,
            OllamaService ollamaService)
        {
            _context = context;
            _tourRepository = tourRepository;
            _accommodationRepository = accommodationRepository;
            _serviceRepository = serviceRepository;
            _locationRepository = locationRepository;
            _vehicleRepository = vehicleRepository;
            _openAIService = openAIService;
            _ollamaService = ollamaService;
        }

        public async Task<List<TravelRecommendation>> GenerateRecommendationsAsync(UserPreference preferences, string? userId = null)
        {
            var recommendations = new List<TravelRecommendation>();

            // Kiểm tra xem preferences có null không
            if (preferences == null)
            {
                preferences = new UserPreference();
            }

            // Kiểm tra xem có sử dụng AI để tạo gợi ý không
            bool useAI = preferences.UseAI;

            // Get user's search and booking history if userId is provided
            List<SearchHistory> searchHistory = new List<SearchHistory>();
            List<Booking> bookings = new List<Booking>();
            List<TourBooking> tourBookings = new List<TourBooking>();
            List<ServiceBooking> serviceBookings = new List<ServiceBooking>();
            List<VehicleBooking> vehicleBookings = new List<VehicleBooking>();
            List<Review> reviews = new List<Review>();

            // Các biến để theo dõi sở thích người dùng
            Dictionary<int, int> locationFrequency = new Dictionary<int, int>();
            Dictionary<string, int> activityFrequency = new Dictionary<string, int>
            {
                { "Nature", 0 },
                { "History", 0 },
                { "Food", 0 },
                { "Adventure", 0 },
                { "Relaxation", 0 }
            };

            if (!string.IsNullOrEmpty(userId))
            {
                // Lấy lịch sử tìm kiếm chi tiết hơn
                searchHistory = await _context.SearchHistories
                    .Where(sh => sh.UserId == userId)
                    .OrderByDescending(sh => sh.SearchDate)
                    .Take(50) // Tăng số lượng lịch sử tìm kiếm để phân tích tốt hơn
                    .ToListAsync();

                // Phân tích lịch sử tìm kiếm để xác định sở thích
                foreach (var search in searchHistory)
                {
                    // Đếm tần suất tìm kiếm theo địa điểm
                    if (search.LocationId.HasValue)
                    {
                        if (!locationFrequency.ContainsKey(search.LocationId.Value))
                        {
                            locationFrequency[search.LocationId.Value] = 0;
                        }
                        locationFrequency[search.LocationId.Value]++;
                    }

                    // Đếm tần suất tìm kiếm theo loại hoạt động
                    if (search.RelatedToNature == true) activityFrequency["Nature"]++;
                    if (search.RelatedToHistory == true) activityFrequency["History"]++;
                    if (search.RelatedToFood == true) activityFrequency["Food"]++;
                    if (search.RelatedToAdventure == true) activityFrequency["Adventure"]++;
                    if (search.RelatedToRelaxation == true) activityFrequency["Relaxation"]++;
                }

                // Lấy lịch sử đặt phòng
                bookings = await _context.Bookings
                    .Where(b => b.UserId == userId)
                    .OrderByDescending(b => b.BookingDate)
                    .Take(10)
                    .ToListAsync();

                // Lấy lịch sử đặt tour
                tourBookings = await _context.TourBookings
                    .Where(tb => tb.UserId == userId)
                    .OrderByDescending(tb => tb.BookingDate)
                    .Take(10)
                    .ToListAsync();

                // Lấy lịch sử đặt dịch vụ
                serviceBookings = await _context.ServiceBookings
                    .Where(sb => sb.UserId == userId)
                    .OrderByDescending(sb => sb.BookingDate)
                    .Take(10)
                    .ToListAsync();

                // Lấy lịch sử đặt phương tiện
                vehicleBookings = await _context.VehicleBookings
                    .Where(vb => vb.UserId == userId)
                    .OrderByDescending(vb => vb.BookingDate)
                    .Take(10)
                    .ToListAsync();

                // Lấy đánh giá của người dùng
                reviews = await _context.Reviews
                    .Where(r => r.UserId == userId)
                    .OrderByDescending(r => r.CreatedAt)
                    .Take(20)
                    .ToListAsync();
            }

            // Lấy địa điểm phổ biến dựa trên lịch sử tìm kiếm và đặt phòng
            var popularLocationIds = await _context.SearchHistories
                .Where(sh => sh.LocationId.HasValue)
                .GroupBy(sh => sh.LocationId)
                .OrderByDescending(g => g.Count())
                .Select(g => g.Key!.Value)
                .Take(10)
                .ToListAsync();

            var popularLocations = new List<Location>();
            foreach (var id in popularLocationIds)
            {
                var location = await _context.Locations
                    .Include(l => l.City)
                    .FirstOrDefaultAsync(l => l.Id == id);
                if (location != null)
                {
                    popularLocations.Add(location);
                }
            }

            // Nếu không đủ địa điểm phổ biến, thêm một số địa điểm mặc định
            if (popularLocations.Count < 5)
            {
                var additionalLocations = await _context.Locations
                    .Include(l => l.City)
                    .Where(l => !popularLocationIds.Contains(l.Id))
                    .OrderByDescending(l => l.Id) // Sắp xếp đơn giản theo ID
                    .Take(5 - popularLocations.Count)
                    .ToListAsync();

                popularLocations.AddRange(additionalLocations);
            }

            // Lấy tour phổ biến
            var popularTours = await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .OrderByDescending(t => t.Id)
                .Take(10)
                .ToListAsync();

            // Lấy chỗ ở phổ biến
            var popularAccommodations = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .OrderByDescending(a => a.Id)
                .Take(10)
                .ToListAsync();

            // Lấy dịch vụ phổ biến
            var popularServices = await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .OrderByDescending(s => s.Id)
                .Take(5)
                .ToListAsync();

            // Lấy phương tiện phổ biến
            var popularVehicles = await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .OrderByDescending(v => v.Id)
                .Take(5)
                .ToListAsync();

            // Phân tích sở thích người dùng để tạo gợi ý phù hợp

            // Xác định các hoạt động ưa thích dựa trên tần suất tìm kiếm
            string topActivity = "Nature"; // Mặc định
            int maxCount = 0;

            foreach (var activity in activityFrequency)
            {
                if (activity.Value > maxCount)
                {
                    maxCount = activity.Value;
                    topActivity = activity.Key;
                }
            }

            // Xác định địa điểm ưa thích dựa trên tần suất tìm kiếm
            int? topLocationId = null;
            maxCount = 0;

            foreach (var location in locationFrequency)
            {
                if (location.Value > maxCount)
                {
                    maxCount = location.Value;
                    topLocationId = location.Key;
                }
            }

            // 1. Gợi ý tour có hướng dẫn viên
            if (preferences.PrefersTours)
            {
                var tourRecommendation = new TravelRecommendation
                {
                    UserId = userId,
                    Title = "Tour Du Lịch Bến Tre Theo Hướng Dẫn Viên",
                    Description = "Khám phá Bến Tre với hướng dẫn viên chuyên nghiệp, tận hưởng trải nghiệm trọn vẹn mà không cần lo lắng về việc sắp xếp lịch trình.",
                    RecommendationType = "Guided Tour",
                    EstimatedMinBudget = preferences.MinBudget ?? 500000,
                    EstimatedMaxBudget = preferences.MaxBudget ?? 2000000,
                    RecommendedDuration = preferences.MinDuration ?? 2,

                    // Thêm thông tin chi tiết về loại gợi ý
                    IncludesNature = preferences.PrefersNature || topActivity == "Nature",
                    IncludesHistory = preferences.PrefersHistory || topActivity == "History",
                    IncludesFood = preferences.PrefersFood || topActivity == "Food",
                    IncludesAdventure = preferences.PrefersAdventure || topActivity == "Adventure",
                    IncludesRelaxation = preferences.PrefersRelaxation || topActivity == "Relaxation",

                    // Thông tin về đối tượng phù hợp
                    SuitableForChildren = preferences.TravelingWithChildren,
                    SuitableForElders = preferences.TravelingWithElders,

                    // Nguồn gợi ý
                    RecommendationSource = !string.IsNullOrEmpty(userId) ? "UserHistory" : "AI",

                    // Mức độ ưu tiên
                    Priority = !string.IsNullOrEmpty(userId) ? 8 : 5
                };

                // Tìm tour phù hợp dựa trên sở thích
                var tourQuery = popularTours.AsQueryable();

                // Lọc theo ngân sách
                if (preferences.MinBudget.HasValue)
                {
                    tourQuery = tourQuery.Where(t => t.Price >= preferences.MinBudget.Value);
                }

                if (preferences.MaxBudget.HasValue)
                {
                    tourQuery = tourQuery.Where(t => t.Price <= preferences.MaxBudget.Value);
                }

                // Lọc theo thời gian
                if (preferences.MinDuration.HasValue)
                {
                    tourQuery = tourQuery.Where(t => t.Duration >= preferences.MinDuration.Value);
                }

                if (preferences.MaxDuration.HasValue)
                {
                    tourQuery = tourQuery.Where(t => t.Duration <= preferences.MaxDuration.Value);
                }

                // Lọc theo địa điểm ưa thích
                if (topLocationId.HasValue)
                {
                    var topLocationTours = tourQuery.Where(t => t.LocationId == topLocationId.Value).ToList();
                    if (topLocationTours.Count > 0)
                    {
                        tourQuery = topLocationTours.AsQueryable();
                    }
                }

                // Lấy danh sách tour gợi ý
                var recommendedTours = tourQuery.Take(3).ToList();

                // Tạo lịch trình chi tiết
                var itinerary = new StringBuilder();
                itinerary.AppendLine("# Lịch Trình Đề Xuất");
                itinerary.AppendLine();

                // Thêm phần giới thiệu tổng quan
                tourRecommendation.HighlightsSection = "## Điểm Nổi Bật\n\n" +
                    "- Tour được thiết kế phù hợp với sở thích cá nhân của bạn\n" +
                    "- Có hướng dẫn viên chuyên nghiệp đồng hành\n" +
                    "- Trải nghiệm văn hóa và ẩm thực đặc trưng của Bến Tre\n" +
                    "- Tham quan các địa điểm nổi tiếng và độc đáo\n";

                if (recommendedTours.Count > 0)
                {
                    tourRecommendation.RelatedTourIds = string.Join(",", recommendedTours.Select(t => t.Id));

                    // Thêm thông tin chi tiết về các tour
                    itinerary.AppendLine("## Tour Đề Xuất");
                    itinerary.AppendLine();

                    foreach (var tour in recommendedTours)
                    {
                        itinerary.AppendLine($"### {tour.Name}");
                        itinerary.AppendLine($"**Thời gian:** {tour.Duration} ngày");
                        itinerary.AppendLine($"**Giá:** {tour.Price:N0} VNĐ");
                        itinerary.AppendLine($"**Địa điểm:** {tour.Location?.Name}, {tour.City?.Name}");
                        itinerary.AppendLine();
                        itinerary.AppendLine("#### Chi tiết lịch trình:");
                        itinerary.AppendLine(tour.Itinerary);
                        itinerary.AppendLine();
                    }

                    // Thêm phần phân tích chi phí
                    var minPrice = recommendedTours.Min(t => t.Price);
                    var maxPrice = recommendedTours.Max(t => t.Price);

                    tourRecommendation.BudgetBreakdownSection = "## Chi Phí Dự Kiến\n\n" +
                        $"- Tour: {minPrice:N0} - {maxPrice:N0} VNĐ\n" +
                        "- Chi phí ăn uống ngoài chương trình: 300,000 - 500,000 VNĐ\n" +
                        "- Chi phí mua sắm: Tùy theo nhu cầu cá nhân\n";
                }
                else
                {
                    // Tạo lịch trình mẫu nếu không tìm thấy tour phù hợp
                    itinerary.AppendLine("## Tour Khám Phá Bến Tre 2 Ngày 1 Đêm");
                    itinerary.AppendLine("**Ngày 1:**");
                    itinerary.AppendLine("- Sáng: Khởi hành từ TP.HCM đi Bến Tre");
                    itinerary.AppendLine("- Trưa: Ăn trưa tại nhà hàng địa phương với các món đặc sản");
                    itinerary.AppendLine("- Chiều: Tham quan làng nghề truyền thống, trải nghiệm làm kẹo dừa");
                    itinerary.AppendLine("- Tối: Ăn tối và nghỉ ngơi tại khách sạn");
                    itinerary.AppendLine();
                    itinerary.AppendLine("**Ngày 2:**");
                    itinerary.AppendLine("- Sáng: Đi thuyền trên sông Bến Tre, tham quan vườn trái cây");
                    itinerary.AppendLine("- Trưa: Ăn trưa với đặc sản địa phương");
                    itinerary.AppendLine("- Chiều: Trở về TP.HCM");

                    // Thêm phần phân tích chi phí
                    tourRecommendation.BudgetBreakdownSection = "## Chi Phí Dự Kiến\n\n" +
                        "- Tour: 1,200,000 - 1,500,000 VNĐ\n" +
                        "- Chi phí ăn uống ngoài chương trình: 300,000 - 500,000 VNĐ\n" +
                        "- Chi phí mua sắm: Tùy theo nhu cầu cá nhân\n";
                }

                // Thêm phần mẹo du lịch
                tourRecommendation.TipsSection = "## Mẹo Du Lịch\n\n" +
                    "- Nên mang theo nón, kem chống nắng và thuốc chống côn trùng\n" +
                    "- Mặc trang phục thoải mái, phù hợp với thời tiết nhiệt đới\n" +
                    "- Mang theo tiền mặt vì một số địa điểm không hỗ trợ thanh toán thẻ\n" +
                    "- Nên đặt tour trước ít nhất 3-5 ngày để đảm bảo có chỗ\n";

                // Gán nội dung lịch trình
                tourRecommendation.ItineraryDetails = itinerary.ToString();

                // Thêm phần lịch trình theo ngày
                tourRecommendation.DayByDaySection = "## Lịch Trình Chi Tiết Theo Ngày\n\n" +
                    "### Ngày 1:\n" +
                    "- 07:00 - 08:00: Khởi hành từ TP.HCM\n" +
                    "- 10:00 - 10:30: Đến Bến Tre, nghỉ ngơi\n" +
                    "- 10:30 - 12:00: Tham quan làng nghề truyền thống\n" +
                    "- 12:00 - 13:30: Ăn trưa với đặc sản địa phương\n" +
                    "- 13:30 - 16:00: Tham quan vườn trái cây, trải nghiệm hái trái cây theo mùa\n" +
                    "- 16:00 - 18:00: Nhận phòng khách sạn, nghỉ ngơi\n" +
                    "- 18:00 - 20:00: Ăn tối và khám phá ẩm thực địa phương\n\n" +
                    "### Ngày 2:\n" +
                    "- 07:00 - 08:00: Ăn sáng tại khách sạn\n" +
                    "- 08:00 - 10:30: Đi thuyền trên sông Bến Tre\n" +
                    "- 10:30 - 12:00: Tham quan Cồn Phụng\n" +
                    "- 12:00 - 13:30: Ăn trưa\n" +
                    "- 13:30 - 15:00: Mua đặc sản làm quà\n" +
                    "- 15:00 - 17:30: Trở về TP.HCM\n";

                recommendations.Add(tourRecommendation);
            }

            // 2. Recommendation for independent travel
            if (preferences.PrefersIndependentTravel)
            {
                // Log the preferences for debugging
                Console.WriteLine("=== Creating Independent Travel Recommendation ===");
                Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
                Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
                Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
                Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
                Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
                Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
                Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
                Console.WriteLine($"InterestedInCooking: {preferences.InterestedInCooking}");
                Console.WriteLine($"InterestedInCrafts: {preferences.InterestedInCrafts}");
                Console.WriteLine($"InterestedInFarming: {preferences.InterestedInFarming}");
                Console.WriteLine($"InterestedInBoating: {preferences.InterestedInBoating}");
                Console.WriteLine($"InterestedInCycling: {preferences.InterestedInCycling}");
                Console.WriteLine($"InterestedInFishing: {preferences.InterestedInFishing}");
                Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
                Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
                Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
                Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
                Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");

                var independentRecommendation = new TravelRecommendation
                {
                    UserId = userId,
                    Title = "Khám Phá Bến Tre Tự Túc",
                    Description = "Tự do khám phá Bến Tre theo cách riêng của bạn, với những gợi ý về địa điểm, ẩm thực và hoạt động thú vị.",
                    RecommendationType = "Independent Travel",
                    EstimatedMinBudget = preferences.MinBudget ?? 300000,
                    EstimatedMaxBudget = preferences.MaxBudget ?? 1500000,
                    RecommendedDuration = preferences.MinDuration ?? 3,

                    // Thêm thông tin chi tiết về loại gợi ý - đảm bảo khớp chính xác với sở thích người dùng
                    IncludesNature = preferences.PrefersNature,
                    IncludesHistory = preferences.PrefersHistory,
                    IncludesFood = preferences.PrefersFood,
                    IncludesAdventure = preferences.PrefersAdventure,
                    IncludesRelaxation = preferences.PrefersRelaxation,

                    // Thông tin về đối tượng phù hợp
                    SuitableForChildren = preferences.TravelingWithChildren,
                    SuitableForElders = preferences.TravelingWithElders,
                    ForSoloTravelers = preferences.TravelingAlone,
                    ForCouples = preferences.TravelingAsCouple,
                    ForFriends = preferences.TravelingWithFriends
                };

                // Find suitable accommodations
                var recommendedAccommodations = popularAccommodations
                    .Take(2)
                    .ToList();

                if (recommendedAccommodations.Any())
                {
                    independentRecommendation.RelatedAccommodationIds = string.Join(",", recommendedAccommodations.Select(a => a.Id));
                }

                // Find suitable locations
                var recommendedLocations = popularLocations.Take(3).ToList();
                if (recommendedLocations.Any())
                {
                    independentRecommendation.RelatedLocationIds = string.Join(",", recommendedLocations.Select(l => l.Id));
                }

                // Create itinerary based on user preferences
                var itinerary = new System.Text.StringBuilder();
                itinerary.AppendLine("# Lịch Trình Du Lịch Tự Túc Tại Bến Tre");
                itinerary.AppendLine();

                // Tạo lịch trình dựa trên sở thích người dùng
                if (preferences.PrefersNature)
                {
                    itinerary.AppendLine("## Ngày 1: Khám Phá Thiên Nhiên Bến Tre");
                    itinerary.AppendLine("- Sáng: Tham quan vườn trái cây, thưởng thức trái cây tươi theo mùa");
                    itinerary.AppendLine("- Trưa: Ăn trưa tại nhà vườn với các món đặc sản địa phương");
                    itinerary.AppendLine("- Chiều: Đi thuyền dọc sông Bến Tre, ngắm cảnh thiên nhiên sông nước");
                    itinerary.AppendLine("- Tối: Nghỉ ngơi tại homestay ven sông, thưởng thức bữa tối với đặc sản địa phương");
                }
                else
                {
                    itinerary.AppendLine("## Ngày 1: Khám Phá Trung Tâm Bến Tre");
                    itinerary.AppendLine("- Sáng: Tham quan chợ Bến Tre, thưởng thức bữa sáng với hủ tiếu hoặc bánh canh cua đặc sản");
                    itinerary.AppendLine("- Trưa: Ăn trưa tại nhà hàng địa phương với món cá tai tượng chiên xù");
                    itinerary.AppendLine("- Chiều: Tham quan Đền thờ Ông Trần, Chùa Phú Lâm");
                    itinerary.AppendLine("- Tối: Dạo quanh bờ sông Bến Tre, thưởng thức ẩm thực đường phố");
                }
                itinerary.AppendLine();

                if (preferences.PrefersAdventure)
                {
                    itinerary.AppendLine("## Ngày 2: Khám Phá Mạo Hiểm");
                    itinerary.AppendLine("- Sáng: Thuê xe đạp địa hình khám phá các con đường làng quê");
                    itinerary.AppendLine("- Trưa: Picnic tại vườn dừa với thức ăn mang theo");
                    itinerary.AppendLine("- Chiều: Tham gia các hoạt động mạo hiểm như chèo thuyền kayak trên sông, leo dừa");
                    itinerary.AppendLine("- Tối: Cắm trại tại khu du lịch sinh thái (nếu có điều kiện) hoặc trở về homestay");
                }
                else if (preferences.PrefersHistory)
                {
                    itinerary.AppendLine("## Ngày 2: Khám Phá Di Tích Lịch Sử");
                    itinerary.AppendLine("- Sáng: Tham quan di tích lịch sử Đồng Khởi, tìm hiểu về lịch sử kháng chiến");
                    itinerary.AppendLine("- Trưa: Ăn trưa tại nhà hàng địa phương");
                    itinerary.AppendLine("- Chiều: Tham quan Khu di tích Ông Trần, tìm hiểu về văn hóa lịch sử địa phương");
                    itinerary.AppendLine("- Tối: Xem biểu diễn đờn ca tài tử Nam Bộ");
                }
                else
                {
                    itinerary.AppendLine("## Ngày 2: Trải Nghiệm Sông Nước Miệt Vườn");
                    itinerary.AppendLine("- Sáng: Thuê xe máy đi Cồn Phụng, tham quan nhà thờ Đạo Dừa");
                    itinerary.AppendLine("- Trưa: Thưởng thức bữa trưa tại nhà hàng trên cồn với các món từ dừa");
                    itinerary.AppendLine("- Chiều: Đi thuyền tham quan các làng nghề truyền thống, trải nghiệm làm kẹo dừa");
                    itinerary.AppendLine("- Tối: Quay về trung tâm thành phố, thưởng thức bữa tối tại nhà hàng địa phương");
                }
                itinerary.AppendLine();

                if (preferences.PrefersFood)
                {
                    itinerary.AppendLine("## Ngày 3: Khám Phá Ẩm Thực Bến Tre");
                    itinerary.AppendLine("- Sáng: Học nấu ăn các món đặc sản Bến Tre tại lớp dạy nấu ăn địa phương");
                    itinerary.AppendLine("- Trưa: Thưởng thức thành quả nấu ăn của bạn");
                    itinerary.AppendLine("- Chiều: Tham quan các cơ sở sản xuất đặc sản như kẹo dừa, rượu dừa");
                    itinerary.AppendLine("- Tối: Tour ẩm thực đường phố, thưởng thức các món ăn đặc trưng của Bến Tre");
                }
                else if (preferences.PrefersRelaxation)
                {
                    itinerary.AppendLine("## Ngày 3: Thư Giãn và Nghỉ Dưỡng");
                    itinerary.AppendLine("- Sáng: Yoga hoặc thiền tại khu vườn dừa");
                    itinerary.AppendLine("- Trưa: Ăn trưa nhẹ nhàng với các món chay");
                    itinerary.AppendLine("- Chiều: Massage với dầu dừa tại spa địa phương");
                    itinerary.AppendLine("- Tối: Ngắm hoàng hôn bên sông, thưởng thức bữa tối lãng mạn");
                }
                else
                {
                    itinerary.AppendLine("## Ngày 3: Khám Phá Vùng Ven");
                    itinerary.AppendLine("- Sáng: Tham quan vườn trái cây, thưởng thức trái cây tươi theo mùa");
                    itinerary.AppendLine("- Trưa: Ăn trưa tại nhà vườn với các món đặc sản");
                    itinerary.AppendLine("- Chiều: Tham quan làng nghề đan lát, mua sắm đồ lưu niệm");
                    itinerary.AppendLine("- Tối: Trở về TP.HCM hoặc ở lại thêm một đêm tại Bến Tre");
                }

                // Thêm phần điểm nổi bật dựa trên sở thích
                var highlights = new StringBuilder();
                highlights.AppendLine("## Điểm Nổi Bật");
                highlights.AppendLine();
                highlights.AppendLine("- Tự do khám phá Bến Tre theo cách riêng của bạn");
                highlights.AppendLine("- Lịch trình linh hoạt, có thể điều chỉnh theo sở thích cá nhân");

                if (preferences.PrefersNature)
                    highlights.AppendLine("- Trải nghiệm thiên nhiên tuyệt đẹp với vườn trái cây và sông nước");

                if (preferences.PrefersAdventure)
                    highlights.AppendLine("- Các hoạt động mạo hiểm thú vị như chèo thuyền kayak, đạp xe địa hình");

                if (preferences.PrefersHistory)
                    highlights.AppendLine("- Khám phá di tích lịch sử và văn hóa đặc sắc của Bến Tre");

                if (preferences.PrefersFood)
                    highlights.AppendLine("- Thưởng thức ẩm thực đa dạng và học nấu các món đặc sản địa phương");

                if (preferences.PrefersRelaxation)
                    highlights.AppendLine("- Thời gian thư giãn, nghỉ ngơi tại các khu nghỉ dưỡng yên bình");

                independentRecommendation.HighlightsSection = highlights.ToString();

                // Thêm phần phân tích chi phí
                var budget = new StringBuilder();
                budget.AppendLine("## Chi Phí Dự Kiến");
                budget.AppendLine();
                budget.AppendLine("- Phương tiện di chuyển: 200,000 - 300,000 VNĐ/ngày (thuê xe máy hoặc xe đạp)");
                budget.AppendLine("- Ăn uống: 150,000 - 250,000 VNĐ/ngày/người");
                budget.AppendLine("- Chỗ ở: 300,000 - 700,000 VNĐ/đêm (homestay hoặc khách sạn)");
                budget.AppendLine("- Vé tham quan: 50,000 - 100,000 VNĐ/địa điểm");

                if (preferences.PrefersAdventure)
                    budget.AppendLine("- Hoạt động mạo hiểm: 150,000 - 300,000 VNĐ/hoạt động");

                if (preferences.PrefersRelaxation)
                    budget.AppendLine("- Dịch vụ spa và massage: 200,000 - 500,000 VNĐ");

                independentRecommendation.BudgetBreakdownSection = budget.ToString();

                // Thêm phần mẹo du lịch
                var tips = new StringBuilder();
                tips.AppendLine("## Mẹo Du Lịch");
                tips.AppendLine();
                tips.AppendLine("- Nên mang theo nón, kem chống nắng và thuốc chống côn trùng");
                tips.AppendLine("- Mặc trang phục thoải mái, phù hợp với thời tiết nhiệt đới");
                tips.AppendLine("- Mang theo tiền mặt vì một số địa điểm không hỗ trợ thanh toán thẻ");

                if (preferences.PrefersNature || preferences.PrefersAdventure)
                    tips.AppendLine("- Mang giày đi bộ thoải mái và quần áo dễ khô nếu tham gia các hoạt động ngoài trời");

                if (preferences.PrefersIndependentTravel)
                    tips.AppendLine("- Tải bản đồ offline và lưu thông tin liên hệ của chỗ ở");

                independentRecommendation.TipsSection = tips.ToString();

                // Gán nội dung lịch trình
                independentRecommendation.ItineraryDetails = itinerary.ToString();

                // Thêm vào danh sách đề xuất
                recommendations.Add(independentRecommendation);
            }

            // 3. Mixed recommendation (both guided and independent)
            if (preferences.PrefersTours && preferences.PrefersIndependentTravel || (!preferences.PrefersTours && !preferences.PrefersIndependentTravel))
            {
                // Log the preferences for debugging
                Console.WriteLine("=== Creating Mixed Recommendation ===");
                Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
                Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
                Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
                Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
                Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
                Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
                Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");

                var mixedRecommendation = new TravelRecommendation
                {
                    UserId = userId,
                    Title = "Trải Nghiệm Bến Tre Kết Hợp",
                    Description = "Kết hợp giữa tour có hướng dẫn và khám phá tự do, mang đến trải nghiệm du lịch đa dạng và linh hoạt.",
                    RecommendationType = "Mixed",
                    EstimatedMinBudget = preferences.MinBudget ?? 400000,
                    EstimatedMaxBudget = preferences.MaxBudget ?? 1800000,
                    RecommendedDuration = preferences.MinDuration ?? 4
                };

                // Find suitable tours and services
                var recommendedTours = popularTours.Take(1).ToList();
                if (recommendedTours.Any())
                {
                    mixedRecommendation.RelatedTourIds = string.Join(",", recommendedTours.Select(t => t.Id));
                }

                // Find suitable accommodations
                var recommendedAccommodations = popularAccommodations.Take(1).ToList();
                if (recommendedAccommodations.Any())
                {
                    mixedRecommendation.RelatedAccommodationIds = string.Join(",", recommendedAccommodations.Select(a => a.Id));
                }

                // Find suitable locations
                var recommendedLocations = popularLocations.Take(2).ToList();
                if (recommendedLocations.Any())
                {
                    mixedRecommendation.RelatedLocationIds = string.Join(",", recommendedLocations.Select(l => l.Id));
                }

                // Create itinerary
                var itinerary = new System.Text.StringBuilder();
                itinerary.AppendLine("# Lịch Trình Kết Hợp Tại Bến Tre");
                itinerary.AppendLine();
                itinerary.AppendLine("## Ngày 1-2: Tour Có Hướng Dẫn");
                itinerary.AppendLine("Tham gia tour có hướng dẫn viên để khám phá những điểm đến nổi tiếng của Bến Tre:");
                itinerary.AppendLine("- Tham quan Cồn Phụng, Cồn Quy");
                itinerary.AppendLine("- Trải nghiệm làng nghề truyền thống");
                itinerary.AppendLine("- Thưởng thức ẩm thực đặc sản địa phương");
                itinerary.AppendLine("- Đi thuyền trên sông Bến Tre");
                itinerary.AppendLine();
                itinerary.AppendLine("## Ngày 3-4: Khám Phá Tự Túc");
                itinerary.AppendLine("Tự do khám phá Bến Tre theo sở thích cá nhân:");
                itinerary.AppendLine("- Thuê xe máy đi vòng quanh thành phố");
                itinerary.AppendLine("- Tham quan các điểm đến chưa có trong tour");
                itinerary.AppendLine("- Thưởng thức ẩm thực đường phố");
                itinerary.AppendLine("- Mua sắm đồ lưu niệm tại các làng nghề");
                itinerary.AppendLine("- Tham quan vườn trái cây theo mùa");

                mixedRecommendation.ItineraryDetails = itinerary.ToString();
                recommendations.Add(mixedRecommendation);
            }

            // Thêm gợi ý từ AI nếu người dùng chọn sử dụng AI
            if (useAI)
            {
                // Log the preferences before calling AddAIRecommendation
                Console.WriteLine("=== Before calling AddAIRecommendation ===");
                Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
                Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
                Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
                Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
                Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
                Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
                Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");

                // Ensure userId is not null
                string userIdForAI = userId ?? string.Empty;

                await AddAIRecommendation(recommendations, preferences, userIdForAI,
                    popularLocations, popularTours, popularAccommodations,
                    searchHistory, bookings, tourBookings, serviceBookings, vehicleBookings, reviews);
            }

            // Add more personalized recommendations based on user history if available
            if (!string.IsNullOrEmpty(userId) && (reviews.Any() || tourBookings.Any() || bookings.Any()))
            {
                var personalizedRecommendation = new TravelRecommendation
                {
                    UserId = userId,
                    Title = "Đề Xuất Cá Nhân Hóa Cho Bạn",
                    Description = "Dựa trên lịch sử đặt phòng, tour và đánh giá của bạn, chúng tôi đề xuất lịch trình phù hợp với sở thích cá nhân.",
                    RecommendationType = preferences.PrefersTours ? "Guided Tour" : "Independent Travel",
                    EstimatedMinBudget = preferences.MinBudget ?? 500000,
                    EstimatedMaxBudget = preferences.MaxBudget ?? 2000000,
                    RecommendedDuration = preferences.MinDuration ?? 3
                };

                // Create personalized itinerary based on user history
                var itinerary = new System.Text.StringBuilder();
                itinerary.AppendLine("# Lịch Trình Cá Nhân Hóa Cho Bạn");
                itinerary.AppendLine();

                // Add personalized content based on user's history
                if (reviews.Any())
                {
                    var highestRatedReviews = reviews.OrderByDescending(r => r.Rating).Take(2).ToList();
                    if (highestRatedReviews.Any())
                    {
                        itinerary.AppendLine("## Dựa trên đánh giá cao của bạn");
                        foreach (var review in highestRatedReviews)
                        {
                            if (review.AccommodationId.HasValue)
                            {
                                var accommodation = await _context.Accommodations.FindAsync(review.AccommodationId);
                                if (accommodation != null)
                                {
                                    itinerary.AppendLine($"- Ở tại {accommodation.Name} - nơi bạn đã đánh giá {review.Rating}/5 sao");
                                    personalizedRecommendation.RelatedAccommodationIds = accommodation.Id.ToString();
                                }
                            }
                            else if (review.TourId.HasValue)
                            {
                                var tour = await _context.Tours.FindAsync(review.TourId);
                                if (tour != null)
                                {
                                    itinerary.AppendLine($"- Tham gia tour {tour.Name} - tour bạn đã đánh giá {review.Rating}/5 sao");
                                    personalizedRecommendation.RelatedTourIds = tour.Id.ToString();
                                }
                            }
                            else if (review.ServiceId.HasValue)
                            {
                                var service = await _context.Services.FindAsync(review.ServiceId);
                                if (service != null)
                                {
                                    itinerary.AppendLine($"- Sử dụng dịch vụ {service.Name} - dịch vụ bạn đã đánh giá {review.Rating}/5 sao");
                                    personalizedRecommendation.RelatedServiceIds = service.Id.ToString();
                                }
                            }
                        }
                        itinerary.AppendLine();
                    }
                }

                // Add general recommendations
                itinerary.AppendLine("## Lịch trình đề xuất");
                itinerary.AppendLine("**Ngày 1:**");
                itinerary.AppendLine("- Sáng: Khởi hành từ TP.HCM đi Bến Tre");
                itinerary.AppendLine("- Trưa: Ăn trưa tại nhà hàng địa phương với các món đặc sản");
                itinerary.AppendLine("- Chiều: Tham quan làng nghề truyền thống, trải nghiệm làm kẹo dừa");
                itinerary.AppendLine("- Tối: Ăn tối và nghỉ ngơi tại khách sạn");
                itinerary.AppendLine();
                itinerary.AppendLine("**Ngày 2:**");
                itinerary.AppendLine("- Sáng: Đi thuyền trên sông Bến Tre, tham quan vườn trái cây");
                itinerary.AppendLine("- Trưa: Ăn trưa với đặc sản địa phương");
                itinerary.AppendLine("- Chiều: Tham quan Cồn Phụng, Cồn Quy");
                itinerary.AppendLine("- Tối: Thưởng thức ẩm thực đường phố");
                itinerary.AppendLine();
                itinerary.AppendLine("**Ngày 3:**");
                itinerary.AppendLine("- Sáng: Tham quan chợ Bến Tre, mua đặc sản làm quà");
                itinerary.AppendLine("- Trưa: Ăn trưa tại nhà hàng địa phương");
                itinerary.AppendLine("- Chiều: Trở về TP.HCM");

                personalizedRecommendation.ItineraryDetails = itinerary.ToString();
                recommendations.Add(personalizedRecommendation);
            }

            return recommendations;
        }

        public async Task<UserPreference> GetUserPreferencesAsync(string userId)
        {
            var preferences = await _context.UserPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId);

            if (preferences == null)
            {
                // Create default preferences
                preferences = new UserPreference
                {
                    UserId = userId,
                    PrefersTours = true,
                    PrefersIndependentTravel = true,
                    MinBudget = 500000,
                    MaxBudget = 2000000,
                    MinDuration = 2,
                    MaxDuration = 5,
                    PrefersNature = true,
                    PrefersHistory = true,
                    PrefersFood = true,
                    PrefersAdventure = false,
                    PrefersRelaxation = true,
                    TravelingWithChildren = false,
                    TravelingWithElders = false,
                    TravelingAlone = true,
                    TravelingAsCouple = false,
                    TravelingWithFriends = false,
                    LastUpdated = DateTime.UtcNow
                };

                _context.UserPreferences.Add(preferences);
                await _context.SaveChangesAsync();
            }

            return preferences;
        }

        public async Task SaveUserPreferencesAsync(UserPreference preferences)
        {
            var existingPreferences = await _context.UserPreferences
                .FirstOrDefaultAsync(p => p.UserId == preferences.UserId);

            if (existingPreferences != null)
            {
                // Update existing preferences
                existingPreferences.PrefersTours = preferences.PrefersTours;
                existingPreferences.PrefersIndependentTravel = preferences.PrefersIndependentTravel;
                existingPreferences.MinBudget = preferences.MinBudget;
                existingPreferences.MaxBudget = preferences.MaxBudget;
                existingPreferences.MinDuration = preferences.MinDuration;
                existingPreferences.MaxDuration = preferences.MaxDuration;
                existingPreferences.PreferredLocationIds = preferences.PreferredLocationIds;
                existingPreferences.PrefersNature = preferences.PrefersNature;
                existingPreferences.PrefersHistory = preferences.PrefersHistory;
                existingPreferences.PrefersFood = preferences.PrefersFood;
                existingPreferences.PrefersAdventure = preferences.PrefersAdventure;
                existingPreferences.PrefersRelaxation = preferences.PrefersRelaxation;
                existingPreferences.PreferredStarRating = preferences.PreferredStarRating;

                // Cập nhật thông tin chi tiết hơn
                existingPreferences.HasVisitedBenTreBefore = preferences.HasVisitedBenTreBefore;
                existingPreferences.HasVisitedMekongDeltaBefore = preferences.HasVisitedMekongDeltaBefore;
                existingPreferences.InterestedInCooking = preferences.InterestedInCooking;
                existingPreferences.InterestedInCrafts = preferences.InterestedInCrafts;
                existingPreferences.InterestedInFarming = preferences.InterestedInFarming;
                existingPreferences.InterestedInBoating = preferences.InterestedInBoating;
                existingPreferences.InterestedInCycling = preferences.InterestedInCycling;
                existingPreferences.InterestedInFishing = preferences.InterestedInFishing;
                existingPreferences.HasDietaryRestrictions = preferences.HasDietaryRestrictions;
                existingPreferences.DietaryRestrictions = preferences.DietaryRestrictions;
                existingPreferences.AccessibilityNeeds = preferences.AccessibilityNeeds;
                existingPreferences.TravelingWithChildren = preferences.TravelingWithChildren;
                existingPreferences.TravelingWithElders = preferences.TravelingWithElders;
                existingPreferences.TravelingAlone = preferences.TravelingAlone;
                existingPreferences.TravelingAsCouple = preferences.TravelingAsCouple;
                existingPreferences.TravelingWithFriends = preferences.TravelingWithFriends;

                // Cập nhật thời gian
                existingPreferences.LastUpdated = DateTime.UtcNow;
            }
            else
            {
                // Add new preferences
                preferences.LastUpdated = DateTime.UtcNow;
                _context.UserPreferences.Add(preferences);
            }

            await _context.SaveChangesAsync();
        }

        public async Task TrackSearchHistoryAsync(SearchHistory searchHistory)
        {
            // Thêm lịch sử tìm kiếm
            _context.SearchHistories.Add(searchHistory);
            await _context.SaveChangesAsync();

            // Cập nhật thông tin sở thích người dùng nếu có userId
            if (!string.IsNullOrEmpty(searchHistory.UserId))
            {
                var userPreference = await GetUserPreferencesAsync(searchHistory.UserId);

                // Cập nhật tần suất tìm kiếm
                if (searchHistory.RelatedToNature == true)
                {
                    userPreference.NatureSearchCount++;
                }

                if (searchHistory.RelatedToHistory == true)
                {
                    userPreference.HistorySearchCount++;
                }

                if (searchHistory.RelatedToFood == true)
                {
                    userPreference.FoodSearchCount++;
                }

                if (searchHistory.RelatedToAdventure == true)
                {
                    userPreference.AdventureSearchCount++;
                }

                if (searchHistory.RelatedToRelaxation == true)
                {
                    userPreference.RelaxationSearchCount++;
                }

                // Cập nhật sở thích dựa trên tần suất tìm kiếm
                if (userPreference.NatureSearchCount > 5) userPreference.PrefersNature = true;
                if (userPreference.HistorySearchCount > 5) userPreference.PrefersHistory = true;
                if (userPreference.FoodSearchCount > 5) userPreference.PrefersFood = true;
                if (userPreference.AdventureSearchCount > 5) userPreference.PrefersAdventure = true;
                if (userPreference.RelaxationSearchCount > 5) userPreference.PrefersRelaxation = true;

                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<SearchHistory>> GetUserSearchHistoryAsync(string userId, int count = 10)
        {
            return await _context.SearchHistories
                .Include(sh => sh.Location!).ThenInclude(l => l.City)
                .Where(sh => sh.UserId == userId)
                .OrderByDescending(sh => sh.SearchDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<Location>> GetPopularLocationsAsync(int count = 5)
        {
            // Get popular locations based on search history and bookings
            var popularLocationIds = await _context.SearchHistories
                .Where(sh => sh.LocationId.HasValue)
                .GroupBy(sh => sh.LocationId)
                .OrderByDescending(g => g.Count())
                .Select(g => g.Key!.Value)
                .Take(count)
                .ToListAsync();

            var locations = new List<Location>();
            foreach (var id in popularLocationIds)
            {
                var location = await _context.Locations
                    .Include(l => l.City)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (location != null)
                {
                    locations.Add(location);
                }
            }

            // If not enough locations found, add some default ones
            if (locations.Count < count)
            {
                var additionalLocations = await _context.Locations
                    .Include(l => l.City)
                    .Where(l => !popularLocationIds.Contains(l.Id))
                    .OrderByDescending(l => l.Id) // Simplified ordering to avoid null reference
                    .Take(count - locations.Count)
                    .ToListAsync();

                locations.AddRange(additionalLocations);
            }

            return locations;
        }

        public async Task<Dictionary<int, Tour>> GetRelatedToursAsync(string tourIds)
        {
            if (string.IsNullOrEmpty(tourIds))
            {
                return new Dictionary<int, Tour>();
            }

            var ids = tourIds.Split(',').Select(id => int.Parse(id)).ToList();
            var tours = await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .Where(t => ids.Contains(t.Id))
                .ToListAsync();

            return tours.ToDictionary(t => t.Id);
        }

        public async Task<Dictionary<int, Accommodation>> GetRelatedAccommodationsAsync(string accommodationIds)
        {
            if (string.IsNullOrEmpty(accommodationIds))
            {
                return new Dictionary<int, Accommodation>();
            }

            var ids = accommodationIds.Split(',').Select(id => int.Parse(id)).ToList();
            var accommodations = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Where(a => ids.Contains(a.Id))
                .ToListAsync();

            return accommodations.ToDictionary(a => a.Id);
        }

        public async Task<Dictionary<int, Service>> GetRelatedServicesAsync(string serviceIds)
        {
            if (string.IsNullOrEmpty(serviceIds))
            {
                return new Dictionary<int, Service>();
            }

            var ids = serviceIds.Split(',').Select(id => int.Parse(id)).ToList();
            var services = await _context.Services
                .Include(s => s.Location)
                .Include(s => s.City)
                .Where(s => ids.Contains(s.Id))
                .ToListAsync();

            return services.ToDictionary(s => s.Id);
        }

        public async Task<Dictionary<int, Location>> GetRelatedLocationsAsync(string locationIds)
        {
            if (string.IsNullOrEmpty(locationIds))
            {
                return new Dictionary<int, Location>();
            }

            var ids = locationIds.Split(',').Select(id => int.Parse(id)).ToList();
            var locations = await _context.Locations
                .Include(l => l.City)
                .Where(l => ids.Contains(l.Id))
                .ToListAsync();

            return locations.ToDictionary(l => l.Id);
        }

        public async Task<Dictionary<int, Vehicle>> GetRelatedVehiclesAsync(string vehicleIds)
        {
            if (string.IsNullOrEmpty(vehicleIds))
            {
                return new Dictionary<int, Vehicle>();
            }

            var ids = vehicleIds.Split(',').Select(id => int.Parse(id)).ToList();
            var vehicles = await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Where(v => ids.Contains(v.Id))
                .ToListAsync();

            return vehicles.ToDictionary(v => v.Id);
        }

        public async Task<(int total, int saved, int viewed)> GetUserRecommendationStatsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return (0, 0, 0);
            }

            var total = await _context.TravelRecommendations
                .CountAsync(r => r.UserId == userId);

            var saved = await _context.TravelRecommendations
                .CountAsync(r => r.UserId == userId && r.IsSaved);

            var viewed = await _context.TravelRecommendations
                .CountAsync(r => r.UserId == userId && r.IsViewed);

            return (total, saved, viewed);
        }

        public async Task<List<TravelRecommendation>> GetSimilarRecommendationsAsync(int recommendationId, string? userId = null, int count = 3)
        {
            var recommendation = await _context.TravelRecommendations.FindAsync(recommendationId);
            if (recommendation == null)
            {
                return new List<TravelRecommendation>();
            }

            var query = _context.TravelRecommendations.AsQueryable();

            // Lọc theo ID
            query = query.Where(r => r.Id != recommendationId);

            // Nếu có userId, ưu tiên các gợi ý của người dùng đó
            if (!string.IsNullOrEmpty(userId))
            {
                var userRecommendations = await query
                    .Where(r => r.UserId == userId)
                    .OrderByDescending(r => r.CreatedDate)
                    .Take(count)
                    .ToListAsync();

                if (userRecommendations.Count >= count)
                {
                    return userRecommendations;
                }

                // Nếu không đủ, lấy thêm các gợi ý tương tự
                count -= userRecommendations.Count;

                var additionalRecommendations = await query
                    .Where(r => r.UserId != userId &&
                           (r.RecommendationType == recommendation.RecommendationType ||
                            r.IncludesNature == recommendation.IncludesNature ||
                            r.IncludesHistory == recommendation.IncludesHistory ||
                            r.IncludesFood == recommendation.IncludesFood ||
                            r.IncludesAdventure == recommendation.IncludesAdventure ||
                            r.IncludesRelaxation == recommendation.IncludesRelaxation))
                    .OrderByDescending(r => r.CreatedDate)
                    .Take(count)
                    .ToListAsync();

                userRecommendations.AddRange(additionalRecommendations);
                return userRecommendations;
            }

            // Nếu không có userId, lấy các gợi ý tương tự
            return await query
                .Where(r => r.RecommendationType == recommendation.RecommendationType ||
                       r.IncludesNature == recommendation.IncludesNature ||
                       r.IncludesHistory == recommendation.IncludesHistory ||
                       r.IncludesFood == recommendation.IncludesFood ||
                       r.IncludesAdventure == recommendation.IncludesAdventure ||
                       r.IncludesRelaxation == recommendation.IncludesRelaxation)
                .OrderByDescending(r => r.CreatedDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task UpdateRecommendationViewStatsAsync(int recommendationId, string? userId = null)
        {
            var recommendation = await _context.TravelRecommendations.FindAsync(recommendationId);
            if (recommendation == null)
            {
                return;
            }

            // Cập nhật thông tin xem
            recommendation.IsViewed = true;
            recommendation.ViewCount++;
            recommendation.LastViewedDate = DateTime.UtcNow;

            // Lưu thay đổi
            await _context.SaveChangesAsync();

            // Nếu có userId, lưu lịch sử xem
            if (!string.IsNullOrEmpty(userId))
            {
                var searchHistory = new SearchHistory
                {
                    UserId = userId,
                    SearchDate = DateTime.UtcNow,
                    SearchType = "RecommendationView",
                    RelatedToNature = recommendation.IncludesNature,
                    RelatedToHistory = recommendation.IncludesHistory,
                    RelatedToFood = recommendation.IncludesFood,
                    RelatedToAdventure = recommendation.IncludesAdventure,
                    RelatedToRelaxation = recommendation.IncludesRelaxation,
                    InterestLevel = 4, // Mức độ quan tâm cao
                    ResultClicked = true
                };

                await TrackSearchHistoryAsync(searchHistory);
            }
        }

        public async Task<Dictionary<string, int>> GetUserTopInterestsAsync(string userId, int count = 5)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return new Dictionary<string, int>();
            }

            // Lấy thông tin sở thích người dùng
            var preferences = await GetUserPreferencesAsync(userId);

            // Tạo từ điển sở thích
            var interests = new Dictionary<string, int>
            {
                { "Nature", preferences.NatureSearchCount },
                { "History", preferences.HistorySearchCount },
                { "Food", preferences.FoodSearchCount },
                { "Adventure", preferences.AdventureSearchCount },
                { "Relaxation", preferences.RelaxationSearchCount }
            };

            // Sắp xếp theo tần suất tìm kiếm
            return interests
                .OrderByDescending(i => i.Value)
                .Take(count)
                .ToDictionary(i => i.Key, i => i.Value);
        }

        private async Task AddAIRecommendation(
            List<TravelRecommendation> recommendations,
            UserPreference preferences,
            string userId,
            List<Location> popularLocations,
            List<Tour> popularTours,
            List<Accommodation> popularAccommodations,
            List<SearchHistory> searchHistory,
            List<Booking> bookings,
            List<TourBooking> tourBookings,
            List<ServiceBooking> serviceBookings,
            List<VehicleBooking> vehicleBookings,
            List<Review> reviews)
        {
            try
            {
                // Tạo thông tin về sở thích người dùng - cải thiện định dạng để rõ ràng hơn và dễ đọc
                var userPreferences = new StringBuilder();
                userPreferences.AppendLine("SỞ THÍCH DU LỊCH (Những sở thích được chọn):");
                userPreferences.AppendLine("----------------------------------------------");

                // Loại hình du lịch - đảm bảo rõ ràng về lựa chọn
                userPreferences.AppendLine("LOẠI HÌNH DU LỊCH:");
                userPreferences.AppendLine($"- Tour có hướng dẫn viên: {(preferences.PrefersTours ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Du lịch tự túc: {(preferences.PrefersIndependentTravel ? "CÓ" : "KHÔNG")}");

                // Loại hoạt động - sử dụng định dạng rõ ràng hơn
                userPreferences.AppendLine("\nLOẠI HOẠT ĐỘNG:");
                userPreferences.AppendLine($"- Khám phá thiên nhiên: {(preferences.PrefersNature ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Tìm hiểu lịch sử và văn hóa: {(preferences.PrefersHistory ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Khám phá ẩm thực địa phương: {(preferences.PrefersFood ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Các hoạt động mạo hiểm: {(preferences.PrefersAdventure ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Nghỉ dưỡng và thư giãn: {(preferences.PrefersRelaxation ? "CÓ" : "KHÔNG")}");

                // Thông tin ngân sách
                userPreferences.AppendLine("\nNGÂN SÁCH:");
                if (preferences.MinBudget.HasValue && preferences.MaxBudget.HasValue)
                    userPreferences.AppendLine($"- Ngân sách dự kiến: {preferences.MinBudget:N0} VND - {preferences.MaxBudget:N0} VND");
                else
                    userPreferences.AppendLine("- Ngân sách: Không xác định");

                // Thông tin thời gian
                userPreferences.AppendLine("\nTHỜI GIAN:");
                if (preferences.MinDuration.HasValue && preferences.MaxDuration.HasValue)
                    userPreferences.AppendLine($"- Thời gian dự kiến: {preferences.MinDuration} - {preferences.MaxDuration} ngày");
                else
                    userPreferences.AppendLine("- Thời gian: Không xác định");

                // Thông tin đối tượng đi cùng
                userPreferences.AppendLine("\nĐỐI TƯỢNG ĐI CÙNG:");
                userPreferences.AppendLine($"- Đi cùng trẻ em: {(preferences.TravelingWithChildren ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Đi cùng người lớn tuổi: {(preferences.TravelingWithElders ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Đi một mình: {(preferences.TravelingAlone ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Đi cùng người yêu/vợ/chồng: {(preferences.TravelingAsCouple ? "CÓ" : "KHÔNG")}");
                userPreferences.AppendLine($"- Đi cùng bạn bè: {(preferences.TravelingWithFriends ? "CÓ" : "KHÔNG")}");

                // Tạo thông tin về lịch sử du lịch
                var travelHistory = new StringBuilder();

                if (!string.IsNullOrEmpty(userId))
                {
                    if (bookings.Count > 0)
                    {
                        travelHistory.AppendLine("Lịch sử đặt phòng:");
                        foreach (var booking in bookings.Take(3))
                        {
                            var bookingDetails = await _context.BookingDetails
                                .Include(bd => bd.Room)
                                .ThenInclude(r => r.Accommodation)
                                .ThenInclude(a => a.Location)
                                .Where(bd => bd.BookingId == booking.Id)
                                .ToListAsync();

                            if (bookingDetails.Count > 0)
                            {
                                var accommodation = bookingDetails.First().Room.Accommodation;
                                travelHistory.AppendLine($"- Đã đặt {accommodation.Name} tại {accommodation.Location?.Name} vào {booking.BookingDate.ToString("dd/MM/yyyy")}");
                            }
                        }
                    }

                    if (tourBookings.Count > 0)
                    {
                        travelHistory.AppendLine("Lịch sử đặt tour:");
                        foreach (var booking in tourBookings.Take(3))
                        {
                            var tourBookingDetails = await _context.TourBookingDetails
                                .Include(tbd => tbd.Tour)
                                .ThenInclude(t => t.Location)
                                .Where(tbd => tbd.TourBookingId == booking.Id)
                                .ToListAsync();

                            if (tourBookingDetails.Count > 0)
                            {
                                var tour = tourBookingDetails.First().Tour;
                                travelHistory.AppendLine($"- Đã đặt tour {tour.Name} tại {tour.Location?.Name} vào {booking.BookingDate.ToString("dd/MM/yyyy")}");
                            }
                        }
                    }

                    if (reviews.Count > 0)
                    {
                        travelHistory.AppendLine("Đánh giá gần đây:");
                        foreach (var review in reviews.Take(3))
                        {
                            travelHistory.AppendLine($"- Đánh giá {review.Rating}/5 sao: \"{review.Comment}\"");
                        }
                    }
                }

                // Nếu không có lịch sử, thêm thông tin mặc định
                if (travelHistory.Length == 0)
                {
                    travelHistory.AppendLine("Chưa có lịch sử du lịch trước đây tại Bến Tre.");
                }

                // Gọi AI để tạo gợi ý cá nhân hóa
                string aiResponse;

                try
                {
                    // Thử sử dụng Ollama trước
                    if (!string.IsNullOrEmpty(preferences.AIPrompt))
                    {
                        // Sử dụng prompt tùy chỉnh của người dùng
                        aiResponse = await _ollamaService.GeneratePersonalizedRecommendation(
                            userPreferences.ToString(),
                            travelHistory.ToString(),
                            "Bến Tre, " + preferences.AIPrompt);
                    }
                    else
                    {
                        // Sử dụng prompt mặc định
                        aiResponse = await _ollamaService.GeneratePersonalizedRecommendation(
                            userPreferences.ToString(),
                            travelHistory.ToString(),
                            "Bến Tre");
                    }

                    // Kiểm tra xem phản hồi từ Ollama có hợp lệ không
                    if (string.IsNullOrWhiteSpace(aiResponse) || aiResponse.Contains("Rất tiếc"))
                    {
                        Console.WriteLine("Ollama response was empty or contained an error. Falling back to OpenAI.");
                        throw new Exception("Ollama response was invalid");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error using Ollama: {ex.Message}. Falling back to OpenAI.");

                    // Fallback to OpenAI if Ollama fails
                    if (!string.IsNullOrEmpty(preferences.AIPrompt))
                    {
                        // Sử dụng prompt tùy chỉnh của người dùng
                        aiResponse = await _openAIService.GeneratePersonalizedRecommendation(
                            userPreferences.ToString(),
                            travelHistory.ToString(),
                            "Bến Tre, " + preferences.AIPrompt);
                    }
                    else
                    {
                        // Sử dụng prompt mặc định
                        aiResponse = await _openAIService.GeneratePersonalizedRecommendation(
                            userPreferences.ToString(),
                            travelHistory.ToString(),
                            "Bến Tre");
                    }
                }

                // Log the preferences for debugging
                Console.WriteLine("=== Creating AI Recommendation ===");
                Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
                Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
                Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
                Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
                Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
                Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
                Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
                Console.WriteLine($"InterestedInCooking: {preferences.InterestedInCooking}");
                Console.WriteLine($"InterestedInCrafts: {preferences.InterestedInCrafts}");
                Console.WriteLine($"InterestedInFarming: {preferences.InterestedInFarming}");
                Console.WriteLine($"InterestedInBoating: {preferences.InterestedInBoating}");
                Console.WriteLine($"InterestedInCycling: {preferences.InterestedInCycling}");
                Console.WriteLine($"InterestedInFishing: {preferences.InterestedInFishing}");
                Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
                Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
                Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
                Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
                Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");

                // Tạo gợi ý từ AI
                var aiRecommendation = new TravelRecommendation
                {
                    UserId = userId,
                    Title = "Gợi Ý Du Lịch Bến Tre Từ AI",
                    Description = "Lịch trình du lịch được tạo bởi AI dựa trên sở thích và lịch sử du lịch của bạn.",
                    RecommendationType = preferences.PrefersTours ? "Guided Tour" : (preferences.PrefersIndependentTravel ? "Independent Travel" : "Mixed"),
                    EstimatedMinBudget = preferences.MinBudget ?? 500000,
                    EstimatedMaxBudget = preferences.MaxBudget ?? 2000000,
                    RecommendedDuration = preferences.MinDuration ?? 3,

                    // Thêm thông tin chi tiết về loại gợi ý - đảm bảo khớp chính xác với sở thích người dùng
                    IncludesNature = preferences.PrefersNature,
                    IncludesHistory = preferences.PrefersHistory,
                    IncludesFood = preferences.PrefersFood,
                    IncludesAdventure = preferences.PrefersAdventure,
                    IncludesRelaxation = preferences.PrefersRelaxation,

                    // Thông tin về đối tượng phù hợp
                    SuitableForChildren = preferences.TravelingWithChildren,
                    SuitableForElders = preferences.TravelingWithElders,
                    ForSoloTravelers = preferences.TravelingAlone,
                    ForCouples = preferences.TravelingAsCouple,
                    ForFriends = preferences.TravelingWithFriends,

                    // Nguồn gợi ý
                    RecommendationSource = "AI",

                    // Mức độ ưu tiên
                    Priority = 10,

                    // Nội dung chi tiết từ AI
                    ItineraryDetails = aiResponse,

                    // Thêm các địa điểm, tour, chỗ ở liên quan
                    RelatedLocationIds = string.Join(",", popularLocations.Take(3).Select(l => l.Id)),
                    RelatedTourIds = string.Join(",", popularTours.Take(2).Select(t => t.Id)),
                    RelatedAccommodationIds = string.Join(",", popularAccommodations.Take(2).Select(a => a.Id))
                };

                // Kiểm tra xem phản hồi có chứa thông báo lỗi không
                if (aiResponse.Contains("Rất tiếc, chúng tôi không thể tạo gợi ý") ||
                    aiResponse.Contains("Lỗi:") ||
                    aiResponse.Contains("không thể kết nối"))
                {
                    Console.WriteLine("OpenAI API returned an error message: " + aiResponse);

                    // Tạo gợi ý thay thế trong trường hợp lỗi
                    var fallbackRecommendation = CreateFallbackRecommendation(preferences, userId, popularLocations, popularTours, popularAccommodations);
                    fallbackRecommendation.Title = "Gợi Ý Lịch Trình Bến Tre (Dự phòng)";
                    fallbackRecommendation.Description = "Lịch trình được tạo dựa trên sở thích của bạn. Gợi ý AI không khả dụng do lỗi kỹ thuật.";
                    recommendations.Add(fallbackRecommendation);

                    Console.WriteLine("Using fallback recommendation due to API error");
                    // Không tiếp tục xử lý
                    return;
                }

                // Ghi log thành công
                Console.WriteLine($"Received AI response. Length: {aiResponse?.Length ?? 0} characters");

                // Kiểm tra và xác thực phản hồi từ AI
                Console.WriteLine("Validating AI response...");
                bool isValidResponse = !string.IsNullOrEmpty(aiResponse) && ValidateAIResponse(aiResponse, preferences);

                if (isValidResponse)
                {
                    Console.WriteLine("AI response is valid. Adding to recommendations.");
                    // Thêm vào danh sách gợi ý nếu hợp lệ
                    recommendations.Add(aiRecommendation);
                }
                else
                {
                    Console.WriteLine("AI response is invalid. Creating fallback recommendation.");
                    // Nếu phản hồi không hợp lệ, tạo một gợi ý thay thế
                    var fallbackRecommendation = CreateFallbackRecommendation(preferences, userId, popularLocations, popularTours, popularAccommodations);
                    fallbackRecommendation.Title = "Gợi Ý Lịch Trình Bến Tre (Dự phòng)";
                    fallbackRecommendation.Description = "Lịch trình được tạo dựa trên sở thích của bạn. Gợi ý AI không khả dụng.";
                    recommendations.Add(fallbackRecommendation);

                    // Ghi log về việc sử dụng gợi ý thay thế
                    Console.WriteLine("Using fallback recommendation due to invalid AI response");
                }
            }
            catch (Exception ex)
            {
                // Log lỗi nhưng không làm gián đoạn quá trình tạo gợi ý
                Console.WriteLine($"Error generating AI recommendation: {ex.Message}");

                // Tạo gợi ý thay thế trong trường hợp lỗi
                try
                {
                    var fallbackRecommendation = CreateFallbackRecommendation(preferences, userId, popularLocations, popularTours, popularAccommodations);
                    recommendations.Add(fallbackRecommendation);
                }
                catch (Exception fallbackEx)
                {
                    Console.WriteLine($"Error creating fallback recommendation: {fallbackEx.Message}");
                }
            }
        }
        // Phương thức xác thực phản hồi từ AI
        private bool ValidateAIResponse(string aiResponse, UserPreference preferences)
        {
            try
            {
                // Kiểm tra xem phản hồi có rỗng không
                if (string.IsNullOrWhiteSpace(aiResponse))
                {
                    Console.WriteLine("AI response is empty or whitespace");
                    return false;
                }

                // Kiểm tra độ dài tối thiểu
                if (aiResponse.Length < 200)
                {
                    Console.WriteLine($"AI response is too short: {aiResponse.Length} characters");
                    return false;
                }

                // Chuyển đổi phản hồi sang chữ thường để dễ dàng tìm kiếm
                string lowerResponse = aiResponse.ToLower();

                // Kiểm tra xem phản hồi có phù hợp với sở thích người dùng không
                bool containsValidContent = true;
                List<string> validationErrors = new List<string>();

                // Kiểm tra ngân sách
                if (preferences.MinBudget.HasValue && preferences.MaxBudget.HasValue)
                {
                    string minBudgetStr = preferences.MinBudget.Value.ToString("N0");
                    string maxBudgetStr = preferences.MaxBudget.Value.ToString("N0");
                    string simplifiedMinBudget = preferences.MinBudget.Value.ToString();
                    string simplifiedMaxBudget = preferences.MaxBudget.Value.ToString();

                    // Kiểm tra xem phản hồi có đề cập đến ngân sách trong khoảng người dùng chọn không
                    bool mentionsBudget = lowerResponse.Contains(minBudgetStr.ToLower()) ||
                                         lowerResponse.Contains(maxBudgetStr.ToLower()) ||
                                         lowerResponse.Contains(simplifiedMinBudget) ||
                                         lowerResponse.Contains(simplifiedMaxBudget) ||
                                         lowerResponse.Contains("ngân sách") ||
                                         lowerResponse.Contains("chi phí");

                    if (!mentionsBudget)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến ngân sách phù hợp");
                    }
                }

                // Kiểm tra thời gian
                if (preferences.MinDuration.HasValue && preferences.MaxDuration.HasValue)
                {
                    bool mentionsDuration = false;

                    // Kiểm tra các ngày trong khoảng
                    for (int i = preferences.MinDuration.Value; i <= preferences.MaxDuration.Value; i++)
                    {
                        if (lowerResponse.Contains($"{i} ngày") ||
                            lowerResponse.Contains($"ngày {i}") ||
                            lowerResponse.Contains($"{i}-ngày") ||
                            lowerResponse.Contains($"{i} day") ||
                            lowerResponse.Contains($"day {i}"))
                        {
                            mentionsDuration = true;
                            break;
                        }
                    }

                    if (!mentionsDuration)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến thời gian phù hợp");
                    }
                }

                // Kiểm tra đối tượng đi cùng
                if (preferences.TravelingWithChildren)
                {
                    bool mentionsChildren = lowerResponse.Contains("trẻ em") ||
                                           lowerResponse.Contains("gia đình") ||
                                           lowerResponse.Contains("thân thiện với trẻ em") ||
                                           lowerResponse.Contains("phù hợp cho trẻ em") ||
                                           lowerResponse.Contains("children") ||
                                           lowerResponse.Contains("family");

                    if (!mentionsChildren)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến hoạt động phù hợp cho trẻ em");
                    }
                }

                // Kiểm tra loại hình du lịch - xử lý cả trường hợp người dùng chọn cả hai loại
                if (preferences.PrefersTours && preferences.PrefersIndependentTravel)
                {
                    // Nếu người dùng chọn cả hai, phản hồi phải đề cập đến cả hai loại
                    bool mentionsGuidedTour = lowerResponse.Contains("hướng dẫn viên") ||
                                             lowerResponse.Contains("tour guide") ||
                                             lowerResponse.Contains("tour có hướng dẫn") ||
                                             lowerResponse.Contains("tour được hướng dẫn") ||
                                             lowerResponse.Contains("guided tour");

                    bool mentionsIndependentTravel = lowerResponse.Contains("tự túc") ||
                                                    lowerResponse.Contains("tự khám phá") ||
                                                    lowerResponse.Contains("tự do khám phá") ||
                                                    lowerResponse.Contains("tự mình") ||
                                                    lowerResponse.Contains("independent travel") ||
                                                    lowerResponse.Contains("self-guided");

                    if (!mentionsGuidedTour || !mentionsIndependentTravel)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến cả tour có hướng dẫn và du lịch tự túc");
                    }
                }
                // Nếu người dùng chỉ thích tour có hướng dẫn nhưng không thích du lịch tự túc
                else if (preferences.PrefersTours && !preferences.PrefersIndependentTravel)
                {
                    // Kiểm tra xem phản hồi có đề cập đến tour có hướng dẫn không
                    bool mentionsGuidedTour = lowerResponse.Contains("hướng dẫn viên") ||
                                             lowerResponse.Contains("tour guide") ||
                                             lowerResponse.Contains("tour có hướng dẫn") ||
                                             lowerResponse.Contains("tour được hướng dẫn") ||
                                             lowerResponse.Contains("guided tour");

                    if (!mentionsGuidedTour)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến tour có hướng dẫn");
                    }
                }
                // Nếu người dùng chỉ thích du lịch tự túc nhưng không thích tour có hướng dẫn
                else if (!preferences.PrefersTours && preferences.PrefersIndependentTravel)
                {
                    // Kiểm tra xem phản hồi có đề cập đến du lịch tự túc không
                    bool mentionsIndependentTravel = lowerResponse.Contains("tự túc") ||
                                                    lowerResponse.Contains("tự khám phá") ||
                                                    lowerResponse.Contains("tự do khám phá") ||
                                                    lowerResponse.Contains("tự mình") ||
                                                    lowerResponse.Contains("independent travel") ||
                                                    lowerResponse.Contains("self-guided");

                    if (!mentionsIndependentTravel)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến du lịch tự túc");
                    }
                }

                // Kiểm tra các sở thích về hoạt động - mở rộng các từ khóa tìm kiếm
                if (preferences.PrefersNature)
                {
                    bool mentionsNature = lowerResponse.Contains("thiên nhiên") ||
                                         lowerResponse.Contains("tự nhiên") ||
                                         lowerResponse.Contains("cây cối") ||
                                         lowerResponse.Contains("rừng") ||
                                         lowerResponse.Contains("sông nước") ||
                                         lowerResponse.Contains("vườn") ||
                                         lowerResponse.Contains("nature") ||
                                         lowerResponse.Contains("natural");

                    if (!mentionsNature)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến hoạt động thiên nhiên");
                    }
                }

                if (preferences.PrefersHistory)
                {
                    bool mentionsHistory = lowerResponse.Contains("lịch sử") ||
                                          lowerResponse.Contains("văn hóa") ||
                                          lowerResponse.Contains("di tích") ||
                                          lowerResponse.Contains("truyền thống") ||
                                          lowerResponse.Contains("history") ||
                                          lowerResponse.Contains("cultural") ||
                                          lowerResponse.Contains("heritage");

                    if (!mentionsHistory)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến hoạt động lịch sử/văn hóa");
                    }
                }

                if (preferences.PrefersFood)
                {
                    bool mentionsFood = lowerResponse.Contains("ẩm thực") ||
                                       lowerResponse.Contains("món ăn") ||
                                       lowerResponse.Contains("đặc sản") ||
                                       lowerResponse.Contains("nhà hàng") ||
                                       lowerResponse.Contains("quán ăn") ||
                                       lowerResponse.Contains("food") ||
                                       lowerResponse.Contains("cuisine") ||
                                       lowerResponse.Contains("culinary");

                    if (!mentionsFood)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến ẩm thực");
                    }
                }

                if (preferences.PrefersAdventure)
                {
                    bool mentionsAdventure = lowerResponse.Contains("mạo hiểm") ||
                                            lowerResponse.Contains("phiêu lưu") ||
                                            lowerResponse.Contains("thử thách") ||
                                            lowerResponse.Contains("khám phá") ||
                                            lowerResponse.Contains("adventure") ||
                                            lowerResponse.Contains("exciting") ||
                                            lowerResponse.Contains("thrill");

                    if (!mentionsAdventure)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến hoạt động mạo hiểm");
                    }
                }

                if (preferences.PrefersRelaxation)
                {
                    bool mentionsRelaxation = lowerResponse.Contains("thư giãn") ||
                                             lowerResponse.Contains("nghỉ dưỡng") ||
                                             lowerResponse.Contains("spa") ||
                                             lowerResponse.Contains("massage") ||
                                             lowerResponse.Contains("thư thái") ||
                                             lowerResponse.Contains("relaxation") ||
                                             lowerResponse.Contains("relaxing") ||
                                             lowerResponse.Contains("rest");

                    if (!mentionsRelaxation)
                    {
                        containsValidContent = false;
                        validationErrors.Add("Không đề cập đến hoạt động thư giãn/nghỉ dưỡng");
                    }
                }

                // Ghi log các lỗi xác thực nếu có
                if (!containsValidContent)
                {
                    Console.WriteLine("AI response validation failed with the following errors:");
                    foreach (var error in validationErrors)
                    {
                        Console.WriteLine($"- {error}");
                    }
                }

                return containsValidContent;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error validating AI response: {ex.Message}");
                return false;
            }
        }

        // Phương thức tạo gợi ý thay thế khi AI không hoạt động hoặc không phù hợp
        private TravelRecommendation CreateFallbackRecommendation(
            UserPreference preferences,
            string userId,
            List<Location> popularLocations,
            List<Tour> popularTours,
            List<Accommodation> popularAccommodations)
        {
            // Xác định số ngày dựa trên sở thích người dùng
            int recommendedDuration = preferences.MinDuration ?? 3;
            if (preferences.MinDuration.HasValue && preferences.MaxDuration.HasValue)
            {
                // Lấy giá trị trung bình của khoảng thời gian
                recommendedDuration = (preferences.MinDuration.Value + preferences.MaxDuration.Value) / 2;
            }

            var fallbackRecommendation = new TravelRecommendation
            {
                UserId = userId,
                Title = "Gợi Ý Du Lịch Bến Tre Phù Hợp Với Sở Thích Của Bạn",
                Description = "Lịch trình du lịch được tạo chính xác dựa trên tất cả các sở thích bạn đã chọn.",
                RecommendationType = preferences.PrefersTours ?
                                    (preferences.PrefersIndependentTravel ? "Mixed" : "Guided Tour") :
                                    "Independent Travel",
                EstimatedMinBudget = preferences.MinBudget ?? 500000,
                EstimatedMaxBudget = preferences.MaxBudget ?? 2000000,
                RecommendedDuration = recommendedDuration,

                // Thêm thông tin chi tiết về loại gợi ý
                IncludesNature = preferences.PrefersNature,
                IncludesHistory = preferences.PrefersHistory,
                IncludesFood = preferences.PrefersFood,
                IncludesAdventure = preferences.PrefersAdventure,
                IncludesRelaxation = preferences.PrefersRelaxation,

                // Thông tin về đối tượng phù hợp
                SuitableForChildren = preferences.TravelingWithChildren,
                SuitableForElders = preferences.TravelingWithElders,

                // Nguồn gợi ý
                RecommendationSource = "System",

                // Mức độ ưu tiên
                Priority = 5,

                // Thêm các địa điểm, tour, chỗ ở liên quan
                RelatedLocationIds = string.Join(",", popularLocations.Take(3).Select(l => l.Id)),
                RelatedTourIds = string.Join(",", popularTours.Take(2).Select(t => t.Id)),
                RelatedAccommodationIds = string.Join(",", popularAccommodations.Take(2).Select(a => a.Id))
            };

            // Tạo nội dung chi tiết dựa trên sở thích
            var itinerary = new StringBuilder();

            // Thêm tóm tắt ở đầu để xác nhận tất cả sở thích
            itinerary.AppendLine("# Lịch Trình Du Lịch Bến Tre Phù Hợp Với Sở Thích Của Bạn");
            itinerary.AppendLine();

            // Thêm thông tin tổng quan
            itinerary.AppendLine("## Tổng Quan Lịch Trình");
            itinerary.AppendLine($"- **Thời gian:** {recommendedDuration} ngày");
            itinerary.AppendLine($"- **Ngân sách:** {preferences.MinBudget?.ToString("N0")} - {preferences.MaxBudget?.ToString("N0")} VND");
            itinerary.AppendLine($"- **Loại hình du lịch:** {(preferences.PrefersTours && preferences.PrefersIndependentTravel ? "Kết hợp tour có hướng dẫn và du lịch tự túc" : (preferences.PrefersTours ? "Tour có hướng dẫn" : "Du lịch tự túc"))}");

            // Thêm thông tin về sở thích
            itinerary.AppendLine("- **Sở thích đã chọn:**");
            if (preferences.PrefersNature) itinerary.AppendLine("  + Khám phá thiên nhiên");
            if (preferences.PrefersHistory) itinerary.AppendLine("  + Tìm hiểu lịch sử và văn hóa");
            if (preferences.PrefersFood) itinerary.AppendLine("  + Khám phá ẩm thực địa phương");
            if (preferences.PrefersAdventure) itinerary.AppendLine("  + Các hoạt động mạo hiểm");
            if (preferences.PrefersRelaxation) itinerary.AppendLine("  + Nghỉ dưỡng và thư giãn");

            // Thêm thông tin về đối tượng đi cùng
            itinerary.AppendLine("- **Đối tượng đi cùng:**");
            if (preferences.TravelingWithChildren) itinerary.AppendLine("  + Đi cùng trẻ em");
            if (preferences.TravelingWithElders) itinerary.AppendLine("  + Đi cùng người lớn tuổi");
            if (preferences.TravelingAlone) itinerary.AppendLine("  + Đi một mình");
            if (preferences.TravelingAsCouple) itinerary.AppendLine("  + Đi cùng người yêu/vợ/chồng");
            if (preferences.TravelingWithFriends) itinerary.AppendLine("  + Đi cùng bạn bè");

            itinerary.AppendLine();

            // Tạo lịch trình theo ngày dựa trên số ngày đề xuất
            for (int day = 1; day <= recommendedDuration; day++)
            {
                // Xác định loại hoạt động cho ngày này
                bool isTourDay = preferences.PrefersTours && (day <= recommendedDuration / 2 || !preferences.PrefersIndependentTravel);
                bool isIndependentDay = preferences.PrefersIndependentTravel && (day > recommendedDuration / 2 || !preferences.PrefersTours);

                if (isTourDay)
                {
                    itinerary.AppendLine($"## Ngày {day}: Khám Phá Bến Tre Với Tour Có Hướng Dẫn");
                    itinerary.AppendLine("- Tham gia tour khám phá miệt vườn Bến Tre");
                    itinerary.AppendLine("- Tham quan các làng nghề truyền thống");

                    // Thêm hoạt động dựa trên sở thích
                    if (preferences.PrefersNature)
                        itinerary.AppendLine("- Tham quan vườn dừa và các khu sinh thái tự nhiên");

                    if (preferences.PrefersHistory)
                        itinerary.AppendLine("- Tham quan di tích lịch sử với hướng dẫn viên chuyên nghiệp");

                    if (preferences.PrefersFood)
                        itinerary.AppendLine("- Thưởng thức ẩm thực đặc sản địa phương trong tour");

                    if (preferences.PrefersAdventure)
                        itinerary.AppendLine("- Tham gia các hoạt động mạo hiểm như chèo thuyền, leo núi");

                    if (preferences.PrefersRelaxation)
                        itinerary.AppendLine("- Nghỉ ngơi tại các điểm dừng chân thư giãn");

                    if (preferences.TravelingWithChildren)
                        itinerary.AppendLine("- Tham gia các hoạt động thân thiện với trẻ em trong tour");

                    itinerary.AppendLine($"- Nghỉ đêm tại khách sạn phù hợp với ngân sách {preferences.MinBudget?.ToString("N0")} - {preferences.MaxBudget?.ToString("N0")} VND");
                    itinerary.AppendLine();
                }
                else if (isIndependentDay)
                {
                    itinerary.AppendLine($"## Ngày {day}: Khám Phá Tự Túc");
                    itinerary.AppendLine("- Thuê xe đạp/xe máy khám phá các con đường làng");

                    // Thêm hoạt động dựa trên sở thích
                    if (preferences.PrefersNature)
                        itinerary.AppendLine("- Tự do khám phá các khu vườn trái cây và cảnh quan thiên nhiên");

                    if (preferences.PrefersHistory)
                        itinerary.AppendLine("- Ghé thăm các di tích lịch sử và bảo tàng địa phương");

                    if (preferences.PrefersFood)
                        itinerary.AppendLine("- Thưởng thức ẩm thực đường phố và các quán ăn địa phương");

                    if (preferences.PrefersAdventure)
                        itinerary.AppendLine("- Tự tổ chức các hoạt động mạo hiểm như đi thuyền, leo núi");

                    if (preferences.PrefersRelaxation)
                        itinerary.AppendLine("- Dành thời gian thư giãn tại các quán cà phê ven sông");

                    if (preferences.TravelingWithChildren)
                        itinerary.AppendLine("- Ghé thăm các điểm vui chơi thân thiện với trẻ em");

                    itinerary.AppendLine($"- Nghỉ đêm tại homestay/khách sạn phù hợp với ngân sách {preferences.MinBudget?.ToString("N0")} - {preferences.MaxBudget?.ToString("N0")} VND");
                    itinerary.AppendLine();
                }
            }

            // Thêm phần chi tiết về ngân sách
            itinerary.AppendLine("## Chi Tiết Ngân Sách");
            itinerary.AppendLine($"Tổng ngân sách dự kiến: {preferences.MinBudget?.ToString("N0")} - {preferences.MaxBudget?.ToString("N0")} VND cho {recommendedDuration} ngày");
            itinerary.AppendLine("Bao gồm:");

            // Tính toán chi phí với xử lý đúng kiểu dữ liệu
            if (preferences.MinBudget.HasValue && preferences.MaxBudget.HasValue)
            {
                decimal minAccommodation = (decimal)(preferences.MinBudget.Value * 0.4m);
                decimal maxAccommodation = (decimal)(preferences.MaxBudget.Value * 0.4m);
                decimal minFood = (decimal)(preferences.MinBudget.Value * 0.3m);
                decimal maxFood = (decimal)(preferences.MaxBudget.Value * 0.3m);
                decimal minTransport = (decimal)(preferences.MinBudget.Value * 0.2m);
                decimal maxTransport = (decimal)(preferences.MaxBudget.Value * 0.2m);
                decimal minOther = (decimal)(preferences.MinBudget.Value * 0.1m);
                decimal maxOther = (decimal)(preferences.MaxBudget.Value * 0.1m);

                itinerary.AppendLine($"- Chi phí lưu trú: {minAccommodation.ToString("N0")} - {maxAccommodation.ToString("N0")} VND");
                itinerary.AppendLine($"- Chi phí ăn uống: {minFood.ToString("N0")} - {maxFood.ToString("N0")} VND");
                itinerary.AppendLine($"- Chi phí di chuyển và tour: {minTransport.ToString("N0")} - {maxTransport.ToString("N0")} VND");
                itinerary.AppendLine($"- Chi phí khác (vé tham quan, mua sắm): {minOther.ToString("N0")} - {maxOther.ToString("N0")} VND");
            }
            else
            {
                itinerary.AppendLine("- Chi phí lưu trú: 200,000 - 800,000 VND");
                itinerary.AppendLine("- Chi phí ăn uống: 150,000 - 600,000 VND");
                itinerary.AppendLine("- Chi phí di chuyển và tour: 100,000 - 400,000 VND");
                itinerary.AppendLine("- Chi phí khác (vé tham quan, mua sắm): 50,000 - 200,000 VND");
            }
            itinerary.AppendLine();

            // Thêm lưu ý khi du lịch
            itinerary.AppendLine("## Lưu Ý Khi Du Lịch Bến Tre");
            itinerary.AppendLine("- Mang theo kem chống nắng và mũ rộng vành");
            itinerary.AppendLine("- Nên mặc trang phục thoáng mát, dễ di chuyển");
            itinerary.AppendLine("- Mang theo thuốc chống côn trùng");
            itinerary.AppendLine("- Nên đặt phòng trước khi đến, đặc biệt vào mùa cao điểm");

            if (preferences.TravelingWithChildren)
            {
                itinerary.AppendLine("- Chuẩn bị thêm đồ dùng cần thiết cho trẻ em");
                itinerary.AppendLine("- Lựa chọn các hoạt động phù hợp với lứa tuổi của trẻ");
            }

            if (preferences.TravelingWithElders)
            {
                itinerary.AppendLine("- Đảm bảo lịch trình không quá mệt mỏi cho người lớn tuổi");
                itinerary.AppendLine("- Chọn các địa điểm dễ tiếp cận, không có nhiều bậc thang");
            }

            fallbackRecommendation.ItineraryDetails = itinerary.ToString();
            return fallbackRecommendation;
        }
    }
}
