﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class ServiceCartItem
    {
        public int ServiceId { get; set; }
        
        public string ServiceName { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        public int Quantity { get; set; } = 1;
        
        public DateTime ServiceDate { get; set; }
        
        public string? ImageUrl { get; set; }
        
        public int Duration { get; set; }
        
        [NotMapped]
        public decimal TotalPrice => Price * Quantity;
    }
}
