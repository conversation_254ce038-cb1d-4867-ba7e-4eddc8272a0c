﻿﻿using Microsoft.AspNetCore.Mvc;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class Panorama360Controller : Controller
    {
        private readonly IPanorama360Repository _panorama360Repository;
        private readonly ITourRepository _tourRepository;
        private readonly ILocationRepository _locationRepository;

        public Panorama360Controller(
            IPanorama360Repository panorama360Repository,
            ITourRepository tourRepository,
            ILocationRepository locationRepository)
        {
            _panorama360Repository = panorama360Repository;
            _tourRepository = tourRepository;
            _locationRepository = locationRepository;
        }

        // GET: Panorama360/Guide
        public IActionResult Guide()
        {
            return View();
        }

        // GET: Panorama360/Index
        public async Task<IActionResult> Index()
        {
            var panoramas = await _panorama360Repository.GetAllAsync();
            return View(panoramas);
        }

        // GET: Panorama360/View/5
        public async Task<IActionResult> View(int id)
        {
            var panorama = await _panorama360Repository.GetByIdAsync(id);
            if (panorama == null)
            {
                return NotFound();
            }

            return View(panorama);
        }

        // GET: Panorama360/TourView/5
        public async Task<IActionResult> TourView(int id)
        {
            var tour = await _tourRepository.GetByIdWithDetailsAsync(id);
            if (tour == null)
            {
                return NotFound();
            }

            var panoramas = await _panorama360Repository.GetByTourIdAsync(id);
            if (!panoramas.Any())
            {
                return RedirectToAction("Details", "Tour", new { id });
            }

            ViewBag.Tour = tour;
            return View(panoramas);
        }

        // GET: Panorama360/LocationView/5
        public async Task<IActionResult> LocationView(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            var panoramas = await _panorama360Repository.GetByLocationIdAsync(id);
            if (!panoramas.Any())
            {
                return RedirectToAction("Details", "Location", new { id });
            }

            ViewBag.Location = location;
            return View(panoramas);
        }
    }
}
