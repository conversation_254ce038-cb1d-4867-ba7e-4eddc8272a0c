﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class TourController : Controller
    {
        private readonly ITourRepository _tourRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public TourController(
            ITourRepository tourRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            ApplicationDbContext context,
            IWebHostEnvironment hostEnvironment)
        {
            _tourRepository = tourRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Tour
        public async Task<IActionResult> Index()
        {
            var tours = await _tourRepository.GetAllAsync();
            return View(tours);
        }

        // GET: Admin/Tour/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var tour = await _tourRepository.GetByIdWithDetailsAsync(id);
            if (tour == null)
            {
                return NotFound();
            }

            return View(tour);
        }

        // GET: Admin/Tour/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View();
        }

        // POST: Admin/Tour/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Tour tour, IFormFile imageFile, int[] selectedAmenities)
        {
            // Bỏ qua xác thực ModelState cho trường Price
            if (tour.Price > 0)
            {
                ModelState.Remove("Price");
            }

            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    tour.ImageUrl = await SaveImage(imageFile);
                }

                // Thêm tour vào cơ sở dữ liệu
                await _tourRepository.AddAsync(tour);

                // Thêm tiện ích cho tour
                if (selectedAmenities != null && selectedAmenities.Length > 0)
                {
                    foreach (var amenityId in selectedAmenities)
                    {
                        _context.TourAmenities.Add(new TourAmenity
                        {
                            TourId = tour.Id,
                            AmenityId = amenityId
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", tour.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", tour.CityId);
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View(tour);
        }

        // GET: Admin/Tour/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var tour = await _context.Tours
                .Include(t => t.TourAmenities)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (tour == null)
            {
                return NotFound();
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", tour.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", tour.CityId);

            // Lấy danh sách tất cả tiện ích và đánh dấu những tiện ích đã được chọn
            var allAmenities = await _context.Amenities.ToListAsync();
            var selectedAmenityIds = tour.TourAmenities.Select(ta => ta.AmenityId).ToList();

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = selectedAmenityIds.Contains(a.Id)
            }).ToList();

            return View(tour);
        }

        // POST: Admin/Tour/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Tour tour, IFormFile imageFile, int[] selectedAmenities)
        {
            if (id != tour.Id)
            {
                return NotFound();
            }

            // Bỏ qua xác thực ModelState cho trường Price
            if (tour.Price > 0)
            {
                ModelState.Remove("Price");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Lấy tour hiện tại từ cơ sở dữ liệu
                    var existingTour = await _context.Tours
                        .Include(t => t.TourAmenities)
                        .FirstOrDefaultAsync(t => t.Id == id);

                    if (existingTour == null)
                    {
                        return NotFound();
                    }

                    // Cập nhật thông tin tour
                    existingTour.Name = tour.Name;
                    existingTour.Description = tour.Description;
                    existingTour.Itinerary = tour.Itinerary;
                    existingTour.Address = tour.Address;
                    existingTour.Duration = tour.Duration;
                    existingTour.Price = tour.Price;
                    existingTour.MaxGroupSize = tour.MaxGroupSize;
                    existingTour.IsFeatured = tour.IsFeatured;
                    existingTour.LocationId = tour.LocationId;
                    existingTour.CityId = tour.CityId;

                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        existingTour.ImageUrl = await SaveImage(imageFile);
                    }

                    // Cập nhật tiện ích
                    // Xóa tất cả các tiện ích hiện tại
                    _context.TourAmenities.RemoveRange(existingTour.TourAmenities);

                    // Thêm các tiện ích mới
                    if (selectedAmenities != null && selectedAmenities.Length > 0)
                    {
                        foreach (var amenityId in selectedAmenities)
                        {
                            _context.TourAmenities.Add(new TourAmenity
                            {
                                TourId = tour.Id,
                                AmenityId = amenityId
                            });
                        }
                    }

                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await _tourRepository.ExistsAsync(tour.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", tour.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", tour.CityId);
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View(tour);
        }

        // GET: Admin/Tour/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var tour = await _tourRepository.GetByIdAsync(id);
            if (tour == null)
            {
                return NotFound();
            }

            return View(tour);
        }

        // POST: Admin/Tour/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _tourRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }

        // Phương thức lưu hình ảnh
        private async Task<string> SaveImage(IFormFile imageFile)
        {
            string wwwRootPath = _hostEnvironment.WebRootPath;
            string fileName = Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName);
            string tourPath = Path.Combine(wwwRootPath, "images", "tours");

            if (!Directory.Exists(tourPath))
            {
                Directory.CreateDirectory(tourPath);
            }

            using (var fileStream = new FileStream(Path.Combine(tourPath, fileName), FileMode.Create))
            {
                await imageFile.CopyToAsync(fileStream);
            }

            return "/images/tours/" + fileName;
        }
    }
}
