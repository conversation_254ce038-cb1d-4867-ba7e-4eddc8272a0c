﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models.ViewModels
{
    public class TourReviewViewModel
    {
        public int TourId { get; set; }
        
        public Tour Tour { get; set; }
        
        [Required(ErrorMessage = "Vui lòng chọn đánh giá")]
        [Range(1, 5, ErrorMessage = "Đánh giá phải từ 1 đến 5 sao")]
        public int Rating { get; set; }
        
        [Required(ErrorMessage = "Vui lòng nhập nhận xét")]
        [StringLength(500, ErrorMessage = "Nhận xét không được vượt quá 500 ký tự")]
        public string Comment { get; set; }
        
        public IEnumerable<Review> Reviews { get; set; }
    }
}
