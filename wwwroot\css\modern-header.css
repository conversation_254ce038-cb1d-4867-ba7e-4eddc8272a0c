/* Travel Header Styles - Auto hide/show with transparency */
.travel-header {
  background-color: rgba(245, 245, 240, 0.1);
  padding: 1rem 2rem;
  position: fixed;
  top: 0;
  left: 80px;
  right: 0;
  z-index: 1000;
  box-shadow: none;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.travel-header.hidden {
  transform: translateY(-100%);
}

.travel-header.visible {
  background-color: rgba(245, 245, 240, 0.95);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.full-page-layout .travel-header {
  background-color: rgba(245, 245, 240, 0.05);
}

.full-page-layout .travel-header.visible {
  background-color: rgba(245, 245, 240, 0.9);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.logo-container {
  font-weight: 600;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.logo-container a {
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.logo-container a:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.logo-text {
  font-weight: 700;
  color: #4caf50;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.travel-header.visible .logo-text {
  color: #333;
}

.main-nav {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.main-nav a {
  color: #555;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 0.8rem;
  position: relative;
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.main-nav a i {
  margin-right: 5px;
  font-size: 1rem;
}

.main-nav a:hover {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.main-nav a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: #4caf50;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.main-nav a:hover::after {
  width: 50%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: transparent;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.action-btn:hover {
  background-color: #f0f0f0;
  color: #000;
}

.action-btn.dropdown-toggle::after {
  display: none;
}

.search-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: transparent;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.search-btn:hover {
  background-color: #f0f0f0;
  color: #000;
}

.user-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: transparent;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.user-btn:hover {
  background-color: #f0f0f0;
  color: #000;
}

.user-btn.dropdown-toggle::after {
  display: none;
}

/* Responsive styles - No header needed */
@media (max-width: 992px) {
  .full-page-layout #destination-showcase-container {
    height: 100vh;
    margin-top: 0;
  }
}

@media (max-width: 576px) {
  .showcase-content {
    padding-top: 1rem !important;
  }

  .continent-name {
    font-size: 2.5rem !important;
  }
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #555;
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
}

/* No header - no padding needed */
body {
  padding-top: 0;
}

.full-page-layout {
  padding-top: 0;
}

/* Content wrapper adjustments */
.content-wrapper {
  padding-top: 0;
  margin-top: 0;
}

/* Wrapper adjustments for sidebar */
.wrapper {
  margin-left: 80px; /* Match sidebar width */
  width: calc(100% - 80px);
}

.wrapper.full-width {
  width: calc(100% - 80px);
}

/* Dropdown menu styling */
.dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  padding: 0.5rem 0;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background-color: #f8f8f8;
}

.dropdown-divider {
  margin: 0.25rem 0;
}

/* Badge styling */
.badge {
  font-weight: 500;
  padding: 0.25em 0.5em;
  font-size: 0.75em;
}
