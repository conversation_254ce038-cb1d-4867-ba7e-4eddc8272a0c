﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class ServiceBookingDetail
    {
        public int Id { get; set; }
        
        public int ServiceBookingId { get; set; }
        
        public int ServiceId { get; set; }
        
        public int Quantity { get; set; } = 1;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        public DateTime ServiceDate { get; set; }
        
        public ServiceBookingDetailStatus Status { get; set; } = ServiceBookingDetailStatus.Pending;
        
        [ForeignKey("ServiceBookingId")]
        public ServiceBooking ServiceBooking { get; set; }
        
        [ForeignKey("ServiceId")]
        public Service Service { get; set; }
        
        [NotMapped]
        public decimal TotalPrice => Price * Quantity;
    }
    
    public enum ServiceBookingDetailStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
