﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class BookingItem
    {
        public int RoomId { get; set; }

        public string RoomName { get; set; }

        public string AccommodationName { get; set; }

        public int AccommodationId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PricePerNight { get; set; }

        public int NumberOfRooms { get; set; } = 1;

        public int NumberOfGuests { get; set; } = 1;

        public DateTime CheckInDate { get; set; }

        public DateTime CheckOutDate { get; set; }

        public string? ImageUrl { get; set; }

        [NotMapped]
        public int TotalNights => (CheckOutDate - CheckInDate).Days;

        [NotMapped]
        public decimal TotalPrice => PricePerNight * NumberOfRooms * TotalNights;
    }
}
