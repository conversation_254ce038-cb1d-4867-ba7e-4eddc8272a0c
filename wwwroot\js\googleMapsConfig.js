// <PERSON><PERSON><PERSON> Google Maps API
const GOOGLE_MAPS_API_KEY = "AIzaSyAMqvsRr2toGUxy7yF_HBNuNCifHE3S1UU";

// Hàm khởi tạo Google Street View
function initStreetView(elementId, lat, lng, heading = 165, pitch = 0) {
  const panorama = new google.maps.StreetViewPanorama(
    document.getElementById(elementId),
    {
      position: { lat: lat, lng: lng },
      pov: { heading: heading, pitch: pitch },
      zoom: 1,
      addressControl: true,
      showRoadLabels: true,
      fullscreenControl: true,
    }
  );
  return panorama;
}

// Hàm khởi tạo Google Maps
function initMap(elementId, lat, lng, zoom = 15) {
  const map = new google.maps.Map(document.getElementById(elementId), {
    center: { lat: lat, lng: lng },
    zoom: zoom,
    mapTypeId: google.maps.MapTypeId.ROADMAP,
  });
  return map;
}

// Hàm thêm marker vào bản đồ
function addMarker(map, lat, lng, title, content = null) {
  const marker = new google.maps.Marker({
    position: { lat: lat, lng: lng },
    map: map,
    title: title,
  });

  if (content) {
    const infoWindow = new google.maps.InfoWindow({
      content: content,
    });

    marker.addListener("click", function () {
      infoWindow.open(map, marker);
    });
  }

  return marker;
}

// Hàm kiểm tra xem Google Street View có sẵn tại vị trí cụ thể không
function checkStreetViewAvailability(lat, lng, callback) {
  const streetViewService = new google.maps.StreetViewService();
  streetViewService.getPanorama(
    {
      location: { lat: lat, lng: lng },
      radius: 50,
    },
    (data, status) => {
      callback(status === google.maps.StreetViewStatus.OK);
    }
  );
}
