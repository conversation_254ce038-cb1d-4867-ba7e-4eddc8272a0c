﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class ReviewController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ReviewController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/Review
        public async Task<IActionResult> Index(string searchString, string sortOrder, int? page)
        {
            ViewData["CurrentSort"] = sortOrder ?? "";
            ViewData["DateSortParam"] = string.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            ViewData["RatingSortParam"] = sortOrder == "rating" ? "rating_desc" : "rating";
            ViewData["CurrentFilter"] = searchString ?? "";

            var reviews = _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                reviews = reviews.Where(r =>
                    r.User.FullName.Contains(searchString) ||
                    r.Comment.Contains(searchString) ||
                    (r.Accommodation != null && r.Accommodation.Name.Contains(searchString)) ||
                    (r.Tour != null && r.Tour.Name.Contains(searchString)) ||
                    (r.Service != null && r.Service.Name.Contains(searchString)));
            }

            switch (sortOrder)
            {
                case "date_desc":
                    reviews = reviews.OrderByDescending(r => r.CreatedAt);
                    break;
                case "rating":
                    reviews = reviews.OrderBy(r => r.Rating);
                    break;
                case "rating_desc":
                    reviews = reviews.OrderByDescending(r => r.Rating);
                    break;
                default:
                    reviews = reviews.OrderBy(r => r.CreatedAt);
                    break;
            }

            int pageSize = 10;
            int pageNumber = page ?? 1;

            return View(await reviews.ToListAsync());
        }

        // GET: Admin/Review/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (review == null)
            {
                return NotFound();
            }

            return View(review);
        }

        // GET: Admin/Review/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (review == null)
            {
                return NotFound();
            }

            return View(review);
        }

        // POST: Admin/Review/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Rating,Comment")] Review review)
        {
            if (id != review.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var existingReview = await _context.Reviews.FindAsync(id);
                    if (existingReview == null)
                    {
                        return NotFound();
                    }

                    existingReview.Rating = review.Rating;
                    existingReview.Comment = review.Comment;

                    _context.Update(existingReview);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "Đánh giá đã được cập nhật thành công.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ReviewExists(review.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(review);
        }

        // GET: Admin/Review/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (review == null)
            {
                return NotFound();
            }

            return View(review);
        }

        // POST: Admin/Review/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var review = await _context.Reviews.FindAsync(id);
            if (review == null)
            {
                return NotFound();
            }

            _context.Reviews.Remove(review);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Đánh giá đã được xóa thành công.";
            return RedirectToAction(nameof(Index));
        }

        private bool ReviewExists(int id)
        {
            return _context.Reviews.Any(e => e.Id == id);
        }
    }
}
