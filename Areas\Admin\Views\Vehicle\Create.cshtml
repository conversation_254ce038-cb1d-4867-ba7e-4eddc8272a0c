@model ViVu.Models.Vehicle

@{
    ViewData["Title"] = "Thêm phương tiện mới";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý phương tiện</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i>
            Thông tin phương tiện
        </div>
        <div class="card-body">
            <form asp-action="Create" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên phương tiện</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="LocationId" class="control-label">Địa điểm</label>
                            <select asp-for="LocationId" class="form-select" asp-items="ViewBag.Locations" required>
                                <option value="">-- Chọn địa điểm --</option>
                            </select>
                            <span asp-validation-for="LocationId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CityId" class="control-label">Thành phố</label>
                            <select asp-for="CityId" class="form-select" asp-items="ViewBag.Cities" required>
                                <option value="">-- Chọn thành phố --</option>
                            </select>
                            <span asp-validation-for="CityId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Type" class="control-label">Loại phương tiện</label>
                            <select asp-for="Type" class="form-select" asp-items="ViewBag.VehicleTypes" required>
                                <option value="">-- Chọn loại phương tiện --</option>
                            </select>
                            <span asp-validation-for="Type" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="LicensePlate" class="control-label">Biển số</label>
                            <input asp-for="LicensePlate" class="form-control" required />
                            <span asp-validation-for="LicensePlate" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="PricePerDay" class="control-label">Giá thuê/ngày (VNĐ)</label>
                            <input asp-for="PricePerDay" class="form-control" type="number" min="0.01" step="0.01" required />
                            <span asp-validation-for="PricePerDay" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Capacity" class="control-label">Sức chứa (người)</label>
                            <input asp-for="Capacity" class="form-control" type="number" min="1" max="50" required />
                            <span asp-validation-for="Capacity" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="ImageUrl" class="control-label">Hình ảnh</label>
                            <input type="file" name="imageFile" class="form-control" />
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="IsFeatured" class="form-check-input" />
                            <label asp-for="IsFeatured" class="form-check-label">Nổi bật</label>
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="IsAvailable" class="form-check-input" checked />
                            <label asp-for="IsAvailable" class="form-check-label">Có sẵn</label>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="3" required></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Details" class="control-label">Chi tiết</label>
                    <textarea asp-for="Details" class="form-control" rows="5" required></textarea>
                    <span asp-validation-for="Details" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Lưu
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/disable-price-validation.js"></script>
}
