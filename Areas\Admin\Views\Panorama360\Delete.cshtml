@model ViVu.Models.Panorama360

@{
    ViewData["Title"] = "Xóa Du lịch 360°";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa Du lịch 360°</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý Du lịch 360°</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Xác nhận xóa
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h4>Bạn có chắc chắn muốn xóa panorama 360° này?</h4>
                <p>Hành động này không thể hoàn tác.</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Tên:</dt>
                        <dd class="col-sm-8">@Model.Name</dd>
                        
                        <dt class="col-sm-4">Loại:</dt>
                        <dd class="col-sm-8">@Model.Type</dd>
                        
                        <dt class="col-sm-4">Liên kết với:</dt>
                        <dd class="col-sm-8">
                            @if (Model.Type == "Tour" && Model.Tour != null)
                            {
                                <span>Tour: @Model.Tour.Name</span>
                            }
                            else if (Model.Type == "Location" && Model.Location != null)
                            {
                                <span>Địa điểm: @Model.Location.Name</span>
                            }
                        </dd>
                        
                        <dt class="col-sm-4">Trạng thái:</dt>
                        <dd class="col-sm-8">
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">Hoạt động</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">Không hoạt động</span>
                            }
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid img-thumbnail" style="max-height: 300px;" />
                </div>
            </div>
            
            <form asp-action="Delete" class="mt-3">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
