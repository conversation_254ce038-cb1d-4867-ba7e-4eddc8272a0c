@model IEnumerable<ViVu.Models.TourBooking>

@{
    ViewData["Title"] = "Đơn đặt tour của tôi";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Bạn chưa có đơn đặt tour nào.
        </div>
        
        <div class="text-center mt-4">
            <a asp-controller="Tour" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Tìm tour ngay
            </a>
        </div>
    }
    else
    {
        <div class="card shadow-sm border-0">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead class="table-light">
                            <tr>
                                <th><PERSON><PERSON> đơn</th>
                                <th><PERSON><PERSON>y đặt</th>
                                <th>Ngày tour</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var booking in Model.OrderByDescending(b => b.BookingDate))
                            {
                                <tr>
                                    <td>#@booking.Id</td>
                                    <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>@booking.TourDate.ToString("dd/MM/yyyy")</td>
                                    <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                    <td>
                                        <span class="badge @(booking.Status == TourBookingStatus.Pending ? "bg-warning" : 
                                                           booking.Status == TourBookingStatus.Confirmed ? "bg-primary" :
                                                           booking.Status == TourBookingStatus.Completed ? "bg-success" : "bg-danger")">
                                            @(booking.Status == TourBookingStatus.Pending ? "Chờ xác nhận" : 
                                             booking.Status == TourBookingStatus.Confirmed ? "Đã xác nhận" :
                                             booking.Status == TourBookingStatus.Completed ? "Hoàn thành" : "Đã hủy")
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-controller="Tour" asp-action="BookingDetails" asp-route-id="@booking.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Chi tiết
                                        </a>
                                        
                                        @if (booking.Status == TourBookingStatus.Pending || booking.Status == TourBookingStatus.Confirmed)
                                        {
                                            <form asp-controller="Tour" asp-action="CancelBooking" asp-route-id="@booking.Id" method="post" class="d-inline-block" onsubmit="return confirm('Bạn có chắc chắn muốn hủy đặt tour này?');">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-times-circle"></i> Hủy
                                                </button>
                                            </form>
                                        }
                                        
                                        @if (booking.Status == TourBookingStatus.Completed)
                                        {
                                            <a asp-controller="Tour" asp-action="Review" asp-route-id="@booking.TourBookingDetails.First().TourId" class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-star"></i> Đánh giá
                                            </a>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
