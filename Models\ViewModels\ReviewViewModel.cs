﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models.ViewModels
{
    public class ReviewViewModel
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "<PERSON>ui lòng chọn đánh giá")]
        [Range(1, 5, ErrorMessage = "Đánh giá phải từ 1 đến 5 sao")]
        [Display(Name = "Đánh giá")]
        public int Rating { get; set; }
        
        [Required(ErrorMessage = "Vui lòng nhập nhận xét")]
        [StringLength(500, ErrorMessage = "Nhận xét không được vượt quá 500 ký tự")]
        [Display(Name = "Nhận xét")]
        public string Comment { get; set; }
        
        public ReviewType Type { get; set; }
        
        public int ItemId { get; set; }
        
        public string ItemName { get; set; }
    }
}
