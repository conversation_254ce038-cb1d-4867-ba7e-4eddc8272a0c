@model IEnumerable<ViVu.Models.Tour>

@{
    ViewData["Title"] = "Kết quả tìm kiếm hoạt động";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Activity" asp-action="Index">Hoạt động</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Kết quả tìm kiếm</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title mb-3"><PERSON><PERSON> lọc tìm kiếm</h4>
                    <form asp-controller="Activity" asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Tất cả địa điểm --</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Ngày tham gia</label>
                                <input type="date" name="activityDate" class="form-control" value="@(ViewBag.ActivityDate?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(1).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i> Lọc kết quả
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Kết quả tìm kiếm hoạt động</h2>
            <p>Tìm thấy @Model.Count() hoạt động phù hợp với tiêu chí của bạn</p>
        </div>
    </div>

    <div class="row">
        @if (Model.Any())
        {
            foreach (var activity in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(activity.ImageUrl) ? "/images/default/default-activity.jpg" : activity.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@activity.Name">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-info">Hoạt động</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@activity.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @activity.Location?.Name, @activity.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @activity.Duration giờ
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @activity.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(activity.Description?.Length > 100 ? activity.Description.Substring(0, 100) + "..." : activity.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@activity.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@activity.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-5">
                <div class="alert alert-info">
                    <h4 class="alert-heading">Không tìm thấy hoạt động nào!</h4>
                    <p>Không có hoạt động nào phù hợp với tiêu chí tìm kiếm của bạn. Vui lòng thử lại với các tiêu chí khác.</p>
                </div>
                <a asp-controller="Activity" asp-action="Index" class="btn btn-primary mt-3">Xem tất cả hoạt động</a>
            </div>
        }
    </div>
</div>
