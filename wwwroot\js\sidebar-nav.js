// Sidebar Navigation Functionality
document.addEventListener("DOMContentLoaded", function () {
  // Get all sidebar navigation items
  const sidebarItems = document.querySelectorAll(".sidebar-nav-item");

  // Get all sections that correspond to sidebar items
  const sections = [];
  sidebarItems.forEach((item) => {
    const targetId = item.getAttribute("href");
    if (targetId && targetId.startsWith("#")) {
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        sections.push({
          id: targetId.substring(1),
          element: targetSection,
          navItem: item,
        });
      }
    }
  });

  // Sort sections by their position on the page
  sections.sort((a, b) => {
    return a.element.offsetTop - b.element.offsetTop;
  });

  // Function to update active sidebar item based on scroll position
  function updateActiveItem() {
    const scrollPosition = window.scrollY + window.innerHeight / 3;

    // Find the current section
    let currentSection = null;

    // Find the last section that starts before the current scroll position
    for (let i = sections.length - 1; i >= 0; i--) {
      if (sections[i].element.offsetTop <= scrollPosition) {
        currentSection = sections[i];
        break;
      }
    }

    // If no section is found (we're above the first section), use the first one
    if (!currentSection && sections.length > 0) {
      currentSection = sections[0];
    }

    // Update active class
    if (currentSection) {
      sidebarItems.forEach((item) => {
        item.classList.remove("active");
      });
      currentSection.navItem.classList.add("active");
    }
  }

  // Add scroll event listener with throttling for better performance
  let ticking = false;
  window.addEventListener("scroll", function () {
    if (!ticking) {
      window.requestAnimationFrame(function () {
        updateActiveItem();
        ticking = false;
      });
      ticking = true;
    }
  });

  // Initialize active item on page load
  updateActiveItem();

  // Add click event listeners to sidebar items for smooth scrolling
  sidebarItems.forEach((item) => {
    item.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        // Update active class immediately for better UX
        sidebarItems.forEach((navItem) => {
          navItem.classList.remove("active");
        });
        this.classList.add("active");

        // Smooth scroll to target
        window.scrollTo({
          top: targetElement.offsetTop - 80, // Offset for header
          behavior: "smooth",
        });
      }
    });
  });

  // Add hover effect to sidebar items
  sidebarItems.forEach((item) => {
    item.addEventListener("mouseenter", function () {
      if (!this.classList.contains("active")) {
        this.style.transform = "translateY(-5px)";
      }
    });

    item.addEventListener("mouseleave", function () {
      if (!this.classList.contains("active")) {
        this.style.transform = "";
      }
    });
  });
});
