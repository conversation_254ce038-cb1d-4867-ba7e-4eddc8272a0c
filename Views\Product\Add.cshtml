﻿@model ViVu.Models.Product
@using Microsoft.AspNetCore.Mvc.Rendering

<h2 class="text-center my-4">Add Product</h2>
<div class="container">
    <form asp-action="Add" method="post" enctype="multipart/form-data" class="border p-4 rounded shadow bg-light">
        <div class="mb-3">
            <label asp-for="Name" class="form-label fw-bold"></label>
            <input asp-for="Name" class="form-control" />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Price" class="form-label fw-bold"></label>
            <input asp-for="Price" type="number" step="0.01" class="form-control" />
            <span asp-validation-for="Price" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Description" class="form-label fw-bold"></label>
            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="CategoryId" class="form-label fw-bold">Category</label>
            <select asp-for="CategoryId" asp-items="ViewBag.Categories" class="form-select">
                <option value="">-- Select Category --</option>
            </select>
        </div>

        <div class="mb-3">
            <label asp-for="ImageUrl" class="form-label fw-bold">Product Image</label>
            <input type="file" name="ImageUrl" class="form-control" />
        </div>

        <div class="text-center">
            <button type="submit" class="btn btn-primary px-4">Add</button>
            <a asp-action="Index" class="btn btn-secondary px-4 ms-2">Cancel</a>
        </div>
    </form>
</div>
