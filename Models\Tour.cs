﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class Tour
    {
        public int Id { get; set; }

        [Required, StringLength(100)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string Itinerary { get; set; }

        [Required]
        public string Address { get; set; }

        public string? ImageUrl { get; set; }

        [Required]
        [Range(1, 30)]
        public int Duration { get; set; } // Duration in days

        [Required]
        [Range(0.01, double.MaxValue)]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        [Required]
        [Range(1, 100)]
        public int MaxGroupSize { get; set; }

        public bool IsFeatured { get; set; } = false;

        public int LocationId { get; set; }

        public int CityId { get; set; }

        [ForeignKey("LocationId")]
        [InverseProperty("Tours")]
        public Location? Location { get; set; }

        [ForeignKey("CityId")]
        public City? City { get; set; }

        public List<TourImage>? Images { get; set; }

        public List<Review>? Reviews { get; set; }

        public List<TourAmenity>? TourAmenities { get; set; }

        public List<TourBookingDetail>? TourBookingDetails { get; set; }

        public List<Panorama360>? Panoramas { get; set; }

        [NotMapped]
        public double AverageRating => Reviews?.Any() == true ? Reviews.Average(r => r.Rating) : 0;

        [NotMapped]
        public bool HasPanorama => Panoramas?.Any() == true;
    }
}
