using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ViVu.Data;
using ViVu.Models;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class BookingController : Controller
    {
        private readonly ApplicationDbContext _context;

        public BookingController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/Booking
        public async Task<IActionResult> Index(string status = null)
        {
            IQueryable<Booking> bookingsQuery = _context.Bookings
                .Include(b => b.ApplicationUser)
                .OrderByDescending(b => b.BookingDate);

            // Lọc theo trạng thái nếu có
            if (!string.IsNullOrEmpty(status) && Enum.TryParse<BookingStatus>(status, out var bookingStatus))
            {
                bookingsQuery = bookingsQuery.Where(b => b.Status == bookingStatus);
            }

            var bookings = await bookingsQuery.ToListAsync();

            // Truyền danh sách trạng thái để hiển thị bộ lọc
            ViewBag.Statuses = Enum.GetValues(typeof(BookingStatus))
                .Cast<BookingStatus>()
                .Select(s => new
                {
                    Value = s.ToString(),
                    Text = GetStatusDisplayName(s)
                })
                .ToList();

            ViewBag.SelectedStatus = status;

            return View(bookings);
        }

        // GET: Admin/Booking/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var booking = await _context.Bookings
                .Include(b => b.ApplicationUser)
                .Include(b => b.BookingDetails)
                    .ThenInclude(bd => bd.Room)
                        .ThenInclude(r => r.Accommodation)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/Booking/UpdateStatus
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, BookingStatus status)
        {
            var booking = await _context.Bookings.FindAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            booking.Status = status;

            // Nếu trạng thái là Hủy, cập nhật trạng thái phòng
            if (status == BookingStatus.Cancelled)
            {
                // Lấy danh sách chi tiết đặt phòng
                var bookingDetails = await _context.BookingDetails
                    .Where(bd => bd.BookingId == id)
                    .ToListAsync();

                // Cập nhật trạng thái phòng
                foreach (var detail in bookingDetails)
                {
                    var room = await _context.Rooms.FindAsync(detail.RoomId);
                    if (room != null)
                    {
                        room.IsAvailable = true;
                    }
                }
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: Admin/Booking/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var booking = await _context.Bookings
                .Include(b => b.ApplicationUser)
                .Include(b => b.BookingDetails)
                    .ThenInclude(bd => bd.Room)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/Booking/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var booking = await _context.Bookings
                .Include(b => b.BookingDetails)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            // Kiểm tra xem đơn đặt phòng có thể xóa không
            if (booking.Status != BookingStatus.Cancelled && booking.Status != BookingStatus.Completed)
            {
                ModelState.AddModelError("", "Chỉ có thể xóa đơn đặt phòng đã hủy hoặc đã hoàn thành.");

                // Lấy lại thông tin đầy đủ để hiển thị
                booking = await _context.Bookings
                    .Include(b => b.ApplicationUser)
                    .Include(b => b.BookingDetails)
                        .ThenInclude(bd => bd.Room)
                    .FirstOrDefaultAsync(b => b.Id == id);

                return View(booking);
            }

            // Xóa các chi tiết đặt phòng
            _context.BookingDetails.RemoveRange(booking.BookingDetails);

            // Xóa đơn đặt phòng
            _context.Bookings.Remove(booking);
            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        // Hàm hỗ trợ để hiển thị tên trạng thái
        private string GetStatusDisplayName(BookingStatus status)
        {
            return status switch
            {
                BookingStatus.Pending => "Chờ xác nhận",
                BookingStatus.Confirmed => "Đã xác nhận",
                BookingStatus.Completed => "Hoàn thành",
                BookingStatus.Cancelled => "Đã hủy",
                _ => status.ToString()
            };
        }
    }
}
