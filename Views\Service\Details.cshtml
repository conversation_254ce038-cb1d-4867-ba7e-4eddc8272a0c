@model ViVu.Models.Service
@{
    ViewData["Title"] = Model.Name;
}

<div class="container my-5">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-2">@Model.Name</h1>
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <span class="text-warning">
                            @Model.AverageRating.ToString("0.0")
                            <i class="bi bi-star-fill"></i>
                        </span>
                        <span class="text-muted">(@Model.Reviews.Count đ<PERSON> gi<PERSON>)</span>
                    }
                    else
                    {
                        <span class="text-muted">Chưa có đánh giá</span>
                    }
                </div>
                <span class="text-muted">
                    <i class="bi bi-geo-alt"></i> @Model.Location?.Name, @Model.City?.Name
                </span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="#booking" class="btn btn-primary">
                <i class="bi bi-calendar-check"></i> Đặt dịch vụ ngay
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-4" style="width: 100%; max-height: 400px; object-fit: cover;">
            }

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Mô tả dịch vụ</h5>
                </div>
                <div class="card-body">
                    @Html.Raw(Model.Description.Replace("\n", "<br>"))
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Chi tiết dịch vụ</h5>
                </div>
                <div class="card-body">
                    @Html.Raw(Model.Details.Replace("\n", "<br>"))
                </div>
            </div>

            <div class="card mb-4" id="reviews">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Đánh giá từ khách hàng</h5>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-controller="Review" asp-action="Create" asp-route-type="Service" asp-route-id="@Model.Id" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-star"></i> Viết đánh giá
                        </a>
                    }
                </div>
                <div class="card-body">
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <h2 class="display-4 text-warning">@Model.AverageRating.ToString("0.0")</h2>
                                <div class="mb-2">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        if (i <= Math.Round(Model.AverageRating))
                                        {
                                            <i class="bi bi-star-fill text-warning"></i>
                                        }
                                        else
                                        {
                                            <i class="bi bi-star text-warning"></i>
                                        }
                                    }
                                </div>
                                <p class="text-muted">@Model.Reviews.Count đánh giá</p>
                            </div>
                            <div class="col-md-8">
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">5 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 10px;">
                                            @{
                                                var fiveStarPercent = Model.Reviews.Count > 0 ? (double)Model.Reviews.Count(r => r.Rating == 5) / Model.Reviews.Count * 100 : 0;
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @fiveStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2">@Model.Reviews.Count(r => r.Rating == 5)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">4 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 10px;">
                                            @{
                                                var fourStarPercent = Model.Reviews.Count > 0 ? (double)Model.Reviews.Count(r => r.Rating == 4) / Model.Reviews.Count * 100 : 0;
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @fourStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2">@Model.Reviews.Count(r => r.Rating == 4)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">3 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 10px;">
                                            @{
                                                var threeStarPercent = Model.Reviews.Count > 0 ? (double)Model.Reviews.Count(r => r.Rating == 3) / Model.Reviews.Count * 100 : 0;
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @threeStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2">@Model.Reviews.Count(r => r.Rating == 3)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">2 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 10px;">
                                            @{
                                                var twoStarPercent = Model.Reviews.Count > 0 ? (double)Model.Reviews.Count(r => r.Rating == 2) / Model.Reviews.Count * 100 : 0;
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @twoStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2">@Model.Reviews.Count(r => r.Rating == 2)</div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="col-3">1 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 10px;">
                                            @{
                                                var oneStarPercent = Model.Reviews.Count > 0 ? (double)Model.Reviews.Count(r => r.Rating == 1) / Model.Reviews.Count * 100 : 0;
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @oneStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2">@Model.Reviews.Count(r => r.Rating == 1)</div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="review-list">
                            @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt))
                            {
                                <div class="d-flex mb-4">
                                    <div class="flex-shrink-0">
                                        <div class="bg-light rounded-circle p-3">
                                            <i class="bi bi-person-circle fs-3"></i>
                                        </div>
                                    </div>
                                    <div class="ms-3 flex-grow-1">
                                        <div class="d-flex justify-content-between mb-1">
                                            <h6 class="mb-0">@review.User.FullName</h6>
                                            <small class="text-muted">@review.CreatedAt.ToString("dd/MM/yyyy")</small>
                                        </div>
                                        <div class="mb-2">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                if (i <= review.Rating)
                                                {
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                }
                                                else
                                                {
                                                    <i class="bi bi-star text-warning"></i>
                                                }
                                            }
                                        </div>
                                        <p class="mb-0">@review.Comment</p>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-chat-square-text fs-1 text-muted"></i>
                            <p class="mt-2">Chưa có đánh giá nào cho dịch vụ này.</p>
                            @if (User.Identity.IsAuthenticated)
                            {
                                <a asp-controller="Review" asp-action="Create" asp-route-type="Service" asp-route-id="@Model.Id" class="btn btn-primary">
                                    <i class="bi bi-star"></i> Viết đánh giá đầu tiên
                                </a>
                            }
                            else
                            {
                                <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right"></i> Đăng nhập để đánh giá
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card sticky-top" style="top: 20px;" id="booking">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Đặt dịch vụ</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h4 class="text-primary mb-0">@Model.Price.ToString("#,##0") VNĐ</h4>
                        <small class="text-muted">/ @Model.Duration phút</small>
                    </div>

                    <form asp-action="Book" asp-controller="Service" method="get">
                        <input type="hidden" name="id" value="@Model.Id" />

                        <div class="mb-3">
                            <label class="form-label">Ngày sử dụng</label>
                            <input type="date" name="serviceDate" class="form-control" value="@(((DateTime)ViewBag.ServiceDate).ToString("yyyy-MM-dd"))" min="@DateTime.Today.ToString("yyyy-MM-dd")" required />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Số lượng</label>
                            <input type="number" name="quantity" class="form-control" value="1" min="1" max="10" required />
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-calendar-check"></i> Đặt ngay
                        </button>
                    </form>

                    <hr>

                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-clock me-2 text-primary"></i>
                        <span>Thời gian: @Model.Duration phút</span>
                    </div>

                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-geo-alt me-2 text-primary"></i>
                        <span>Địa điểm: @Model.Location?.Name, @Model.City?.Name</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }

        .rate {
            display: flex;
            flex-direction: row-reverse;
        }

        .rate:not(:checked) > input {
            position: absolute;
            top: -9999px;
        }

        .rate:not(:checked) > label {
            float: right;
            width: 1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 30px;
            color: #ccc;
        }

        .rate:not(:checked) > label:before {
            content: '★ ';
        }

        .rate > input:checked ~ label {
            color: #ffc700;
        }

        .rate:not(:checked) > label:hover,
        .rate:not(:checked) > label:hover ~ label {
            color: #deb217;
        }

        .rate > input:checked + label:hover,
        .rate > input:checked + label:hover ~ label,
        .rate > input:checked ~ label:hover,
        .rate > input:checked ~ label:hover ~ label,
        .rate > label:hover ~ input:checked ~ label {
            color: #c59b08;
        }
    </style>
}
