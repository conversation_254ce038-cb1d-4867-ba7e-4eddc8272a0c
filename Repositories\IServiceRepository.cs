﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IServiceRepository
    {
        Task<IEnumerable<Service>> GetAllAsync();
        Task<Service> GetByIdAsync(int id);
        Task<Service> GetByIdWithDetailsAsync(int id);
        Task<IEnumerable<Service>> GetByLocationIdAsync(int locationId);
        Task<IEnumerable<Service>> GetByCityIdAsync(int cityId);
        Task<IEnumerable<Service>> GetFeaturedServicesAsync(int count = 6);
        Task<IEnumerable<Service>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null,
            DateTime? serviceDate = null, decimal? minPrice = null, decimal? maxPrice = null);
        Task AddAsync(Service service);
        Task UpdateAsync(Service service);
        Task DeleteAsync(int id);
    }
}
