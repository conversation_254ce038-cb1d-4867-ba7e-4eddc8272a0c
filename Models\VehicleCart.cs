﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class VehicleCart
    {
        public List<VehicleCartItem> Items { get; set; } = new List<VehicleCartItem>();
        
        public void AddItem(VehicleCartItem item)
        {
            var existingItem = Items.FirstOrDefault(i => i.VehicleId == item.VehicleId &&
                                                        i.StartDate == item.StartDate &&
                                                        i.EndDate == item.EndDate);
            if (existingItem != null)
            {
                // If the same vehicle is already in the cart for the same dates, just update
                existingItem.Quantity = item.Quantity;
            }
            else
            {
                Items.Add(item);
            }
        }
        
        public void RemoveItem(int vehicleId)
        {
            Items.RemoveAll(i => i.VehicleId == vehicleId);
        }
        
        public void Clear()
        {
            Items.Clear();
        }
        
        [NotMapped]
        public decimal TotalPrice => Items.Sum(item => item.TotalPrice);
        
        [NotMapped]
        public int TotalItems => Items.Count;
    }
}
