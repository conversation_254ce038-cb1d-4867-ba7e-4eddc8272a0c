﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFVehicleBookingRepository : IVehicleBookingRepository
    {
        private readonly ApplicationDbContext _context;

        public EFVehicleBookingRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<VehicleBooking>> GetAllAsync()
        {
            return await _context.VehicleBookings
                .Include(vb => vb.ApplicationUser)
                .Include(vb => vb.VehicleBookingDetails)
                    .ThenInclude(vbd => vbd.Vehicle)
                .OrderByDescending(vb => vb.BookingDate)
                .ToListAsync();
        }

        public async Task<VehicleBooking> GetByIdAsync(int id)
        {
            return await _context.VehicleBookings
                .Include(vb => vb.ApplicationUser)
                .FirstOrDefaultAsync(vb => vb.Id == id);
        }

        public async Task<VehicleBooking> GetByIdWithDetailsAsync(int id)
        {
            return await _context.VehicleBookings
                .Include(vb => vb.ApplicationUser)
                .Include(vb => vb.VehicleBookingDetails)
                    .ThenInclude(vbd => vbd.Vehicle)
                        .ThenInclude(v => v.Location)
                .Include(vb => vb.VehicleBookingDetails)
                    .ThenInclude(vbd => vbd.Vehicle)
                        .ThenInclude(v => v.City)
                .FirstOrDefaultAsync(vb => vb.Id == id);
        }

        public async Task<IEnumerable<VehicleBooking>> GetByUserIdAsync(string userId)
        {
            return await _context.VehicleBookings
                .Include(vb => vb.VehicleBookingDetails)
                    .ThenInclude(vbd => vbd.Vehicle)
                .Where(vb => vb.UserId == userId)
                .OrderByDescending(vb => vb.BookingDate)
                .ToListAsync();
        }

        public async Task AddAsync(VehicleBooking vehicleBooking)
        {
            await _context.VehicleBookings.AddAsync(vehicleBooking);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(VehicleBooking vehicleBooking)
        {
            _context.VehicleBookings.Update(vehicleBooking);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var vehicleBooking = await _context.VehicleBookings.FindAsync(id);
            if (vehicleBooking != null)
            {
                _context.VehicleBookings.Remove(vehicleBooking);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.VehicleBookings.AnyAsync(vb => vb.Id == id);
        }

        public async Task<IEnumerable<VehicleBooking>> GetRecentBookingsAsync(int count = 5)
        {
            return await _context.VehicleBookings
                .Include(vb => vb.ApplicationUser)
                .OrderByDescending(vb => vb.BookingDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<int> GetTotalBookingsAsync()
        {
            return await _context.VehicleBookings.CountAsync();
        }
    }
}
