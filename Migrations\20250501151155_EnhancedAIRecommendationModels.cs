﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ViVu.Migrations
{
    /// <inheritdoc />
    public partial class EnhancedAIRecommendationModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AccessibilityNeeds",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DietaryRestrictions",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasDietaryRestrictions",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasVisitedBenTreBefore",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasVisitedMekongDeltaBefore",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInCooking",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInCrafts",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInFarming",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInFishing",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInNightlife",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInPhotography",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInShopping",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInWellness",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "PreferredPace",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PreferredSeason",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersBiking",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersFineDining",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersHomestays",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersHotels",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersLocalCuisine",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersPublicTransport",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersRentalVehicle",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersResorts",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersStreetFood",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersVillas",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersWalking",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "RequiresAccessibility",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingAlone",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForAdventure",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForCulture",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForEducation",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForRelaxation",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForRomance",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingWithChildren",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingWithElders",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingWithFamily",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingWithFriends",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingWithPartner",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "AccessibilityNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AfternoonActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BestTimeToVisit",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConfidenceScore",
                table: "TravelRecommendations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "CulinaryExperienceNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CulturalExperienceNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DailyItinerary",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DiningRecommendations",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EveningActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FamilyFriendlinessNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "FeedbackDate",
                table: "TravelRecommendations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LocalTips",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MorningActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PersonalizationFactors",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RelatedVehicleIds",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ShoppingRecommendations",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Tags",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransportationRecommendations",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserFeedback",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UserRating",
                table: "TravelRecommendations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Version",
                table: "TravelRecommendations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "WeatherConsiderations",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccessibilityNeeds",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "DietaryRestrictions",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "HasDietaryRestrictions",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "HasVisitedBenTreBefore",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "HasVisitedMekongDeltaBefore",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInCooking",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInCrafts",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInFarming",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInFishing",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInNightlife",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInPhotography",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInShopping",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInWellness",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PreferredPace",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PreferredSeason",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersBiking",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersFineDining",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersHomestays",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersHotels",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersLocalCuisine",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersPublicTransport",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersRentalVehicle",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersResorts",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersStreetFood",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersVillas",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersWalking",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "RequiresAccessibility",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingAlone",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForAdventure",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForCulture",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForEducation",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForRelaxation",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForRomance",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingWithChildren",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingWithElders",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingWithFamily",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingWithFriends",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingWithPartner",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "AccessibilityNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "AfternoonActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "BestTimeToVisit",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ConfidenceScore",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "CulinaryExperienceNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "CulturalExperienceNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "DailyItinerary",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "DiningRecommendations",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "EveningActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "FamilyFriendlinessNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "FeedbackDate",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "LocalTips",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "MorningActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "PersonalizationFactors",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "RelatedVehicleIds",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ShoppingRecommendations",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "Tags",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "TransportationRecommendations",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "UserFeedback",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "UserRating",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "WeatherConsiderations",
                table: "TravelRecommendations");
        }
    }
}
