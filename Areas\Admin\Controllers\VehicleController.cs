﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class VehicleController : Controller
    {
        private readonly IVehicleRepository _vehicleRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public VehicleController(
            IVehicleRepository vehicleRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            ApplicationDbContext context,
            IWebHostEnvironment hostEnvironment)
        {
            _vehicleRepository = vehicleRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Vehicle
        public async Task<IActionResult> Index()
        {
            var vehicles = await _vehicleRepository.GetAllAsync();
            return View(vehicles);
        }

        // GET: Admin/Vehicle/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var vehicle = await _vehicleRepository.GetByIdWithDetailsAsync(id);
            if (vehicle == null)
            {
                return NotFound();
            }

            return View(vehicle);
        }

        // GET: Admin/Vehicle/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            ViewBag.VehicleTypes = new SelectList(Enum.GetValues(typeof(VehicleType)));

            return View();
        }

        // POST: Admin/Vehicle/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Vehicle vehicle, IFormFile imageFile)
        {
            // Bỏ qua xác thực ModelState cho trường PricePerDay
            if (vehicle.PricePerDay > 0)
            {
                ModelState.Remove("PricePerDay");
            }

            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    vehicle.ImageUrl = await SaveImage(imageFile);
                }

                // Thêm phương tiện vào cơ sở dữ liệu
                await _vehicleRepository.AddAsync(vehicle);

                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", vehicle.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", vehicle.CityId);
            ViewBag.VehicleTypes = new SelectList(Enum.GetValues(typeof(VehicleType)), vehicle.Type);

            return View(vehicle);
        }

        // GET: Admin/Vehicle/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var vehicle = await _vehicleRepository.GetByIdAsync(id);
            if (vehicle == null)
            {
                return NotFound();
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", vehicle.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", vehicle.CityId);
            ViewBag.VehicleTypes = new SelectList(Enum.GetValues(typeof(VehicleType)), vehicle.Type);

            return View(vehicle);
        }

        // POST: Admin/Vehicle/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Vehicle vehicle, IFormFile imageFile)
        {
            if (id != vehicle.Id)
            {
                return NotFound();
            }

            // Bỏ qua xác thực ModelState cho trường PricePerDay
            if (vehicle.PricePerDay > 0)
            {
                ModelState.Remove("PricePerDay");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Lấy phương tiện hiện tại từ cơ sở dữ liệu
                    var existingVehicle = await _vehicleRepository.GetByIdAsync(id);
                    if (existingVehicle == null)
                    {
                        return NotFound();
                    }

                    // Cập nhật thông tin phương tiện
                    existingVehicle.Name = vehicle.Name;
                    existingVehicle.Description = vehicle.Description;
                    existingVehicle.Details = vehicle.Details;
                    existingVehicle.PricePerDay = vehicle.PricePerDay;
                    existingVehicle.Type = vehicle.Type;
                    existingVehicle.LicensePlate = vehicle.LicensePlate;
                    existingVehicle.Capacity = vehicle.Capacity;
                    existingVehicle.IsFeatured = vehicle.IsFeatured;
                    existingVehicle.IsAvailable = vehicle.IsAvailable;
                    existingVehicle.LocationId = vehicle.LocationId;
                    existingVehicle.CityId = vehicle.CityId;

                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        existingVehicle.ImageUrl = await SaveImage(imageFile);
                    }

                    // Cập nhật phương tiện
                    await _vehicleRepository.UpdateAsync(existingVehicle);

                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await VehicleExists(vehicle.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", vehicle.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", vehicle.CityId);
            ViewBag.VehicleTypes = new SelectList(Enum.GetValues(typeof(VehicleType)), vehicle.Type);

            return View(vehicle);
        }

        // GET: Admin/Vehicle/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var vehicle = await _vehicleRepository.GetByIdAsync(id);
            if (vehicle == null)
            {
                return NotFound();
            }

            return View(vehicle);
        }

        // POST: Admin/Vehicle/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _vehicleRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }

        // Phương thức lưu hình ảnh
        private async Task<string> SaveImage(IFormFile imageFile)
        {
            string wwwRootPath = _hostEnvironment.WebRootPath;
            string fileName = Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName);
            string vehiclePath = Path.Combine(wwwRootPath, "images", "vehicles");

            if (!Directory.Exists(vehiclePath))
            {
                Directory.CreateDirectory(vehiclePath);
            }

            using (var fileStream = new FileStream(Path.Combine(vehiclePath, fileName), FileMode.Create))
            {
                await imageFile.CopyToAsync(fileStream);
            }

            return "/images/vehicles/" + fileName;
        }

        // Kiểm tra phương tiện tồn tại
        private async Task<bool> VehicleExists(int id)
        {
            return await _vehicleRepository.ExistsAsync(id);
        }
    }
}
