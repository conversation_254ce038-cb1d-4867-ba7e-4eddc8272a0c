@model ViVu.Models.ViewModels.ServiceBookingViewModel
@{
    ViewData["Title"] = "Đặt dịch vụ";
}

<div class="container my-5">
    <h1 class="mb-4">Đặt dịch vụ</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Thông tin đặt dịch vụ</h5>
                </div>
                <div class="card-body">
                    <form asp-action="Book" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="ServiceId" />
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ServiceDate" class="form-label"><PERSON><PERSON>y sử dụng</label>
                                    <input asp-for="ServiceDate" class="form-control" type="date" min="@DateTime.Today.ToString("yyyy-MM-dd")" />
                                    <span asp-validation-for="ServiceDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Quantity" class="form-label">Số lượng</label>
                                    <input asp-for="Quantity" class="form-control" type="number" min="1" max="10" />
                                    <span asp-validation-for="Quantity" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="SpecialRequests" class="form-label">Yêu cầu đặc biệt</label>
                            <textarea asp-for="SpecialRequests" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="SpecialRequests" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-calendar-check"></i> Xác nhận đặt dịch vụ
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.ServiceId" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin dịch vụ</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex mb-3">
                        @if (!string.IsNullOrEmpty(Model.Service.ImageUrl))
                        {
                            <img src="@Model.Service.ImageUrl" alt="@Model.Service.Name" class="img-fluid rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                        }
                        <div>
                            <h5 class="mb-1">@Model.Service.Name</h5>
                            <p class="text-muted mb-0">
                                <i class="bi bi-geo-alt"></i> @Model.Service.Location?.Name, @Model.Service.City?.Name
                            </p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Đơn giá:</span>
                            <span>@Model.Service.Price.ToString("#,##0") VNĐ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Số lượng:</span>
                            <span id="quantity-display">@Model.Quantity</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Thời gian:</span>
                            <span>@Model.Service.Duration phút</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <h5>Tổng tiền:</h5>
                        <h5 class="text-primary" id="total-price">@Model.TotalPrice.ToString("#,##0") VNĐ</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Cập nhật tổng tiền khi thay đổi số lượng
        document.querySelector('#Quantity').addEventListener('change', function() {
            const quantity = parseInt(this.value);
            const price = @Model.Service.Price;
            const totalPrice = quantity * price;
            
            document.querySelector('#quantity-display').textContent = quantity;
            document.querySelector('#total-price').textContent = totalPrice.toLocaleString('vi-VN') + ' VNĐ';
        });
    </script>
}
