﻿using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Models
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions <ApplicationDbContext> options) : base(options)
        {
        }
        // Các model cũ (sẽ được thay thế sau khi migration)
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<ProductImage> ProductImages { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderDetail> OrderDetails { get; set; }

        // Các model mới cho trang web du lịch
        public DbSet<Accommodation> Accommodations { get; set; }
        public DbSet<AccommodationImage> AccommodationImages { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<Amenity> Amenities { get; set; }
        public DbSet<AccommodationAmenity> AccommodationAmenities { get; set; }
        public DbSet<RoomAmenity> RoomAmenities { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<City> Cities { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<Booking> Bookings { get; set; }
        public DbSet<BookingDetail> BookingDetails { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<Promotion> Promotions { get; set; }
        public DbSet<AccommodationPromotion> AccommodationPromotions { get; set; }

        // Tour models
        public DbSet<Tour> Tours { get; set; }
        public DbSet<TourImage> TourImages { get; set; }
        public DbSet<TourAmenity> TourAmenities { get; set; }
        public DbSet<TourBooking> TourBookings { get; set; }
        public DbSet<TourBookingDetail> TourBookingDetails { get; set; }

        // Service models
        public DbSet<Service> Services { get; set; }
        public DbSet<ServiceImage> ServiceImages { get; set; }
        public DbSet<ServiceBooking> ServiceBookings { get; set; }
        public DbSet<ServiceBookingDetail> ServiceBookingDetails { get; set; }

        // Vehicle models
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<VehicleImage> VehicleImages { get; set; }
        public DbSet<VehicleBooking> VehicleBookings { get; set; }
        public DbSet<VehicleBookingDetail> VehicleBookingDetails { get; set; }

        // Panorama 360 models
        public DbSet<Panorama360> Panorama360s { get; set; }

        // AI Recommendation models
        public DbSet<UserPreference> UserPreferences { get; set; }
        public DbSet<SearchHistory> SearchHistories { get; set; }
        public DbSet<TravelRecommendation> TravelRecommendations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Cấu hình mối quan hệ giữa Accommodation và Location
            modelBuilder.Entity<Accommodation>()
                .HasOne(a => a.Location)
                .WithMany(l => l.Accommodations)
                .HasForeignKey(a => a.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa Accommodation và City
            modelBuilder.Entity<Accommodation>()
                .HasOne(a => a.City)
                .WithMany(c => c.Accommodations)
                .HasForeignKey(a => a.CityId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa Location và City
            modelBuilder.Entity<Location>()
                .HasOne(l => l.City)
                .WithMany(c => c.Locations)
                .HasForeignKey(l => l.CityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa City và Country
            modelBuilder.Entity<City>()
                .HasOne(c => c.Country)
                .WithMany(co => co.Cities)
                .HasForeignKey(c => c.CountryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa Room và Accommodation
            modelBuilder.Entity<Room>()
                .HasOne(r => r.Accommodation)
                .WithMany(a => a.Rooms)
                .HasForeignKey(r => r.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa BookingDetail và Booking
            modelBuilder.Entity<BookingDetail>()
                .HasOne(bd => bd.Booking)
                .WithMany(b => b.BookingDetails)
                .HasForeignKey(bd => bd.BookingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa BookingDetail và Room
            modelBuilder.Entity<BookingDetail>()
                .HasOne(bd => bd.Room)
                .WithMany(r => r.BookingDetails)
                .HasForeignKey(bd => bd.RoomId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa Review và Accommodation
            modelBuilder.Entity<Review>()
                .HasOne(r => r.Accommodation)
                .WithMany(a => a.Reviews)
                .HasForeignKey(r => r.AccommodationId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Cấu hình mối quan hệ giữa Review và Tour
            modelBuilder.Entity<Review>()
                .HasOne(r => r.Tour)
                .WithMany(t => t.Reviews)
                .HasForeignKey(r => r.TourId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Cấu hình mối quan hệ giữa AccommodationImage và Accommodation
            modelBuilder.Entity<AccommodationImage>()
                .HasOne(ai => ai.Accommodation)
                .WithMany(a => a.Images)
                .HasForeignKey(ai => ai.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ nhiều-nhiều giữa Accommodation và Amenity
            modelBuilder.Entity<AccommodationAmenity>()
                .HasKey(aa => new { aa.AccommodationId, aa.AmenityId });

            modelBuilder.Entity<AccommodationAmenity>()
                .HasOne(aa => aa.Accommodation)
                .WithMany(a => a.AccommodationAmenities)
                .HasForeignKey(aa => aa.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<AccommodationAmenity>()
                .HasOne(aa => aa.Amenity)
                .WithMany(a => a.AccommodationAmenities)
                .HasForeignKey(aa => aa.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ nhiều-nhiều giữa Room và Amenity
            modelBuilder.Entity<RoomAmenity>()
                .HasKey(ra => new { ra.RoomId, ra.AmenityId });

            modelBuilder.Entity<RoomAmenity>()
                .HasOne(ra => ra.Room)
                .WithMany(r => r.RoomAmenities)
                .HasForeignKey(ra => ra.RoomId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RoomAmenity>()
                .HasOne(ra => ra.Amenity)
                .WithMany(a => a.RoomAmenities)
                .HasForeignKey(ra => ra.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ nhiều-nhiều giữa Accommodation và Promotion
            modelBuilder.Entity<AccommodationPromotion>()
                .HasKey(ap => new { ap.AccommodationId, ap.PromotionId });

            modelBuilder.Entity<AccommodationPromotion>()
                .HasOne(ap => ap.Accommodation)
                .WithMany(a => a.AccommodationPromotions)
                .HasForeignKey(ap => ap.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ cho Tour
            modelBuilder.Entity<Tour>()
                .HasOne(t => t.Location)
                .WithMany(l => l.Tours)
                .HasForeignKey(t => t.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Tour>()
                .HasOne(t => t.City)
                .WithMany()
                .HasForeignKey(t => t.CityId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa TourImage và Tour
            modelBuilder.Entity<TourImage>()
                .HasOne(ti => ti.Tour)
                .WithMany(t => t.Images)
                .HasForeignKey(ti => ti.TourId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa TourBookingDetail và TourBooking
            modelBuilder.Entity<TourBookingDetail>()
                .HasOne(tbd => tbd.TourBooking)
                .WithMany(tb => tb.TourBookingDetails)
                .HasForeignKey(tbd => tbd.TourBookingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa TourBookingDetail và Tour
            modelBuilder.Entity<TourBookingDetail>()
                .HasOne(tbd => tbd.Tour)
                .WithMany(t => t.TourBookingDetails)
                .HasForeignKey(tbd => tbd.TourId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ nhiều-nhiều giữa Tour và Amenity
            modelBuilder.Entity<TourAmenity>()
                .HasKey(ta => new { ta.TourId, ta.AmenityId });

            modelBuilder.Entity<TourAmenity>()
                .HasOne(ta => ta.Tour)
                .WithMany(t => t.TourAmenities)
                .HasForeignKey(ta => ta.TourId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<TourAmenity>()
                .HasOne(ta => ta.Amenity)
                .WithMany()
                .HasForeignKey(ta => ta.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<AccommodationPromotion>()
                .HasOne(ap => ap.Promotion)
                .WithMany(p => p.AccommodationPromotions)
                .HasForeignKey(ap => ap.PromotionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ cho Service
            modelBuilder.Entity<Service>()
                .HasOne(s => s.Location)
                .WithMany()
                .HasForeignKey(s => s.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Service>()
                .HasOne(s => s.City)
                .WithMany()
                .HasForeignKey(s => s.CityId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa ServiceImage và Service
            modelBuilder.Entity<ServiceImage>()
                .HasOne(si => si.Service)
                .WithMany(s => s.Images)
                .HasForeignKey(si => si.ServiceId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa ServiceBookingDetail và ServiceBooking
            modelBuilder.Entity<ServiceBookingDetail>()
                .HasOne(sbd => sbd.ServiceBooking)
                .WithMany(sb => sb.ServiceBookingDetails)
                .HasForeignKey(sbd => sbd.ServiceBookingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa ServiceBookingDetail và Service
            modelBuilder.Entity<ServiceBookingDetail>()
                .HasOne(sbd => sbd.Service)
                .WithMany(s => s.ServiceBookingDetails)
                .HasForeignKey(sbd => sbd.ServiceId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa Review và Service
            modelBuilder.Entity<Review>()
                .HasOne(r => r.Service)
                .WithMany(s => s.Reviews)
                .HasForeignKey(r => r.ServiceId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Cấu hình mối quan hệ cho Vehicle
            modelBuilder.Entity<Vehicle>()
                .HasOne(v => v.Location)
                .WithMany()
                .HasForeignKey(v => v.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Vehicle>()
                .HasOne(v => v.City)
                .WithMany()
                .HasForeignKey(v => v.CityId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa VehicleImage và Vehicle
            modelBuilder.Entity<VehicleImage>()
                .HasOne(vi => vi.Vehicle)
                .WithMany(v => v.Images)
                .HasForeignKey(vi => vi.VehicleId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa VehicleBookingDetail và VehicleBooking
            modelBuilder.Entity<VehicleBookingDetail>()
                .HasOne(vbd => vbd.VehicleBooking)
                .WithMany(vb => vb.VehicleBookingDetails)
                .HasForeignKey(vbd => vbd.VehicleBookingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình mối quan hệ giữa VehicleBookingDetail và Vehicle
            modelBuilder.Entity<VehicleBookingDetail>()
                .HasOne(vbd => vbd.Vehicle)
                .WithMany(v => v.VehicleBookingDetails)
                .HasForeignKey(vbd => vbd.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình mối quan hệ giữa Review và Vehicle
            modelBuilder.Entity<Review>()
                .HasOne(r => r.Vehicle)
                .WithMany(v => v.Reviews)
                .HasForeignKey(r => r.VehicleId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Cấu hình mối quan hệ giữa Panorama360 và Tour
            modelBuilder.Entity<Panorama360>()
                .HasOne(p => p.Tour)
                .WithMany(t => t.Panoramas)
                .HasForeignKey(p => p.TourId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Cấu hình mối quan hệ giữa Panorama360 và Location
            modelBuilder.Entity<Panorama360>()
                .HasOne(p => p.Location)
                .WithMany(l => l.Panoramas)
                .HasForeignKey(p => p.LocationId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
