@model ViVu.Models.Vehicle

@{
    ViewData["Title"] = "Chi tiết phương tiện";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý phương tiện</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Thông tin phương tiện
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" />
                    }
                    else
                    {
                        <div class="alert alert-info">Không có hình ảnh</div>
                    }
                </div>
                <div class="col-md-8">
                    <h3>@Model.Name</h3>
                    <div class="mb-3">
                        <span class="badge bg-primary">@Model.Type.ToString()</span>
                        @if (Model.IsFeatured)
                        {
                            <span class="badge bg-success ms-1">Nổi bật</span>
                        }
                        @if (Model.IsAvailable)
                        {
                            <span class="badge bg-success ms-1">Có sẵn</span>
                        }
                        else
                        {
                            <span class="badge bg-danger ms-1">Không có sẵn</span>
                        }
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Biển số:</strong> @Model.LicensePlate</p>
                            <p><strong>Sức chứa:</strong> @Model.Capacity người</p>
                            <p><strong>Giá thuê:</strong> @Model.PricePerDay.ToString("#,##0") VNĐ/ngày</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Địa điểm:</strong> @Model.Location?.Name</p>
                            <p><strong>Thành phố:</strong> @Model.City?.Name</p>
                            <p><strong>ID:</strong> @Model.Id</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Mô tả</h5>
                        <p>@Model.Description</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Chi tiết</h5>
                        <p>@Model.Details</p>
                    </div>
                </div>
            </div>
            
            @if (Model.Reviews != null && Model.Reviews.Any())
            {
                <hr />
                <h5>Đánh giá (@Model.Reviews.Count)</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Người dùng</th>
                                <th>Đánh giá</th>
                                <th>Nhận xét</th>
                                <th>Ngày đánh giá</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt))
                            {
                                <tr>
                                    <td>@review.User.FullName</td>
                                    <td>
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= review.Rating)
                                            {
                                                <i class="fas fa-star text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star text-warning"></i>
                                            }
                                        }
                                    </td>
                                    <td>@review.Comment</td>
                                    <td>@review.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            
            <div class="mt-3">
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> Chỉnh sửa
                </a>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại
                </a>
            </div>
        </div>
    </div>
</div>
