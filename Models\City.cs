﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models
{
    public class City
    {
        public int Id { get; set; }
        
        [Required, StringLength(100)]
        public string Name { get; set; }
        
        public string? Description { get; set; }
        
        public string? ImageUrl { get; set; }
        
        public bool IsFeatured { get; set; } = false;
        
        public int CountryId { get; set; }
        
        public Country Country { get; set; }
        
        public List<Location>? Locations { get; set; }
        
        public List<Accommodation>? Accommodations { get; set; }
    }
}
