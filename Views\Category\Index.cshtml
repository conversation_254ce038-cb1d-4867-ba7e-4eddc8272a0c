﻿@model IEnumerable<ViVu.Models.Category>

<div class="container my-5">
    <h2 class="text-center text-primary fw-bold">📂 Category List</h2>

    <div class="text-end mb-3">
        <a asp-action="Add" class="btn btn-success">+ Add New Category</a>
    </div>

    <table class="table table-striped table-hover text-center">
        <thead class="table-dark">
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var category in Model)
            {
                <tr>
                    <td class="fw-bold">@category.Id</td>
                    <td>@category.Name</td>
                    <td>
                        <a asp-action="Update" asp-route-id="@category.Id" class="btn btn-warning btn-sm">✏ Edit</a>
                        <a asp-action="Delete" asp-route-id="@category.Id" class="btn btn-danger btn-sm">🗑 Delete</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>
