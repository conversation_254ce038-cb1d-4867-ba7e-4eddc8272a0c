﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface ITourBookingRepository
    {
        Task<IEnumerable<TourBooking>> GetAllAsync();
        Task<TourBooking> GetByIdAsync(int id);
        Task<IEnumerable<TourBooking>> GetByUserIdAsync(string userId);
        Task AddAsync(TourBooking tourBooking);
        Task UpdateAsync(TourBooking tourBooking);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
}
