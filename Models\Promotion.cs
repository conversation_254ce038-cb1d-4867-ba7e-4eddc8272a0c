﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class Promotion
    {
        public int Id { get; set; }
        
        [Required, StringLength(100)]
        public string Name { get; set; }
        
        public string? Description { get; set; }
        
        [Required]
        [Range(0, 100)]
        public int DiscountPercentage { get; set; }
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        public string? PromoCode { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public List<AccommodationPromotion>? AccommodationPromotions { get; set; }
    }
}
