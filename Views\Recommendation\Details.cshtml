@model ViVu.Models.RecommendationViewModel
@using Markdig

@{
    ViewData["Title"] = "Chi Tiết Gợi Ý Lịch Trình";

    // Hàm chuyển đổi Markdown sang HTML
    string MarkdownToHtml(string markdown)
    {
        if (string.IsNullOrEmpty(markdown))
            return "";

        var pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions()
            .Build();

        return Markdown.ToHtml(markdown, pipeline);
    }

    var recommendation = Model.Recommendations.FirstOrDefault();
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Recommendation" asp-action="Index">AI Gợi Ý Lịch Trình</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Chi tiết lịch trình</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (recommendation != null)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="bg-primary text-white p-4 rounded-3">
                    <h1 class="display-5 fw-bold">@recommendation.Title</h1>
                    <p class="fs-5">@recommendation.Description</p>
                    <div class="d-flex align-items-center mt-3">
                        <span class="badge bg-light text-primary me-2">@recommendation.RecommendationType</span>
                        @if (recommendation.IncludesNature)
                        {
                            <span class="badge bg-success me-2">Thiên nhiên</span>
                        }
                        @if (recommendation.IncludesHistory)
                        {
                            <span class="badge bg-info me-2">Lịch sử</span>
                        }
                        @if (recommendation.IncludesFood)
                        {
                            <span class="badge bg-warning me-2">Ẩm thực</span>
                        }
                        @if (recommendation.IncludesAdventure)
                        {
                            <span class="badge bg-danger me-2">Mạo hiểm</span>
                        }
                        @if (recommendation.IncludesRelaxation)
                        {
                            <span class="badge bg-secondary me-2">Thư giãn</span>
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h3 class="mb-0">Chi tiết lịch trình</h3>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">Ngân sách ước tính</h5>
                                        <p class="card-text">@recommendation.EstimatedMinBudget?.ToString("N0") - @recommendation.EstimatedMaxBudget?.ToString("N0") VNĐ</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">Thời gian đề xuất</h5>
                                        <p class="card-text">@recommendation.RecommendedDuration ngày</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4>Chi tiết lịch trình</h4>
                            <div class="itinerary-content">
                                @Html.Raw(MarkdownToHtml(recommendation.ItineraryDetails))
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(recommendation.HighlightsSection))
                        {
                            <div class="mb-4">
                                <h4>Điểm nổi bật</h4>
                                <div class="highlights-content">
                                    @Html.Raw(MarkdownToHtml(recommendation.HighlightsSection))
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(recommendation.DayByDaySection))
                        {
                            <div class="mb-4">
                                <h4>Lịch trình theo ngày</h4>
                                <div class="day-by-day-content">
                                    @Html.Raw(MarkdownToHtml(recommendation.DayByDaySection))
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(recommendation.TipsSection))
                        {
                            <div class="mb-4">
                                <h4>Mẹo du lịch</h4>
                                <div class="tips-content">
                                    @Html.Raw(MarkdownToHtml(recommendation.TipsSection))
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(recommendation.BudgetBreakdownSection))
                        {
                            <div class="mb-4">
                                <h4>Chi phí dự kiến</h4>
                                <div class="budget-content">
                                    @Html.Raw(MarkdownToHtml(recommendation.BudgetBreakdownSection))
                                </div>
                            </div>
                        }
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(recommendation.RelatedTourIds) && Model.RelatedTours != null && Model.RelatedTours.Any())
                {
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h3 class="mb-0">Tour đề xuất</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach (var tourId in recommendation.RelatedTourIds.Split(','))
                                {
                                    if (int.TryParse(tourId, out int id) && Model.RelatedTours.ContainsKey(id))
                                    {
                                        var tour = Model.RelatedTours[id];
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100">
                                                <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/no-image.jpg" : tour.ImageUrl)"
                                                     class="card-img-top" alt="@tour.Name" style="height: 150px; object-fit: cover;">
                                                <div class="card-body">
                                                    <h5 class="card-title">@tour.Name</h5>
                                                    <p class="card-text text-truncate">@tour.Description</p>
                                                    <p class="card-text">
                                                        <small class="text-muted">
                                                            <i class="fas fa-map-marker-alt"></i> @tour.Location?.Name
                                                        </small>
                                                    </p>
                                                    <p class="card-text">
                                                        <span class="text-primary fw-bold">@tour.Price.ToString("N0") VNĐ</span>
                                                    </p>
                                                    <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id"
                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && Model.RelatedAccommodations != null && Model.RelatedAccommodations.Any())
                {
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h3 class="mb-0">Khách sạn đề xuất</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach (var accommodationId in recommendation.RelatedAccommodationIds.Split(','))
                                {
                                    if (int.TryParse(accommodationId, out int id) && Model.RelatedAccommodations.ContainsKey(id))
                                    {
                                        var accommodation = Model.RelatedAccommodations[id];
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100">
                                                <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/no-image.jpg" : accommodation.ImageUrl)"
                                                     class="card-img-top" alt="@accommodation.Name" style="height: 150px; object-fit: cover;">
                                                <div class="card-body">
                                                    <h5 class="card-title">@accommodation.Name</h5>
                                                    <p class="card-text text-truncate">@accommodation.Description</p>
                                                    <p class="card-text">
                                                        <small class="text-muted">
                                                            <i class="fas fa-map-marker-alt"></i> @accommodation.Location?.Name
                                                        </small>
                                                    </p>
                                                    <div class="mb-2">
                                                        @for (int i = 0; i < accommodation.StarRating; i++)
                                                        {
                                                            <i class="fas fa-star text-warning"></i>
                                                        }
                                                    </div>
                                                    <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id"
                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds) && Model.RelatedVehicles != null && Model.RelatedVehicles.Any())
                {
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h3 class="mb-0">Phương tiện đề xuất</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach (var vehicleId in recommendation.RelatedVehicleIds.Split(','))
                                {
                                    if (int.TryParse(vehicleId, out int id) && Model.RelatedVehicles.ContainsKey(id))
                                    {
                                        var vehicle = Model.RelatedVehicles[id];
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100">
                                                <img src="@(string.IsNullOrEmpty(vehicle.ImageUrl) ? "/images/no-image.jpg" : vehicle.ImageUrl)"
                                                     class="card-img-top" alt="@vehicle.Name" style="height: 150px; object-fit: cover;">
                                                <div class="card-body">
                                                    <h5 class="card-title">@vehicle.Name</h5>
                                                    <p class="card-text text-truncate">@vehicle.Description</p>
                                                    <p class="card-text">
                                                        <small class="text-muted">
                                                            <i class="fas fa-map-marker-alt"></i> @vehicle.Location?.Name
                                                        </small>
                                                    </p>
                                                    <p class="card-text">
                                                        <span class="text-primary fw-bold">@vehicle.RentalPrice.ToString("N0") VNĐ</span>
                                                    </p>
                                                    <a asp-controller="Vehicle" asp-action="Details" asp-route-id="@vehicle.Id"
                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="col-md-4">
                <div class="card shadow-sm mb-4 sticky-top" style="top: 20px;">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Thông tin lịch trình</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Loại lịch trình:</strong> @recommendation.RecommendationType</p>
                        <p><strong>Thời gian:</strong> @recommendation.RecommendedDuration ngày</p>
                        <p><strong>Ngân sách:</strong> @recommendation.EstimatedMinBudget?.ToString("N0") - @recommendation.EstimatedMaxBudget?.ToString("N0") VNĐ</p>

                        <p><strong>Phù hợp với:</strong></p>
                        <ul class="list-unstyled">
                            @if (recommendation.SuitableForChildren)
                            {
                                <li><i class="fas fa-check text-success me-2"></i> Gia đình có trẻ em</li>
                            }
                            @if (recommendation.SuitableForElders)
                            {
                                <li><i class="fas fa-check text-success me-2"></i> Người lớn tuổi</li>
                            }
                            @if (recommendation.HasAccessibility)
                            {
                                <li><i class="fas fa-check text-success me-2"></i> Người khuyết tật</li>
                            }
                            @if (recommendation.RequiresPhysicalEffort)
                            {
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> Yêu cầu thể lực</li>
                            }
                        </ul>

                        <div class="d-grid gap-2 mt-4">
                            @if (User.Identity.IsAuthenticated)
                            {
                                @if (!recommendation.IsSaved)
                                {
                                    <form asp-action="SaveRecommendation" asp-route-id="@recommendation.Id" method="post">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-bookmark me-2"></i> Lưu lịch trình này
                                        </button>
                                    </form>
                                }
                                else
                                {
                                    <button class="btn btn-success w-100" disabled>
                                        <i class="fas fa-check me-2"></i> Đã lưu lịch trình
                                    </button>
                                }
                            }
                            else
                            {
                                <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i> Đăng nhập để lưu lịch trình
                                </a>
                            }

                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Quay lại
                            </a>
                        </div>
                    </div>
                </div>

                @if (Model.SimilarRecommendations != null && Model.SimilarRecommendations.Any())
                {
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Lịch trình tương tự</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                @foreach (var similarRecommendation in Model.SimilarRecommendations)
                                {
                                    <a asp-action="Details" asp-route-id="@similarRecommendation.Id" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">@similarRecommendation.Title</h6>
                                            <small>@similarRecommendation.RecommendedDuration ngày</small>
                                        </div>
                                        <p class="mb-1 small text-truncate">@similarRecommendation.Description</p>
                                        <small class="text-muted">@similarRecommendation.RecommendationType</small>
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h4 class="alert-heading">Không tìm thấy lịch trình!</h4>
                    <p>Rất tiếc, chúng tôi không thể tìm thấy lịch trình bạn yêu cầu. Vui lòng thử lại.</p>
                    <hr>
                    <a asp-action="Index" class="btn btn-primary">Quay lại trang gợi ý</a>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Smooth scroll to sections
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this).attr('href');
                $('html, body').animate({
                    scrollTop: $(target).offset().top - 20
                }, 500);
            });
        });
    </script>
}
