@model IEnumerable<ViVu.Models.Room>
@{
    ViewData["Title"] = "Quản lý phòng";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Phòng</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-bed me-1"></i> Danh sách phòng</div>
                <a asp-action="Create" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> Thêm mới
                </a>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>Tên phòng</th>
                        <th>Chỗ ở</th>
                        <th>Giá/đêm</th>
                        <th>Sức chứa</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <img src="@item.ImageUrl" alt="@item.Name" style="width: 80px; height: 60px; object-fit: cover;" />
                            </td>
                            <td>@item.Name</td>
                            <td>@item.Accommodation?.Name</td>
                            <td>@item.PricePerNight.ToString("N0") VNĐ</td>
                            <td>@item.MaxOccupancy người</td>
                            <td>
                                @if (item.IsAvailable)
                                {
                                    <span class="badge bg-success">Có sẵn</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không có sẵn</span>
                                }
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-warning btn-sm me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
