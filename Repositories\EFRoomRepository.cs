﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFRoomRepository : IRoomRepository
    {
        private readonly ApplicationDbContext _context;
        
        public EFRoomRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IEnumerable<Room>> GetAllAsync()
        {
            return await _context.Rooms
                .Include(r => r.Accommodation)
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .ToListAsync();
        }
        
        public async Task<Room> GetByIdAsync(int id)
        {
            return await _context.Rooms
                .Include(r => r.Accommodation)
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .FirstOrDefaultAsync(r => r.Id == id);
        }
        
        public async Task<IEnumerable<Room>> GetByAccommodationIdAsync(int accommodationId)
        {
            return await _context.Rooms
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .Where(r => r.AccommodationId == accommodationId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Room>> GetAvailableRoomsAsync(int accommodationId, DateTime checkIn, DateTime checkOut)
        {
            return await _context.Rooms
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .Where(r => r.AccommodationId == accommodationId && 
                           r.IsAvailable &&
                           !r.BookingDetails.Any(bd => 
                               (checkIn <= bd.Booking.CheckOutDate && 
                                checkOut >= bd.Booking.CheckInDate)))
                .ToListAsync();
        }
        
        public async Task AddAsync(Room room)
        {
            await _context.Rooms.AddAsync(room);
            await _context.SaveChangesAsync();
        }
        
        public async Task UpdateAsync(Room room)
        {
            _context.Rooms.Update(room);
            await _context.SaveChangesAsync();
        }
        
        public async Task DeleteAsync(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room != null)
            {
                _context.Rooms.Remove(room);
                await _context.SaveChangesAsync();
            }
        }
    }
}
