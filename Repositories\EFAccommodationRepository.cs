﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFAccommodationRepository : IAccommodationRepository
    {
        private readonly ApplicationDbContext _context;
        
        public EFAccommodationRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IEnumerable<Accommodation>> GetAllAsync()
        {
            return await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .ToListAsync();
        }
        
        public async Task<Accommodation> GetByIdAsync(int id)
        {
            return await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .Include(a => a.Images)
                .Include(a => a.AccommodationAmenities)
                    .ThenInclude(aa => aa.Amenity)
                .FirstOrDefaultAsync(a => a.Id == id);
        }
        
        public async Task<IEnumerable<Accommodation>> GetByLocationIdAsync(int locationId)
        {
            return await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .Where(a => a.LocationId == locationId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Accommodation>> GetByCityIdAsync(int cityId)
        {
            return await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .Where(a => a.CityId == cityId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Accommodation>> GetFeaturedAsync()
        {
            return await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .Where(a => a.IsFeatured)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Accommodation>> SearchAsync(string searchTerm, int? locationId, int? cityId, DateTime? checkIn, DateTime? checkOut, int? minPrice, int? maxPrice, int? starRating)
        {
            var query = _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Reviews)
                .Include(a => a.Rooms)
                .AsQueryable();
            
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(a => a.Name.Contains(searchTerm) || 
                                        a.Description.Contains(searchTerm) ||
                                        a.Location.Name.Contains(searchTerm) ||
                                        a.City.Name.Contains(searchTerm));
            }
            
            if (locationId.HasValue)
            {
                query = query.Where(a => a.LocationId == locationId.Value);
            }
            
            if (cityId.HasValue)
            {
                query = query.Where(a => a.CityId == cityId.Value);
            }
            
            if (starRating.HasValue)
            {
                query = query.Where(a => a.StarRating >= starRating.Value);
            }
            
            // Lọc theo giá phòng
            if (minPrice.HasValue)
            {
                query = query.Where(a => a.Rooms.Any(r => r.PricePerNight >= minPrice.Value));
            }
            
            if (maxPrice.HasValue)
            {
                query = query.Where(a => a.Rooms.Any(r => r.PricePerNight <= maxPrice.Value));
            }
            
            // Lọc theo ngày đặt phòng (kiểm tra phòng có sẵn)
            if (checkIn.HasValue && checkOut.HasValue)
            {
                query = query.Where(a => a.Rooms.Any(r => r.IsAvailable &&
                                                        !r.BookingDetails.Any(bd => 
                                                            (checkIn.Value <= bd.Booking.CheckOutDate && 
                                                             checkOut.Value >= bd.Booking.CheckInDate))));
            }
            
            return await query.ToListAsync();
        }
        
        public async Task AddAsync(Accommodation accommodation)
        {
            await _context.Accommodations.AddAsync(accommodation);
            await _context.SaveChangesAsync();
        }
        
        public async Task UpdateAsync(Accommodation accommodation)
        {
            _context.Accommodations.Update(accommodation);
            await _context.SaveChangesAsync();
        }
        
        public async Task DeleteAsync(int id)
        {
            var accommodation = await _context.Accommodations.FindAsync(id);
            if (accommodation != null)
            {
                _context.Accommodations.Remove(accommodation);
                await _context.SaveChangesAsync();
            }
        }
    }
}
