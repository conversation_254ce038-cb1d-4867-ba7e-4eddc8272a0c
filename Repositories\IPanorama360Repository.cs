﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IPanorama360Repository
    {
        Task<IEnumerable<Panorama360>> GetAllAsync();
        Task<Panorama360> GetByIdAsync(int id);
        Task<IEnumerable<Panorama360>> GetByTourIdAsync(int tourId);
        Task<IEnumerable<Panorama360>> GetByLocationIdAsync(int locationId);
        Task AddAsync(Panorama360 panorama);
        Task UpdateAsync(Panorama360 panorama);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
}
