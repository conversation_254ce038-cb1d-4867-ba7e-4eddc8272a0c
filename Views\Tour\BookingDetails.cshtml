@model ViVu.Models.TourBooking

@{
    ViewData["Title"] = "Chi tiết đặt tour";
}

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Chi tiết đặt tour #@Model.Id</h5>
                        <span class="badge @(Model.Status == TourBookingStatus.Pending ? "bg-warning" : 
                                           Model.Status == TourBookingStatus.Confirmed ? "bg-primary" :
                                           Model.Status == TourBookingStatus.Completed ? "bg-success" : "bg-danger")">
                            @(Model.Status == TourBookingStatus.Pending ? "Chờ xác nhận" : 
                             Model.Status == TourBookingStatus.Confirmed ? "Đã xác nhận" :
                             Model.Status == TourBookingStatus.Completed ? "Hoàn thành" : "Đã hủy")
                        </span>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Ngày đặt:</strong> @Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                            <p class="mb-1"><strong>Ngày tour:</strong> @Model.TourDate.ToString("dd/MM/yyyy")</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <p class="mb-1"><strong>Tổng tiền:</strong> <span class="text-primary fw-bold">@Model.TotalPrice.ToString("N0") VNĐ</span></p>
                        </div>
                    </div>
                    
                    <h5 class="mb-3">Chi tiết tour</h5>
                    <div class="table-responsive mb-4">
                        <table class="table">
                            <thead class="table-light">
                                <tr>
                                    <th>Tour</th>
                                    <th>Ngày</th>
                                    <th>Số người lớn</th>
                                    <th>Số trẻ em</th>
                                    <th class="text-end">Giá</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var detail in Model.TourBookingDetails)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(detail.Tour.ImageUrl))
                                                {
                                                    <img src="@detail.Tour.ImageUrl" alt="@detail.Tour.Name" class="me-2" style="width: 50px; height: 50px; object-fit: cover;">
                                                }
                                                <div>
                                                    <h6 class="mb-0">@detail.Tour.Name</h6>
                                                    <small class="text-muted">@detail.Tour.Duration ngày</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@detail.TourDate.ToString("dd/MM/yyyy")</td>
                                        <td>@detail.NumberOfAdults</td>
                                        <td>@detail.NumberOfChildren</td>
                                        <td class="text-end">@detail.Price.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Tổng cộng:</strong></td>
                                    <td class="text-end"><strong>@Model.TotalPrice.ToString("N0") VNĐ</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="mb-4">
                            <h5 class="mb-3">Yêu cầu đặc biệt</h5>
                            <div class="p-3 bg-light rounded">
                                @Model.SpecialRequests
                            </div>
                        </div>
                    }
                    
                    @if (Model.Status == TourBookingStatus.Pending || Model.Status == TourBookingStatus.Confirmed)
                    {
                        <form asp-action="CancelBooking" asp-route-id="@Model.Id" method="post" class="mt-4" onsubmit="return confirm('Bạn có chắc chắn muốn hủy đặt tour này?');">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times-circle me-2"></i>Hủy đặt tour
                            </button>
                        </form>
                    }
                    
                    @if (Model.Status == TourBookingStatus.Completed)
                    {
                        <div class="mt-4">
                            <a asp-controller="Tour" asp-action="Review" asp-route-id="@Model.TourBookingDetails.First().TourId" class="btn btn-primary">
                                <i class="fas fa-star me-2"></i>Đánh giá tour
                            </a>
                        </div>
                    }
                </div>
            </div>
            
            <div class="text-center">
                <a asp-controller="Tour" asp-action="MyBookings" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách đặt tour
                </a>
            </div>
        </div>
    </div>
</div>
