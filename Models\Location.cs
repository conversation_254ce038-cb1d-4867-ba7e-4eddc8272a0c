﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class Location
    {
        public int Id { get; set; }

        [Required, StringLength(100)]
        public string Name { get; set; }

        public string? Description { get; set; }

        public string? ImageUrl { get; set; }

        public bool IsFeatured { get; set; } = false;

        public int CityId { get; set; }

        [ValidateNever]
        public City? City { get; set; }

        public List<Accommodation>? Accommodations { get; set; }

        public List<Panorama360>? Panoramas { get; set; }

        [InverseProperty("Location")]
        public List<Tour>? Tours { get; set; }

        public List<Review>? Reviews { get; set; }

        // Thêm các thuộc tính mới
        public string? Address { get; set; }

        public string? Phone { get; set; }

        public string? Email { get; set; }

        public string? Website { get; set; }

        public double Latitude { get; set; }

        public double Longitude { get; set; }

        [NotMapped]
        public bool HasPanorama => Panoramas?.Any() == true;

        [NotMapped]
        public double AverageRating => Reviews?.Any() == true ? Reviews.Average(r => r.Rating) : 0;
    }
}
