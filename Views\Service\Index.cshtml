@model IEnumerable<ViVu.Models.Service>
@{
    ViewData["Title"] = "Dịch vụ";
}

<div class="container my-5">
    <h1 class="mb-4">D<PERSON><PERSON> vụ</h1>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">T<PERSON><PERSON> kiếm dịch vụ</h5>
        </div>
        <div class="card-body">
            <form method="get" action="@Url.Action("Index", "Service")">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Từ khóa</label>
                            <input type="text" name="searchTerm" class="form-control" placeholder="Tên dịch vụ, mô tả..." value="@ViewBag.SearchTerm" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label"><PERSON><PERSON><PERSON> điểm</label>
                            <select name="locationId" class="form-select">
                                <option value="">Tất cả địa điểm</option>
                                @foreach (var location in ViewBag.Locations)
                                {
                                    <option value="@location.Value" selected="@(ViewBag.LocationId != null && ViewBag.LocationId.ToString() == location.Value)">@location.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Thành phố</label>
                            <select name="cityId" class="form-select">
                                <option value="">Tất cả thành phố</option>
                                @foreach (var city in ViewBag.Cities)
                                {
                                    <option value="@city.Value" selected="@(ViewBag.CityId != null && ViewBag.CityId.ToString() == city.Value)">@city.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Ngày sử dụng</label>
                            <input type="date" name="serviceDate" class="form-control" value="@(ViewBag.ServiceDate != null ? ((DateTime)ViewBag.ServiceDate).ToString("yyyy-MM-dd") : DateTime.Today.AddDays(1).ToString("yyyy-MM-dd"))" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Giá từ (VNĐ)</label>
                            <input type="number" name="minPrice" class="form-control" placeholder="Giá thấp nhất" value="@ViewBag.MinPrice" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Giá đến (VNĐ)</label>
                            <input type="number" name="maxPrice" class="form-control" placeholder="Giá cao nhất" value="@ViewBag.MaxPrice" />
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Tìm kiếm
                    </button>
                    <a href="@Url.Action("Index", "Service")" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-repeat"></i> Đặt lại
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Không tìm thấy dịch vụ nào phù hợp với tiêu chí tìm kiếm.
        </div>
    }
    else
    {
        <div class="row row-cols-1 row-cols-md-3 g-4">
            @foreach (var service in Model)
            {
                <div class="col">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            @if (!string.IsNullOrEmpty(service.ImageUrl))
                            {
                                <img src="@service.ImageUrl" class="card-img-top" alt="@service.Name" style="height: 200px; object-fit: cover;">
                            }
                            else
                            {
                                <img src="/images/no-image.jpg" class="card-img-top" alt="No Image" style="height: 200px; object-fit: cover;">
                            }
                            
                            @if (service.IsFeatured)
                            {
                                <span class="position-absolute top-0 end-0 badge bg-warning m-2">Nổi bật</span>
                            }
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">@service.Name</h5>
                            <p class="card-text text-muted">
                                <i class="bi bi-geo-alt"></i> @service.Location?.Name, @service.City?.Name
                            </p>
                            <p class="card-text">
                                @if (service.Description.Length > 100)
                                {
                                    @(service.Description.Substring(0, 100) + "...")
                                }
                                else
                                {
                                    @service.Description
                                }
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="text-primary fw-bold">@service.Price.ToString("#,##0") VNĐ</span>
                                    <small class="text-muted">/ @service.Duration phút</small>
                                </div>
                                <a href="@Url.Action("Details", "Service", new { id = service.Id })" class="btn btn-outline-primary">
                                    <i class="bi bi-info-circle"></i> Chi tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
