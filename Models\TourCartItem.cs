﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TourCartItem
    {
        public int TourId { get; set; }
        
        public string TourName { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        public int NumberOfAdults { get; set; } = 1;
        
        public int NumberOfChildren { get; set; } = 0;
        
        public DateTime TourDate { get; set; }
        
        public string? ImageUrl { get; set; }
        
        public int Duration { get; set; }
        
        [NotMapped]
        public decimal TotalPrice => Price * (NumberOfAdults + (NumberOfChildren * 0.5m));
    }
}
