@model ViVu.Models.ViewModels.ReviewViewModel
@{
    ViewData["Title"] = "Chỉnh sửa đánh giá";
}

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Chỉnh sửa đánh giá cho @Model.ItemName</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="Type" />
                        <input type="hidden" asp-for="ItemId" />
                        <input type="hidden" asp-for="ItemName" />
                        
                        <div class="mb-4">
                            <label asp-for="Rating" class="form-label"><PERSON><PERSON>h giá của bạn</label>
                            <div class="rating">
                                <div class="rate">
                                    <input type="radio" id="star5" name="Rating" value="5" @(Model.Rating == 5 ? "checked" : "") />
                                    <label for="star5" title="5 sao">5 stars</label>
                                    <input type="radio" id="star4" name="Rating" value="4" @(Model.Rating == 4 ? "checked" : "") />
                                    <label for="star4" title="4 sao">4 stars</label>
                                    <input type="radio" id="star3" name="Rating" value="3" @(Model.Rating == 3 ? "checked" : "") />
                                    <label for="star3" title="3 sao">3 stars</label>
                                    <input type="radio" id="star2" name="Rating" value="2" @(Model.Rating == 2 ? "checked" : "") />
                                    <label for="star2" title="2 sao">2 stars</label>
                                    <input type="radio" id="star1" name="Rating" value="1" @(Model.Rating == 1 ? "checked" : "") />
                                    <label for="star1" title="1 sao">1 star</label>
                                </div>
                            </div>
                            <span asp-validation-for="Rating" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Comment" class="form-label">Nhận xét của bạn</label>
                            <textarea asp-for="Comment" class="form-control" rows="5"></textarea>
                            <span asp-validation-for="Comment" class="text-danger"></span>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay lại
                            </a>
                            <div>
                                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-outline-danger me-2">
                                    <i class="bi bi-trash"></i> Xóa
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Lưu thay đổi
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
            margin-bottom: 20px;
        }
        
        .rate {
            display: flex;
            flex-direction: row-reverse;
        }
        
        .rate:not(:checked) > input {
            position: absolute;
            top: -9999px;
        }
        
        .rate:not(:checked) > label {
            float: right;
            width: 1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 30px;
            color: #ccc;
            margin-right: 5px;
        }
        
        .rate:not(:checked) > label:before {
            content: '★ ';
        }
        
        .rate > input:checked ~ label {
            color: #ffc700;
        }
        
        .rate:not(:checked) > label:hover,
        .rate:not(:checked) > label:hover ~ label {
            color: #deb217;
        }
        
        .rate > input:checked + label:hover,
        .rate > input:checked + label:hover ~ label,
        .rate > input:checked ~ label:hover,
        .rate > input:checked ~ label:hover ~ label,
        .rate > label:hover ~ input:checked ~ label {
            color: #c59b08;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
