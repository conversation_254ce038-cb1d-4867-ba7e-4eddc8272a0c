@model IEnumerable<ViVu.Models.Panorama360>

@{
    ViewData["Title"] = "Du lịch 360° - Khám phá Bến Tre";
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Du lịch 360° - Khám phá Bến Tre</h1>
            
            <div class="card mb-4">
                <div class="card-body">
                    <p>
                        Khám phá Bến Tre qua góc nhìn 360 độ sống động. Trải nghiệm không gian và cảnh quan của các địa điểm du lịch nổi tiếng 
                        trước khi quyết định đến tham quan.
                    </p>
                    <p>
                        <a href="@Url.Action("Guide", "Panorama360")" class="btn btn-info">
                            <i class="fas fa-question-circle"></i> Hướng dẫn sử dụng
                        </a>
                    </p>
                </div>
            </div>
            
            @if (!Model.Any())
            {
                <div class="alert alert-info">
                    Hiện chưa có dữ liệu panorama 360°. Vui lòng quay lại sau.
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <h2>Địa điểm du lịch 360°</h2>
                    </div>
                    
                    @foreach (var item in Model.Where(p => p.Type == "Location" && p.IsActive))
                    {
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="panorama-thumbnail" style="background-image: url('@item.ImageUrl');">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="panorama-link">
                                        <div class="panorama-overlay">
                                            <i class="fas fa-vr-cardboard fa-3x"></i>
                                            <span>Xem 360°</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">@item.Name</h5>
                                    <p class="card-text">@(item.Description.Length > 100 ? item.Description.Substring(0, 100) + "..." : item.Description)</p>
                                </div>
                                <div class="card-footer">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="btn btn-primary btn-sm">
                                        <i class="fas fa-vr-cardboard"></i> Xem 360°
                                    </a>
                                    @if (item.Location != null)
                                    {
                                        <a href="@Url.Action("Details", "Location", new { id = item.LocationId })" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-info-circle"></i> Chi tiết địa điểm
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
                
                <div class="row mt-5">
                    <div class="col-md-12 mb-4">
                        <h2>Tour du lịch 360°</h2>
                    </div>
                    
                    @foreach (var item in Model.Where(p => p.Type == "Tour" && p.IsActive))
                    {
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="panorama-thumbnail" style="background-image: url('@item.ImageUrl');">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="panorama-link">
                                        <div class="panorama-overlay">
                                            <i class="fas fa-vr-cardboard fa-3x"></i>
                                            <span>Xem 360°</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">@item.Name</h5>
                                    <p class="card-text">@(item.Description.Length > 100 ? item.Description.Substring(0, 100) + "..." : item.Description)</p>
                                </div>
                                <div class="card-footer">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="btn btn-primary btn-sm">
                                        <i class="fas fa-vr-cardboard"></i> Xem 360°
                                    </a>
                                    @if (item.Tour != null)
                                    {
                                        <a href="@Url.Action("Details", "Tour", new { id = item.TourId })" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-info-circle"></i> Chi tiết tour
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>
