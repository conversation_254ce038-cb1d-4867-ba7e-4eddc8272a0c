@model ViVu.Models.Review
@{
    ViewData["Title"] = "Chi tiết đánh giá";
    Layout = "_AdminLayout";
    
    string itemName = "";
    string itemType = "";
    int itemId = 0;
    string controllerName = "";
    
    if (Model.AccommodationId.HasValue && Model.Accommodation != null)
    {
        itemName = Model.Accommodation.Name;
        itemType = "Chỗ ở";
        itemId = Model.AccommodationId.Value;
        controllerName = "Accommodation";
    }
    else if (Model.TourId.HasValue && Model.Tour != null)
    {
        itemName = Model.Tour.Name;
        itemType = "Tour";
        itemId = Model.TourId.Value;
        controllerName = "Tour";
    }
    else if (Model.ServiceId.HasValue && Model.Service != null)
    {
        itemName = Model.Service.Name;
        itemType = "Dịch vụ";
        itemId = Model.ServiceId.Value;
        controllerName = "Service";
    }
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý đánh giá</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>

    <div class="row">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Thông tin đánh giá
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Thông tin người dùng</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Họ tên:</th>
                                    <td>@Model.User?.FullName</td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>@Model.User?.Email</td>
                                </tr>
                                <tr>
                                    <th>Ngày đánh giá:</th>
                                    <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Thông tin đối tượng</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Loại:</th>
                                    <td><span class="badge bg-info">@itemType</span></td>
                                </tr>
                                <tr>
                                    <th>Tên:</th>
                                    <td>@itemName</td>
                                </tr>
                                <tr>
                                    <th>Liên kết:</th>
                                    <td>
                                        <a asp-area="Admin" asp-controller="@controllerName" asp-action="Details" asp-route-id="@itemId" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i> Xem chi tiết
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h5>Đánh giá</h5>
                        <div class="text-warning mb-2">
                            @for (int i = 1; i <= 5; i++)
                            {
                                if (i <= Model.Rating)
                                {
                                    <i class="fas fa-star fa-2x"></i>
                                }
                                else
                                {
                                    <i class="far fa-star fa-2x"></i>
                                }
                            }
                            <span class="ms-2 fs-4">(@Model.Rating/5)</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h5>Nhận xét</h5>
                        <div class="p-3 bg-light rounded">
                            @Model.Comment
                        </div>
                    </div>

                    <div class="d-flex mt-4">
                        <a asp-action="Index" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
