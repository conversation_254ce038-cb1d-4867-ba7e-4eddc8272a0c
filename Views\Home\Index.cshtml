﻿@model IEnumerable<ViVu.Models.Product>

@{
    ViewData["Title"] = "Trang chủ";
}

<!-- Destination Showcase - Main Container -->
<div id="destination-showcase-container" class="full-page">
    <!-- Destination Showcase - Asia -->
    <div class="destination-showcase asia">
        <div class="destination-container">
            <!-- Main Content Area -->
            <div class="showcase-content">
                <!-- Left side with continent info -->
                <div class="showcase-info">
                    <h1 class="continent-name">Bến Tre</h1>
                    <p class="continent-description">Khám phá vẻ đẹp miền sông nước, những vườn dừa xanh mát và văn hóa đặc sắc của xứ dừa Bến Tre. Từ những làng nghề truyền thống đến ẩm thực đặc sản, Bến Tre mang đến trải nghiệm du lịch khó quên cho mọi du khách.</p>
                    <button class="explore-btn">
                        <i class="bi bi-compass me-2"></i>
                        Khám phá ngay
                    </button>
                </div>

                <!-- Right side with tab navigation and content -->
                <div class="showcase-destinations">
                    <!-- Tab Navigation -->
                    <div class="showcase-tab-navigation">
                        <div class="tab-nav-container">
                            <button class="tab-nav-btn active" data-tab="search">
                                <i class="bi bi-search"></i>
                                <span>Tìm kiếm</span>
                            </button>
                            <button class="tab-nav-btn" data-tab="destinations">
                                <i class="bi bi-geo-alt"></i>
                                <span>Điểm đến</span>
                            </button>
                            <button class="tab-nav-btn" data-tab="hotels">
                                <i class="bi bi-building"></i>
                                <span>Khách sạn</span>
                            </button>
                            <button class="tab-nav-btn" data-tab="tours">
                                <i class="bi bi-compass"></i>
                                <span>Tour du lịch</span>
                            </button>
                            <button class="tab-nav-btn" data-tab="experiences">
                                <i class="bi bi-stars"></i>
                                <span>Trải nghiệm</span>
                            </button>
                            <button class="tab-nav-btn" data-tab="about">
                                <i class="bi bi-info-circle"></i>
                                <span>Giới thiệu</span>
                            </button>
                        </div>
                    </div>

                    <!-- Tab Content Area -->
                    <div class="showcase-tab-content">
                <!-- Tab 1: Search Content -->
                <div class="tab-content-panel active" id="search-panel">
                    <div class="search-form-modern">
                        <!-- Search Tabs -->
                        <div class="search-tabs">
                            <ul class="nav nav-tabs border-0" id="searchTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active d-flex align-items-center" id="hotel-tab" data-bs-toggle="tab" data-bs-target="#hotel-search" type="button" role="tab" aria-controls="hotel-search" aria-selected="true">
                                        <i class="bi bi-building me-2"></i> Khách sạn
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center" id="tour-tab" data-bs-toggle="tab" data-bs-target="#tour-search" type="button" role="tab" aria-controls="tour-search" aria-selected="false">
                                        <i class="bi bi-compass me-2"></i> Tour du lịch
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center" id="service-tab" data-bs-toggle="tab" data-bs-target="#service-search" type="button" role="tab" aria-controls="service-search" aria-selected="false">
                                        <i class="bi bi-stars me-2"></i> Dịch vụ
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity-search" type="button" role="tab" aria-controls="activity-search" aria-selected="false">
                                        <i class="bi bi-calendar-event me-2"></i> Hoạt động
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center" id="combo-tab" data-bs-toggle="tab" data-bs-target="#combo-search" type="button" role="tab" aria-controls="combo-search" aria-selected="false">
                                        <i class="bi bi-box me-2"></i> Combo tiết kiệm
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <!-- Tab Content -->
                        <div class="tab-content p-4" id="searchTabsContent">
                            <!-- Hotel Search Tab -->
                            <div class="tab-pane fade show active" id="hotel-search" role="tabpanel" aria-labelledby="hotel-tab">
                                <form asp-controller="Search" asp-action="Search" method="post">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                    <option value="">-- Chọn địa điểm --</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày nhận phòng</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="checkIn" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-x me-1 text-primary"></i>Ngày trả phòng</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="checkOut" class="form-control shadow-sm" value="@DateTime.Today.AddDays(2).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                                <i class="bi bi-search me-1"></i> TÌM
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Tour Search Tab -->
                            <div class="tab-pane fade" id="tour-search" role="tabpanel" aria-labelledby="tour-tab">
                                <form asp-controller="Tour" asp-action="Search" method="post">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Điểm đến</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                    <option value="">-- Chọn điểm đến --</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày bắt đầu</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="startDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-people me-1 text-primary"></i>Số người</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-person"></i></span>
                                                <select name="groupSize" class="form-select shadow-sm">
                                                    <option value="1">1 người</option>
                                                    <option value="2" selected>2 người</option>
                                                    <option value="3">3 người</option>
                                                    <option value="4">4 người</option>
                                                    <option value="5">5 người</option>
                                                    <option value="6">6+ người</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                                <i class="bi bi-search me-1"></i> TÌM
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Service Search Tab -->
                            <div class="tab-pane fade" id="service-search" role="tabpanel" aria-labelledby="service-tab">
                                <form asp-controller="Service" asp-action="Index" method="get">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                    <option value="">-- Chọn địa điểm --</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày sử dụng</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="serviceDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-search me-1 text-primary"></i>Tìm kiếm</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <input type="text" name="searchTerm" class="form-control shadow-sm" placeholder="Tên dịch vụ...">
                                            </div>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                                <i class="bi bi-search me-1"></i> TÌM
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Activity Search Tab -->
                            <div class="tab-pane fade" id="activity-search" role="tabpanel" aria-labelledby="activity-tab">
                                <form asp-controller="Activity" asp-action="Search" method="post">
                                    <div class="row g-3">
                                        <div class="col-md-5">
                                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                    <option value="">-- Chọn địa điểm --</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày tham gia</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="activityDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                                <i class="bi bi-search me-1"></i> TÌM
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Combo Search Tab -->
                            <div class="tab-pane fade" id="combo-search" role="tabpanel" aria-labelledby="combo-tab">
                                <form asp-controller="Combo" asp-action="Search" method="post">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Điểm đến</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                    <option value="">-- Chọn điểm đến --</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày đi</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="startDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label fw-bold"><i class="bi bi-calendar-x me-1 text-primary"></i>Ngày về</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                                <input type="date" name="endDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(3).ToString("yyyy-MM-dd")">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label fw-bold"><i class="bi bi-people me-1 text-primary"></i>Số người</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bi bi-person"></i></span>
                                                <select name="groupSize" class="form-select shadow-sm">
                                                    <option value="1">1 người</option>
                                                    <option value="2" selected>2 người</option>
                                                    <option value="3">3 người</option>
                                                    <option value="4">4 người</option>
                                                    <option value="5">5+ người</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                                <i class="bi bi-search me-1"></i> TÌM
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab 2: Destinations Content -->
                <div class="tab-content-panel" id="destinations-panel">
                    <div class="destinations-showcase">
                        <div class="section-title">
                            <h2>Điểm đến nổi bật</h2>
                            <p>Khám phá những điểm đến tuyệt vời tại miền Tây Nam Bộ</p>
                        </div>

                        <!-- Featured Banner -->
                        <div class="featured-destination-banner mb-4">
                            <div class="position-relative">
                                <img src="/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg"
                                     alt="Bến Tre" class="img-fluid rounded" style="height: 300px; width: 100%; object-fit: cover;">
                                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                                     style="background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4));">
                                    <div class="text-center text-white">
                                        <h3 class="display-5 fw-bold mb-3">Bến Tre - Xứ sở dừa</h3>
                                        <p class="lead mb-4">Khám phá vẻ đẹp bình dị của miền Tây</p>
                                        <a href="/Explore/BenTre" class="btn btn-light btn-lg">Khám phá ngay</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Destinations Grid -->
                        <div class="row">
                            @if (ViewBag.FeaturedLocations != null)
                            {
                                foreach (var location in ViewBag.FeaturedLocations)
                                {
                                    <div class="col-md-4 mb-4">
                                        <div class="destination-card-modern">
                                            <img src="@(string.IsNullOrEmpty(location.ImageUrl) ? "/images/default-location.jpg" : location.ImageUrl)"
                                                 alt="@location.Name" class="card-img">
                                            <div class="card-overlay">
                                                <div class="card-content">
                                                    <h5 class="card-title">@location.Name</h5>
                                                    <p class="card-subtitle">@location.Description</p>
                                                    <a href="/Location/Details/@location.Id" class="btn btn-outline-light btn-sm">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- Tab 3: Hotels Content -->
                <div class="tab-content-panel" id="hotels-panel">
                    <div class="hotels-showcase">
                        <div class="section-title">
                            <h2>Khách sạn đề xuất</h2>
                            <p>Những lựa chọn lưu trú tốt nhất cho chuyến đi của bạn</p>
                        </div>

                        <div class="row">
                            @if (ViewBag.FeaturedAccommodations != null)
                            {
                                foreach (var accommodation in ViewBag.FeaturedAccommodations)
                                {
                                    <div class="col-md-4 mb-4">
                                        <div class="hotel-card-modern">
                                            <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default-hotel.jpg" : accommodation.ImageUrl)"
                                                 class="card-img-top" alt="@accommodation.Name">
                                            <div class="card-body">
                                                <h5 class="card-title">@accommodation.Name</h5>
                                                <p class="card-text">
                                                    <i class="bi bi-geo-alt-fill text-primary"></i> @accommodation.Location?.Name
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="text-success fw-bold">@accommodation.MinPrice.ToString("#,##0") VNĐ/đêm</span>
                                                    <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id"
                                                       class="btn btn-modern btn-modern-outline btn-sm">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- Tab 4: Tours Content -->
                <div class="tab-content-panel" id="tours-panel">
                    <div class="tours-showcase">
                        <div class="section-title">
                            <h2>Tour du lịch đề xuất</h2>
                            <p>Những chuyến đi thú vị đang chờ đón bạn</p>
                        </div>

                        <!-- Featured Tour Banner -->
                        <div class="featured-tour-banner mb-4">
                            <div class="position-relative">
                                <img src="/images/tours/banners/tour_kham-pha-ben-tre_banner.jpg"
                                     class="img-fluid w-100 rounded" style="height: 350px; object-fit: cover;" alt="Tour Khám Phá Bến Tre">
                                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center"
                                     style="background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);">
                                    <div class="container-fluid px-4">
                                        <div class="row">
                                            <div class="col-md-7">
                                                <span class="badge bg-warning mb-2">Tour nổi bật</span>
                                                <h2 class="text-white mb-3">Tour Khám Phá Bến Tre 2 Ngày 1 Đêm</h2>
                                                <p class="text-white mb-3">Trải nghiệm vẻ đẹp sông nước miền Tây, thưởng thức ẩm thực đặc sắc</p>
                                                <div class="d-flex align-items-center">
                                                    <span class="text-white me-3 fs-4 fw-bold">1,500,000 VNĐ</span>
                                                    <a href="#" class="btn btn-modern btn-warning">Đặt ngay</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tours Grid -->
                        <div class="row">
                            @if (ViewBag.FeaturedTours != null && ViewBag.FeaturedTours.Count > 0)
                            {
                                foreach (var tour in ViewBag.FeaturedTours)
                                {
                                    <div class="col-md-4 mb-4">
                                        <div class="tour-card-modern">
                                            <div class="position-relative">
                                                <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/default-tour.jpg" : tour.ImageUrl)"
                                                     class="card-img-top" style="height: 200px; object-fit: cover;" alt="@tour.Name">
                                                <div class="position-absolute top-0 end-0 m-2">
                                                    <span class="badge bg-primary">Tour mới</span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <h5 class="card-title mb-2">@tour.Name</h5>
                                                <p class="card-text text-muted mb-2">
                                                    <i class="bi bi-geo-alt-fill"></i> @tour.Location?.Name, @tour.City?.Name
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span><i class="bi bi-clock"></i> @tour.Duration ngày</span>
                                                    <span><i class="bi bi-people-fill"></i> Tối đa @tour.MaxGroupSize người</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <span class="text-success fw-bold">@tour.Price.ToString("#,##0") VNĐ</span>
                                                    <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id"
                                                       class="btn btn-modern btn-modern-outline btn-sm">Xem chi tiết</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- Tab 5: Experiences Content -->
                <div class="tab-content-panel" id="experiences-panel">
                    <div class="experiences-showcase">
                        <div class="section-title">
                            <h2>Trải nghiệm đặc biệt</h2>
                            <p>Khám phá Bến Tre qua những trải nghiệm độc đáo</p>
                        </div>

                        <!-- 360° Experience Banner -->
                        <div class="panorama-banner mb-4">
                            <div class="position-relative">
                                <div style="height: 350px; background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg') center/cover no-repeat; border-radius: 8px;"></div>
                                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center">
                                    <div class="container-fluid px-4">
                                        <div class="row">
                                            <div class="col-md-7">
                                                <span class="badge bg-info mb-2">Tính năng mới</span>
                                                <h2 class="text-white mb-3">Khám phá Bến Tre qua góc nhìn 360°</h2>
                                                <p class="text-white mb-3">Trải nghiệm không gian và cảnh quan trước khi quyết định đến tham quan</p>
                                                <div class="d-flex align-items-center">
                                                    <a asp-controller="Panorama360" asp-action="Index" class="btn btn-modern btn-info me-3">
                                                        <i class="fas fa-vr-cardboard me-2"></i>Khám phá ngay
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Experience Cards -->
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="experience-card">
                                    <div class="mb-3 text-center">
                                        <i class="bi bi-water text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="text-center">Du lịch sông nước</h5>
                                    <p class="text-center">Trải nghiệm đi thuyền trên sông, khám phá các cồn và làng nghề truyền thống</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="experience-card">
                                    <div class="mb-3 text-center">
                                        <i class="bi bi-tree text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="text-center">Khám phá vườn trái cây</h5>
                                    <p class="text-center">Tham quan vườn trái cây nhiệt đới, thưởng thức trái cây tươi ngon</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="experience-card">
                                    <div class="mb-3 text-center">
                                        <i class="bi bi-cup-hot text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="text-center">Ẩm thực đặc sắc</h5>
                                    <p class="text-center">Thưởng thức các món ăn đặc sản của Bến Tre</p>
                                </div>
                            </div>
                        </div>

                        <!-- AI Recommendation -->
                        <div class="ai-recommendation-card">
                            <div class="row g-0">
                                <div class="col-md-6">
                                    <div class="bg-primary text-white p-4 h-100 d-flex flex-column justify-content-center">
                                        <span class="badge bg-warning mb-3">Tính năng mới</span>
                                        <h3 class="mb-3">AI Gợi Ý Lịch Trình Du Lịch</h3>
                                        <p class="mb-4">Để AI giúp bạn lên kế hoạch du lịch Bến Tre phù hợp với sở thích và ngân sách</p>
                                        <div>
                                            <a asp-controller="Recommendation" asp-action="SimpleForm" class="btn btn-modern btn-light">
                                                <i class="bi bi-magic me-2"></i>Tạo lịch trình ngay
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="position-relative h-100">
                                        <img src="/images/banners/ai-recommendation.jpg" class="w-100 h-100" style="object-fit: cover;" alt="AI Recommendation">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab 6: About Content -->
                <div class="tab-content-panel" id="about-panel">
                    <div class="about-showcase">
                        <div class="section-title">
                            <h2>Khám phá Bến Tre</h2>
                            <p>Vùng đất của dừa và sông nước</p>
                        </div>

                        <div class="row align-items-center mb-4">
                            <div class="col-md-6 mb-4">
                                <img src="/images/banners/banner_home_01.jpg" class="img-fluid rounded shadow" alt="Bến Tre - Xứ Dừa">
                            </div>
                            <div class="col-md-6 mb-4">
                                <h3 class="mb-3">Vùng đất của dừa và sông nước</h3>
                                <p>Bến Tre được mệnh danh là "Xứ Dừa" với hơn 72.000 hecta dừa, chiếm 1/3 diện tích dừa cả nước.</p>
                                <p>Bến Tre còn là vùng đất của sông nước với hệ thống sông ngòi chằng chịt, tạo nên cảnh quan thiên nhiên tuyệt đẹp.</p>
                                <a asp-controller="Explore" asp-action="BenTre" class="btn btn-modern btn-modern-primary">Tìm hiểu thêm</a>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4 mb-4">
                                <div class="info-card text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-geo-alt text-primary" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <h5 class="mb-3">Vị trí địa lý</h5>
                                    <p>Bến Tre nằm ở phía Đông Nam của đồng bằng sông Cửu Long, cách TP. Hồ Chí Minh khoảng 85km</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="info-card text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-clock-history text-primary" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <h5 class="mb-3">Lịch sử văn hóa</h5>
                                    <p>Bến Tre có lịch sử hình thành lâu đời, là quê hương của nhiều danh nhân, anh hùng dân tộc</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="info-card text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-calendar-event text-primary" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <h5 class="mb-3">Thời điểm lý tưởng</h5>
                                    <p>Thời gian lý tưởng để du lịch Bến Tre là từ tháng 12 đến tháng 4 năm sau</p>
                                </div>
                            </div>
                        </div>

                        <!-- Newsletter -->
                        <div class="newsletter-section mt-5">
                            <div class="section-title">
                                <h3>Đăng ký nhận thông tin</h3>
                            </div>
                            <div class="newsletter-card">
                                <div class="card-body p-4 text-center">
                                    <h4>Đăng ký nhận thông tin ưu đãi</h4>
                                    <p>Nhận thông tin về các ưu đãi và khuyến mãi mới nhất từ chúng tôi.</p>
                                    <div class="row justify-content-center">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <input type="email" class="form-control" placeholder="Nhập email của bạn">
                                                <button class="btn btn-modern btn-modern-primary" type="button">Đăng ký</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>

            <!-- Bottom navigation controls -->
            <div class="showcase-controls">
                <div class="continent-nav">
                    <div class="nav-btn">
                        <i class="bi bi-arrow-left"></i>
                    </div>
                    <span>Cần Thơ</span>
                </div>

                <div class="continent-indicators">
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator active"></div>
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator"></div>
                </div>

                <div class="continent-nav">
                    <span>Tiền Giang</span>
                    <div class="nav-btn">
                        <i class="bi bi-arrow-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="searchModalLabel">Tìm kiếm</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/Search" method="get">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="Nhập từ khóa tìm kiếm..." name="q" required>
                        <button class="btn btn-modern btn-modern-primary" type="submit">
                            <i class="bi bi-search me-1"></i> Tìm
                        </button>
                    </div>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="tours" id="searchTours" name="type" checked>
                            <label class="form-check-label" for="searchTours">
                                Tours
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="hotels" id="searchHotels" name="type" checked>
                            <label class="form-check-label" for="searchHotels">
                                Khách sạn
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="destinations" id="searchDestinations" name="type" checked>
                            <label class="form-check-label" for="searchDestinations">
                                Địa điểm
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



