﻿@model IEnumerable<ViVu.Models.Order>

@{
    ViewData["Title"] = "Quản lý đơn hàng";
}

<h2 class="text-center my-4">Order Management</h2>

<div class="container">
    <table class="table table-bordered table-striped shadow-sm">
        <thead class="table-dark text-center">
            <tr>
                <th>Order ID</th>
                <th>Customer Name</th>
                <th>Order Date</th>
                <th>Total Amount</th>
                <th>Address</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            @if (Model.Any())
            {
                @foreach (var order in Model)
                {
                    <tr class="text-center">
                        <td>@order.Id</td>
                        <td>@(order.ApplicationUser?.FullName ?? "Unknown")</td>
                        <td>@order.OrderDate.ToString("yyyy-MM-dd HH:mm")</td>
                        <td>@order.TotalPrice.ToString("#,##0") VND</td>
                        <td>@(order.ShippingAddress ?? "N/A")</td>
                        <td>@(order.Notes ?? "N/A")</td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="5" class="text-center text-muted">No orders found.</td>
                </tr>
            }
        </tbody>
    </table>
</div>
