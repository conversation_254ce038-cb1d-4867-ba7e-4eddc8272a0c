﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class ServiceController : Controller
    {
        private readonly IServiceRepository _serviceRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public ServiceController(
            IServiceRepository serviceRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            ApplicationDbContext context,
            IWebHostEnvironment webHostEnvironment)
        {
            _serviceRepository = serviceRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        // GET: Admin/Service
        public async Task<IActionResult> Index()
        {
            var services = await _serviceRepository.GetAllAsync();
            return View(services);
        }

        // GET: Admin/Service/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var service = await _serviceRepository.GetByIdWithDetailsAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            return View(service);
        }

        // GET: Admin/Service/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View();
        }

        // POST: Admin/Service/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Service service, IFormFile imageFile)
        {
            // Bỏ qua xác thực ModelState cho trường Price
            if (service.Price > 0)
            {
                ModelState.Remove("Price");
            }

            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    service.ImageUrl = await SaveImage(imageFile);
                }

                // Thêm dịch vụ vào cơ sở dữ liệu
                await _serviceRepository.AddAsync(service);

                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", service.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", service.CityId);

            return View(service);
        }

        // GET: Admin/Service/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var service = await _serviceRepository.GetByIdAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", service.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", service.CityId);

            return View(service);
        }

        // POST: Admin/Service/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Service service, IFormFile imageFile)
        {
            if (id != service.Id)
            {
                return NotFound();
            }

            // Bỏ qua xác thực ModelState cho trường Price
            if (service.Price > 0)
            {
                ModelState.Remove("Price");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Lấy dịch vụ hiện tại từ cơ sở dữ liệu
                    var existingService = await _serviceRepository.GetByIdAsync(id);
                    if (existingService == null)
                    {
                        return NotFound();
                    }

                    // Cập nhật thông tin dịch vụ
                    existingService.Name = service.Name;
                    existingService.Description = service.Description;
                    existingService.Details = service.Details;
                    existingService.Price = service.Price;
                    existingService.Duration = service.Duration;
                    existingService.IsFeatured = service.IsFeatured;
                    existingService.LocationId = service.LocationId;
                    existingService.CityId = service.CityId;

                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        existingService.ImageUrl = await SaveImage(imageFile);
                    }

                    // Cập nhật dịch vụ
                    await _serviceRepository.UpdateAsync(existingService);

                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await ServiceExists(service.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name", service.LocationId);
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name", service.CityId);

            return View(service);
        }

        // GET: Admin/Service/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var service = await _serviceRepository.GetByIdAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            return View(service);
        }

        // POST: Admin/Service/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _serviceRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> ServiceExists(int id)
        {
            var service = await _serviceRepository.GetByIdAsync(id);
            return service != null;
        }

        private async Task<string> SaveImage(IFormFile imageFile)
        {
            string wwwRootPath = _webHostEnvironment.WebRootPath;
            string fileName = Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName);
            string servicesPath = Path.Combine(wwwRootPath, "images", "services");

            if (!Directory.Exists(servicesPath))
            {
                Directory.CreateDirectory(servicesPath);
            }

            using (var fileStream = new FileStream(Path.Combine(servicesPath, fileName), FileMode.Create))
            {
                await imageFile.CopyToAsync(fileStream);
            }

            return "/images/services/" + fileName;
        }
    }
}
