/**
 * Homepage Tabs Functionality
 * Handles the tabbed interface for the homepage content
 */

document.addEventListener("DOMContentLoaded", function () {
    initializeHomepageTabs();
});

/**
 * Initialize homepage tabs functionality
 */
function initializeHomepageTabs() {
    // Initialize Bootstrap tabs
    const triggerTabList = [].slice.call(document.querySelectorAll('#homepageTabs button[data-bs-toggle="tab"]'));
    triggerTabList.forEach(function (triggerEl) {
        const tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
            
            // Add smooth transition effect
            const targetPane = document.querySelector(triggerEl.getAttribute('data-bs-target'));
            if (targetPane) {
                targetPane.style.opacity = '0';
                setTimeout(() => {
                    targetPane.style.opacity = '1';
                }, 50);
            }
        });
    });

    // Add keyboard navigation
    addKeyboardNavigation();
    
    // Add smooth scrolling to tab container when switching tabs
    addSmoothScrolling();
    
    // Initialize tab content animations
    initializeTabAnimations();
}

/**
 * Add keyboard navigation for tabs
 */
function addKeyboardNavigation() {
    const tabButtons = document.querySelectorAll('#homepageTabs button[data-bs-toggle="tab"]');
    
    tabButtons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            let nextIndex;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    nextIndex = index > 0 ? index - 1 : tabButtons.length - 1;
                    tabButtons[nextIndex].focus();
                    tabButtons[nextIndex].click();
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    nextIndex = index < tabButtons.length - 1 ? index + 1 : 0;
                    tabButtons[nextIndex].focus();
                    tabButtons[nextIndex].click();
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    tabButtons[0].focus();
                    tabButtons[0].click();
                    break;
                    
                case 'End':
                    e.preventDefault();
                    tabButtons[tabButtons.length - 1].focus();
                    tabButtons[tabButtons.length - 1].click();
                    break;
            }
        });
    });
}

/**
 * Add smooth scrolling when switching tabs
 */
function addSmoothScrolling() {
    const tabContainer = document.querySelector('.homepage-tabs-container');
    const tabButtons = document.querySelectorAll('#homepageTabs button[data-bs-toggle="tab"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function() {
            // Smooth scroll to tab container
            if (tabContainer) {
                const offsetTop = tabContainer.offsetTop - 100; // Account for fixed header
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Initialize animations for tab content
 */
function initializeTabAnimations() {
    const tabPanes = document.querySelectorAll('.homepage-tab-content .tab-pane');
    
    // Add intersection observer for animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1
    });
    
    tabPanes.forEach(pane => {
        observer.observe(pane);
    });
}

/**
 * Handle tab switching with custom effects
 */
function switchToTab(tabId) {
    const tabButton = document.querySelector(`#homepageTabs button[data-bs-target="${tabId}"]`);
    if (tabButton) {
        const tab = new bootstrap.Tab(tabButton);
        tab.show();
    }
}

/**
 * Get currently active tab
 */
function getActiveTab() {
    const activeButton = document.querySelector('#homepageTabs button.active');
    return activeButton ? activeButton.getAttribute('data-bs-target') : null;
}

/**
 * Add loading state to tab content
 */
function showTabLoading(tabId) {
    const tabPane = document.querySelector(tabId);
    if (tabPane) {
        tabPane.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang tải...</span>
                </div>
                <p class="mt-3">Đang tải nội dung...</p>
            </div>
        `;
    }
}

/**
 * Handle responsive behavior for tabs
 */
function handleResponsiveTabs() {
    const tabContainer = document.querySelector('.homepage-main-tabs');
    const tabButtons = document.querySelectorAll('#homepageTabs .nav-link');
    
    function checkTabOverflow() {
        if (window.innerWidth < 768) {
            // Mobile: Stack tabs vertically or create dropdown
            tabContainer.classList.add('mobile-tabs');
        } else {
            tabContainer.classList.remove('mobile-tabs');
        }
    }
    
    // Check on load and resize
    checkTabOverflow();
    window.addEventListener('resize', checkTabOverflow);
}

// Initialize responsive behavior
document.addEventListener("DOMContentLoaded", function () {
    handleResponsiveTabs();
});

// Export functions for external use
window.HomepageTabs = {
    switchToTab,
    getActiveTab,
    showTabLoading
};
