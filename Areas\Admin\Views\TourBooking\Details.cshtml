@model ViVu.Models.TourBooking

@{
    ViewData["Title"] = "Chi tiết đặt tour";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý đặt tour</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Thông tin đặt tour
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="mb-3">Thông tin khách hàng</h5>
                    <div class="mb-2">
                        <strong>Họ tên:</strong>
                        <span>@Model.ApplicationUser.FullName</span>
                    </div>
                    <div class="mb-2">
                        <strong>Email:</strong>
                        <span>@Model.ApplicationUser.Email</span>
                    </div>
                    <div class="mb-2">
                        <strong>Địa chỉ:</strong>
                        <span>@Model.ApplicationUser.Address</span>
                    </div>
                    <div class="mb-2">
                        <strong>Số điện thoại:</strong>
                        <span>@Model.ApplicationUser.PhoneNumber</span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="mb-3">Thông tin đặt tour</h5>
                    <div class="mb-2">
                        <strong>Mã đặt tour:</strong>
                        <span>#@Model.Id</span>
                    </div>
                    <div class="mb-2">
                        <strong>Ngày đặt:</strong>
                        <span>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</span>
                    </div>
                    <div class="mb-2">
                        <strong>Ngày tour:</strong>
                        <span>@Model.TourDate.ToString("dd/MM/yyyy")</span>
                    </div>
                    <div class="mb-2">
                        <strong>Tổng tiền:</strong>
                        <span class="text-primary fw-bold">@Model.TotalPrice.ToString("N0") VNĐ</span>
                    </div>
                    <div class="mb-2">
                        <strong>Trạng thái:</strong>
                        <span class="badge @(Model.Status == TourBookingStatus.Pending ? "bg-warning" : 
                                           Model.Status == TourBookingStatus.Confirmed ? "bg-primary" :
                                           Model.Status == TourBookingStatus.Completed ? "bg-success" : "bg-danger")">
                            @(Model.Status == TourBookingStatus.Pending ? "Chờ xác nhận" : 
                             Model.Status == TourBookingStatus.Confirmed ? "Đã xác nhận" :
                             Model.Status == TourBookingStatus.Completed ? "Hoàn thành" : "Đã hủy")
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <h5 class="mb-3">Cập nhật trạng thái</h5>
                <form asp-action="UpdateStatus" method="post">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <select name="status" class="form-select">
                                <option value="0" selected="@(Model.Status == TourBookingStatus.Pending)">Chờ xác nhận</option>
                                <option value="1" selected="@(Model.Status == TourBookingStatus.Confirmed)">Đã xác nhận</option>
                                <option value="2" selected="@(Model.Status == TourBookingStatus.Completed)">Hoàn thành</option>
                                <option value="3" selected="@(Model.Status == TourBookingStatus.Cancelled)">Đã hủy</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Cập nhật trạng thái
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <h5 class="mb-3">Chi tiết tour</h5>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Tour</th>
                            <th>Ngày</th>
                            <th>Số người lớn</th>
                            <th>Số trẻ em</th>
                            <th>Giá</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.TourBookingDetails)
                        {
                            <tr>
                                <td>
                                    <a asp-area="Admin" asp-controller="Tour" asp-action="Details" asp-route-id="@detail.TourId">
                                        @detail.Tour.Name
                                    </a>
                                </td>
                                <td>@detail.TourDate.ToString("dd/MM/yyyy")</td>
                                <td>@detail.NumberOfAdults</td>
                                <td>@detail.NumberOfChildren</td>
                                <td>@detail.Price.ToString("N0") VNĐ</td>
                                <td>
                                    <span class="badge @(detail.Status == TourBookingDetailStatus.Pending ? "bg-warning" : 
                                                       detail.Status == TourBookingDetailStatus.Confirmed ? "bg-primary" :
                                                       detail.Status == TourBookingDetailStatus.Completed ? "bg-success" : "bg-danger")">
                                        @(detail.Status == TourBookingDetailStatus.Pending ? "Chờ xác nhận" : 
                                         detail.Status == TourBookingDetailStatus.Confirmed ? "Đã xác nhận" :
                                         detail.Status == TourBookingDetailStatus.Completed ? "Hoàn thành" : "Đã hủy")
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            @if (!string.IsNullOrEmpty(Model.SpecialRequests))
            {
                <div class="mt-4">
                    <h5 class="mb-3">Yêu cầu đặc biệt</h5>
                    <div class="p-3 bg-light rounded">
                        @Model.SpecialRequests
                    </div>
                </div>
            }
            
            <div class="mt-4">
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
                </a>
                <form asp-action="Delete" method="post" class="d-inline-block">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa đơn đặt tour này?');">
                        <i class="fas fa-trash me-1"></i> Xóa
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
