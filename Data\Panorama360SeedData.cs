﻿﻿using ViVu.Models;
using System.Text.Json;

namespace ViVu.Data
{
    public static class Panorama360SeedData
    {
        public static void SeedPanorama360s(ApplicationDbContext context)
        {
            // Kiểm tra xem đã có dữ liệu panorama 360 nào chưa
            if (context.Panorama360s.Any())
            {
                return; // Đã có dữ liệu, không cần seed
            }

            // Lấy các địa điểm và tour hiện có
            var conPhungLocation = context.Locations.FirstOrDefault(l => l.Name.Contains("Cồn Phụng"));
            var langNgheDuaLocation = context.Locations.FirstOrDefault(l => l.Name.Contains("Làng nghề dừa"));

            // Tạo một panorama mẫu cho tour nếu có tour
            var benTreTour = context.Tours.FirstOrDefault();

            if (conPhungLocation == null && langNgheDuaLocation == null && benTreTour == null)
            {
                return; // Không tìm thấy dữ liệu cần thiết
            }

            // Tạo danh sách panorama 360 mẫu
            var panoramas = new List<Panorama360>();

            // Panorama cho Cồn Phụng
            if (conPhungLocation != null)
            {
                var hotspotData = new List<object>
                {
                    new
                    {
                        pitch = 0,
                        yaw = 30,
                        type = "info",
                        text = "Đền thờ Đạo Dừa",
                        URL = $"/Location/Details/{conPhungLocation.Id}"
                    },
                    new
                    {
                        pitch = -10,
                        yaw = 150,
                        type = "info",
                        text = "Khu vực sông nước"
                    }
                };

                panoramas.Add(new Panorama360
                {
                    Name = "Cồn Phụng - Toàn cảnh",
                    Description = "Khám phá toàn cảnh Cồn Phụng với góc nhìn 360 độ, nơi có khu di tích Đạo Dừa và cảnh quan sông nước tuyệt đẹp.",
                    ImageUrl = "/images/panoramas/con-phung-360.jpg",
                    Type = "Location",
                    LocationId = conPhungLocation.Id,
                    IsActive = true,
                    HotspotData = JsonSerializer.Serialize(hotspotData),
                    DisplayOrder = 1,
                    CreatedAt = DateTime.Now
                });
            }

            // Panorama cho Làng nghề dừa
            if (langNgheDuaLocation != null)
            {
                var hotspotData = new List<object>
                {
                    new
                    {
                        pitch = 0,
                        yaw = 45,
                        type = "info",
                        text = "Khu vực chế biến dừa"
                    },
                    new
                    {
                        pitch = -5,
                        yaw = 180,
                        type = "info",
                        text = "Cửa hàng sản phẩm từ dừa"
                    }
                };

                panoramas.Add(new Panorama360
                {
                    Name = "Làng nghề dừa - Khu sản xuất",
                    Description = "Tham quan khu vực sản xuất các sản phẩm từ dừa tại làng nghề truyền thống Bến Tre.",
                    ImageUrl = "/images/panoramas/lang-nghe-dua-360.jpg",
                    Type = "Location",
                    LocationId = langNgheDuaLocation.Id,
                    IsActive = true,
                    HotspotData = JsonSerializer.Serialize(hotspotData),
                    DisplayOrder = 1,
                    CreatedAt = DateTime.Now
                });
            }

            // Panorama cho Tour Bến Tre
            if (benTreTour != null)
            {
                var hotspotData = new List<object>
                {
                    new
                    {
                        pitch = 0,
                        yaw = 90,
                        type = "info",
                        text = "Khu vực vườn trái cây"
                    },
                    new
                    {
                        pitch = -10,
                        yaw = 270,
                        type = "info",
                        text = "Đường sông Bến Tre"
                    }
                };

                panoramas.Add(new Panorama360
                {
                    Name = "Tour Bến Tre - Điểm dừng chân",
                    Description = "Khám phá một trong những điểm dừng chân thú vị trong tour khám phá miệt vườn Bến Tre.",
                    ImageUrl = "/images/panoramas/tour-ben-tre-360.jpg",
                    Type = "Tour",
                    TourId = benTreTour.Id,
                    IsActive = true,
                    HotspotData = JsonSerializer.Serialize(hotspotData),
                    DisplayOrder = 1,
                    CreatedAt = DateTime.Now
                });
            }

            // Thêm panorama vào cơ sở dữ liệu
            context.Panorama360s.AddRange(panoramas);
            context.SaveChanges();
        }
    }
}
