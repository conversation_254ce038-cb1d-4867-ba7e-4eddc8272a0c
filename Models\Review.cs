﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class Review
    {
        public int Id { get; set; }

        public int? AccommodationId { get; set; }

        public int? TourId { get; set; }

        public int? ServiceId { get; set; }

        public int? VehicleId { get; set; }

        public int? LocationId { get; set; }

        public string UserId { get; set; }

        [Required]
        [Range(1, 5)]
        public int Rating { get; set; }

        [Required]
        public string Comment { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [ForeignKey("UserId")]
        public ApplicationUser User { get; set; }

        [ForeignKey("AccommodationId")]
        public Accommodation? Accommodation { get; set; }

        [ForeignKey("TourId")]
        public Tour? Tour { get; set; }

        [ForeignKey("ServiceId")]
        public Service? Service { get; set; }

        [ForeignKey("VehicleId")]
        public Vehicle? Vehicle { get; set; }

        [ForeignKey("LocationId")]
        public Location? Location { get; set; }

        [NotMapped]
        public ReviewType Type
        {
            get
            {
                if (AccommodationId.HasValue) return ReviewType.Accommodation;
                if (TourId.HasValue) return ReviewType.Tour;
                if (ServiceId.HasValue) return ReviewType.Service;
                if (VehicleId.HasValue) return ReviewType.Vehicle;
                return ReviewType.Location;
            }
        }
    }

    public enum ReviewType
    {
        Accommodation,
        Tour,
        Service,
        Vehicle,
        Location
    }
}
