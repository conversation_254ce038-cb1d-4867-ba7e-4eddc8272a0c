﻿﻿﻿using ViVu.Models;

namespace ViVu.Services
{
    public interface IRecommendationService
    {
        // Generate recommendations based on user preferences
        Task<List<TravelRecommendation>> GenerateRecommendationsAsync(UserPreference preferences, string? userId = null);

        // Get user preferences (create if not exists)
        Task<UserPreference> GetUserPreferencesAsync(string userId);

        // Save user preferences
        Task SaveUserPreferencesAsync(UserPreference preferences);

        // Track search history
        Task TrackSearchHistoryAsync(SearchHistory searchHistory);

        // Get user's search history
        Task<List<SearchHistory>> GetUserSearchHistoryAsync(string userId, int count = 10);

        // Get popular locations based on search history
        Task<List<Location>> GetPopularLocationsAsync(int count = 5);

        // Get related items for a recommendation
        Task<Dictionary<int, Tour>> GetRelatedToursAsync(string tourIds);
        Task<Dictionary<int, Accommodation>> GetRelatedAccommodationsAsync(string accommodationIds);
        Task<Dictionary<int, Service>> GetRelatedServicesAsync(string serviceIds);
        Task<Dictionary<int, Location>> GetRelatedLocationsAsync(string locationIds);
        Task<Dictionary<int, Vehicle>> GetRelatedVehiclesAsync(string vehicleIds);

        // Get user's recommendation statistics
        Task<(int total, int saved, int viewed)> GetUserRecommendationStatsAsync(string userId);

        // Get similar recommendations
        Task<List<TravelRecommendation>> GetSimilarRecommendationsAsync(int recommendationId, string? userId = null, int count = 3);

        // Update recommendation view statistics
        Task UpdateRecommendationViewStatsAsync(int recommendationId, string? userId = null);

        // Get user's top interests based on search history
        Task<Dictionary<string, int>> GetUserTopInterestsAsync(string userId, int count = 5);
    }
}
