@model ViVu.Models.Location

@{
    ViewData["Title"] = "Xem phố - " + Model.Name;
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ViVu</title>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        #street-view {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="street-view"></div>

    <script src="/js/googleMapsConfig.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAMqvsRr2toGUxy7yF_HBNuNCifHE3S1UU&callback=initStreetViewPage" async defer></script>
    <script>
        function initStreetViewPage() {
            @if (ViewBag.DefaultLatitude != null && ViewBag.DefaultLongitude != null)
            {
                <text>
                const lat = @ViewBag.DefaultLatitude;
                const lng = @ViewBag.DefaultLongitude;
                </text>
            }
            else
            {
                <text>
                const lat = 10.2433; // Tọa độ mặc định của Bến Tre nếu không có tọa độ
                const lng = 106.3756;
                </text>
            }

            // Kiểm tra xem có Street View tại vị trí này không
            checkStreetViewAvailability(lat, lng, function(available) {
                if (available) {
                    // Nếu có, hiển thị Street View
                    initStreetView('street-view', lat, lng);
                } else {
                    // Nếu không, hiển thị thông báo và bản đồ thông thường
                    document.getElementById('street-view').innerHTML =
                        '<div class="alert alert-warning text-center p-3 mb-3">' +
                        '<i class="bi bi-exclamation-triangle-fill me-2"></i> ' +
                        'Chế độ xem phố không khả dụng tại vị trí này. Đang hiển thị bản đồ thông thường.' +
                        '</div>' +
                        '<div id="map" style="width: 100%; height: calc(100% - 80px);"></div>';

                    // Hiển thị bản đồ thông thường
                    const map = initMap('map', lat, lng);
                    addMarker(map, lat, lng, '@Model.Name', '<strong>@Model.Name</strong><br>@(string.IsNullOrEmpty(Model.Address) ? Model.City?.Name : Model.Address)');
                }
            });
        }
    </script>
</body>
</html>
