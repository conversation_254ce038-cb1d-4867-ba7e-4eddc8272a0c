@{
    ViewData["Title"] = "Khám phá Bến Tre - Xứ Dừa";
}
@using System.Linq
@using ViVu.Models.ViewModels

<div class="container py-5">
    <h1 class="text-center mb-5">Khám phá Bến Tre - Xứ Dừa</h1>

    <!-- Giớ<PERSON> thiệu tổng quan -->
    <section class="mb-5">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <img src="@ViewBag.BenTreInfo.ImageUrl" class="img-fluid rounded shadow" alt="Bến Tre - Xứ Dừa">
            </div>
            <div class="col-lg-6">
                <h2 class="mb-3">Tổng quan về Bến Tre</h2>
                <p>@ViewBag.BenTreInfo.Description</p>
                <div class="row mt-4">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-geo-alt-fill text-primary me-2" style="font-size: 1.5rem;"></i>
                            <div>
                                <h6 class="mb-0">Diện tích</h6>
                                <p class="mb-0">@ViewBag.BenTreInfo.Area</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-people-fill text-primary me-2" style="font-size: 1.5rem;"></i>
                            <div>
                                <h6 class="mb-0">Dân số</h6>
                                <p class="mb-0">@ViewBag.BenTreInfo.Population</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-building text-primary me-2" style="font-size: 1.5rem;"></i>
                            <div>
                                <h6 class="mb-0">Đơn vị hành chính</h6>
                                <p class="mb-0">@ViewBag.BenTreInfo.Districts</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-sun text-primary me-2" style="font-size: 1.5rem;"></i>
                            <div>
                                <h6 class="mb-0">Khí hậu</h6>
                                <p class="mb-0">@ViewBag.BenTreInfo.Climate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lịch sử Bến Tre -->
    <section class="mb-5">
        <h2 class="text-center mb-4">Lịch sử Bến Tre</h2>
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Lịch sử hình thành</h3>
                        <p>@ViewBag.History.Formation</p>
                        <div class="mt-3">
                            <a href="@Url.Action("ViewByCoordinates", "StreetView", new { lat = 10.2433, lng = 106.3756, name = "Thành phố Bến Tre - Trung tâm lịch sử" })" class="btn btn-outline-success">
                                <i class="bi bi-street-view me-1"></i> Xem phố 360° tại trung tâm lịch sử
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Truyền thống cách mạng</h3>
                        <p>@ViewBag.History.RevolutionaryTradition</p>
                        <div class="mt-3">
                            <a href="@Url.Action("ViewByCoordinates", "StreetView", new { lat = 10.2352, lng = 106.3845, name = "Khu di tích Đồng Khởi" })" class="btn btn-outline-success">
                                <i class="bi bi-street-view me-1"></i> Xem phố 360° tại khu di tích
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Phát triển kinh tế</h3>
                        <p>@ViewBag.History.EconomicDevelopment</p>
                        <div class="mt-3">
                            <a href="@Url.Action("ViewByCoordinates", "StreetView", new { lat = 10.2371, lng = 106.3760, name = "Khu công nghiệp Bến Tre" })" class="btn btn-outline-success">
                                <i class="bi bi-street-view me-1"></i> Xem phố 360° tại khu công nghiệp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Du lịch 360° -->
    <section class="mb-5">
        <h2 class="text-center mb-4">Du lịch 360° - Khám phá Bến Tre</h2>

        @{
            var panoramas = ViewBag.BenTrePanoramas as IEnumerable<ViVu.Models.Panorama360>;
        }

        @if (panoramas != null && panoramas.Any())
        {
            <div class="row">
                @foreach (var panorama in panoramas.Take(3))
                {
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <img src="@panorama.ImageUrl" class="card-img-top" alt="@panorama.Name" style="height: 200px; object-fit: cover;">
                            <div class="card-body">
                                <h5 class="card-title">@panorama.Name</h5>
                                <p class="card-text text-truncate">@panorama.Description</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-info">@panorama.Type</span>
                                    <a asp-controller="Panorama360" asp-action="View" asp-route-id="@panorama.Id" class="btn btn-outline-primary">
                                        <i class="bi bi-arrows-fullscreen me-1"></i> Xem 360°
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
            <div class="text-center mt-3">
                <a asp-controller="Panorama360" asp-action="Index" class="btn btn-primary">Xem tất cả panorama 360°</a>
                <a asp-controller="Panorama360" asp-action="Guide" class="btn btn-outline-info ms-2">
                    <i class="bi bi-info-circle me-1"></i> Hướng dẫn sử dụng
                </a>
            </div>
        }
        else
        {
            <div class="alert alert-info text-center">
                <p class="mb-0">Hiện chưa có panorama 360° cho Bến Tre.</p>
            </div>
        }
    </section>

    <!-- Điểm du lịch nổi bật -->
    <section class="mb-5">
        <h2 class="text-center mb-4">Điểm du lịch nổi bật</h2>

        @{
            var highlights = ViewBag.TourismHighlights as IEnumerable<TourismHighlight>;
        }
        @if (highlights != null && highlights.Any())
        {
            var count = 0;
            foreach (var highlight in highlights)
            {
                if (count % 2 == 0)
                {
                    <!-- Hiển thị hình ảnh bên trái, nội dung bên phải -->
                    <div class="card mb-4 border-0 shadow-sm overflow-hidden">
                        <div class="row g-0">
                            <div class="col-md-4">
                                <img src="@highlight.ImageUrl" class="img-fluid h-100" style="object-fit: cover;" alt="@highlight.Name">
                            </div>
                            <div class="col-md-8">
                                <div class="card-body p-4">
                                    <h3 class="card-title">@highlight.Name</h3>
                                    <p class="card-text">@highlight.Description</p>
                                    <div class="mb-3">
                                        <h6 class="mb-2">Hoạt động nổi bật:</h6>
                                        <p class="card-text"><i class="bi bi-check-circle-fill text-primary me-2"></i>@highlight.Activities</p>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="#" class="btn btn-outline-primary">Xem chi tiết</a>
                                        <a href="@Url.Action("ViewByCoordinates", "StreetView", new { lat = highlight.Latitude, lng = highlight.Longitude, name = highlight.Name })" class="btn btn-outline-success">
                                            <i class="bi bi-street-view me-1"></i> Phố 360°
                                        </a>
                                        @{
                                            var locationPanorama = panoramas?.FirstOrDefault(p => p.Type == "Location" && 
                                                (p.Location?.Name?.Contains(highlight.Name) == true || 
                                                 p.Name?.Contains(highlight.Name) == true));
                                        }
                                        @if (locationPanorama != null)
                                        {
                                            <a asp-controller="Panorama360" asp-action="View" asp-route-id="@locationPanorama.Id" class="btn btn-outline-info">
                                                <i class="bi bi-arrows-fullscreen me-1"></i> Du lịch 360°
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <!-- Hiển thị nội dung bên trái, hình ảnh bên phải -->
                    <div class="card mb-4 border-0 shadow-sm overflow-hidden">
                        <div class="row g-0">
                            <div class="col-md-8">
                                <div class="card-body p-4">
                                    <h3 class="card-title">@highlight.Name</h3>
                                    <p class="card-text">@highlight.Description</p>
                                    <div class="mb-3">
                                        <h6 class="mb-2">Hoạt động nổi bật:</h6>
                                        <p class="card-text"><i class="bi bi-check-circle-fill text-primary me-2"></i>@highlight.Activities</p>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="#" class="btn btn-outline-primary">Xem chi tiết</a>
                                        <a href="@Url.Action("ViewByCoordinates", "StreetView", new { lat = highlight.Latitude, lng = highlight.Longitude, name = highlight.Name })" class="btn btn-outline-success">
                                            <i class="bi bi-street-view me-1"></i> Phố 360°
                                        </a>
                                        @{
                                            var locationPanorama = panoramas?.FirstOrDefault(p => p.Type == "Location" && 
                                                (p.Location?.Name?.Contains(highlight.Name) == true || 
                                                 p.Name?.Contains(highlight.Name) == true));
                                        }
                                        @if (locationPanorama != null)
                                        {
                                            <a asp-controller="Panorama360" asp-action="View" asp-route-id="@locationPanorama.Id" class="btn btn-outline-info">
                                                <i class="bi bi-arrows-fullscreen me-1"></i> Du lịch 360°
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <img src="@highlight.ImageUrl" class="img-fluid h-100" style="object-fit: cover;" alt="@highlight.Name">
                            </div>
                        </div>
                    </div>
                }
                count++;
            }
        }
        else
        {
            <div class="alert alert-info text-center">
                <p class="mb-0">Hiện chưa có thông tin về các điểm du lịch nổi bật ở Bến Tre.</p>
            </div>
        }
    </section>

    <!-- Văn hóa và ẩm thực -->
    <section class="mb-5">
        <h2 class="text-center mb-4">Văn hóa và ẩm thực</h2>
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="@ViewBag.CultureAndCuisine.Culture.ImageUrl" class="card-img-top" style="height: 250px; object-fit: cover;" alt="Đờn ca tài tử">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Văn hóa</h3>
                        <p>@ViewBag.CultureAndCuisine.Culture.Description</p>
                        <div class="mt-3">
                            <h6 class="mb-2">Lễ hội truyền thống:</h6>
                            <p><i class="bi bi-calendar-event text-primary me-2"></i>@ViewBag.CultureAndCuisine.Culture.Festivals</p>

                            <h6 class="mb-2">Nghệ thuật dân gian:</h6>
                            <p><i class="bi bi-music-note-beamed text-primary me-2"></i>@ViewBag.CultureAndCuisine.Culture.TraditionalArts</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="@ViewBag.CultureAndCuisine.Cuisine.ImageUrl" class="card-img-top" style="height: 250px; object-fit: cover;" alt="Ẩm thực Bến Tre">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Ẩm thực</h3>
                        <p>@ViewBag.CultureAndCuisine.Cuisine.Description</p>
                        <div class="mt-3">
                            <h6 class="mb-2">Món ăn đặc sản:</h6>
                            <p><i class="bi bi-cup-hot text-primary me-2"></i>@ViewBag.CultureAndCuisine.Cuisine.FamousDishes</p>

                            <h6 class="mb-2">Bánh truyền thống:</h6>
                            <p><i class="bi bi-basket text-primary me-2"></i>@ViewBag.CultureAndCuisine.Cuisine.TraditionalCakes</p>

                            <h6 class="mb-2">Trái cây đặc sản:</h6>
                            <p><i class="bi bi-tree text-primary me-2"></i>@ViewBag.CultureAndCuisine.Cuisine.Fruits</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mùa du lịch và lời khuyên -->
    <section class="mb-5">
        <h2 class="text-center mb-4">Mùa du lịch và lời khuyên</h2>
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Thời điểm lý tưởng để du lịch</h3>
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-calendar-check text-primary me-3" style="font-size: 2rem;"></i>
                            <div>
                                <h6 class="mb-1">Thời điểm tốt nhất:</h6>
                                <p class="mb-0">@ViewBag.BenTreInfo.BestTimeToVisit</p>
                            </div>
                        </div>
                        <p>Bến Tre có khí hậu nhiệt đới gió mùa, nóng ẩm quanh năm. Thời điểm lý tưởng nhất để du lịch Bến Tre là từ tháng 12 đến tháng 4 năm sau, khi thời tiết mát mẻ, ít mưa.</p>
                        <p>Nếu muốn tham gia Lễ hội Dừa Bến Tre - một trong những sự kiện văn hóa lớn nhất của tỉnh, du khách nên đến vào khoảng tháng 2 hoặc tháng 3 (tùy theo lịch tổ chức hàng năm).</p>
                        <p>Mùa trái cây ở Bến Tre thường rơi vào khoảng tháng 5 đến tháng 8, đây cũng là thời điểm thích hợp để thưởng thức các loại trái cây nhiệt đới tươi ngon.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-3">Lời khuyên cho du khách</h3>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Nên chuẩn bị quần áo thoáng mát, mũ, kem chống nắng khi du lịch Bến Tre
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Mang theo thuốc chống côn trùng, đặc biệt khi tham quan các khu vực sông nước
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Nên đi tour có hướng dẫn viên để hiểu rõ hơn về văn hóa, lịch sử địa phương
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Chuẩn bị tiền mặt vì một số điểm du lịch nhỏ có thể không chấp nhận thẻ
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Nên thuê xe máy hoặc xe đạp để khám phá các làng quê và vườn trái cây
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <i class="bi bi-check-circle-fill text-primary me-2"></i>
                                Tôn trọng phong tục, tập quán của người dân địa phương
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Các tour du lịch Bến Tre -->
    <section class="mt-5">
        <h2 class="text-center mb-4">Tour du lịch Bến Tre</h2>

        @{
            var tours = ViewBag.BenTreTours as IEnumerable<ViVu.Models.Tour>;
        }
        @if (tours != null && tours.Any())
        {
            var displayTours = tours.Take(3).ToList();
            <div class="row">
                @foreach (var tour in displayTours)
                {
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <img src="@tour.ImageUrl" class="card-img-top" alt="@tour.Name" style="height: 200px; object-fit: cover;">
                            <div class="card-body">
                                <h5 class="card-title">@tour.Name</h5>
                                <p class="card-text text-truncate">@tour.Description</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-primary fw-bold">@tour.Price.ToString("N0") VNĐ</span>
                                    <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-outline-primary">Chi tiết</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
            <div class="text-center mt-3">
                <a asp-controller="Tour" asp-action="Index" class="btn btn-primary">Xem tất cả tour</a>
            </div>
        }
        else
        {
            <div class="alert alert-info text-center">
                <p class="mb-0">Hiện chưa có tour du lịch Bến Tre.</p>
            </div>
        }
    </section>

    <!-- Khách sạn ở Bến Tre -->
    <section class="mt-5">
        <h2 class="text-center mb-4">Khách sạn ở Bến Tre</h2>

        @{
            var accommodations = ViewBag.BenTreAccommodations as IEnumerable<ViVu.Models.Accommodation>;
        }
        @if (accommodations != null && accommodations.Any())
        {
            var displayAccommodations = accommodations.Take(3).ToList();
            <div class="row">
                @foreach (var accommodation in displayAccommodations)
                {
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            @if (accommodation.Images != null && accommodation.Images.Any())
                            {
                                <img src="@accommodation.Images.First().Url" class="card-img-top" alt="@accommodation.Name" style="height: 200px; object-fit: cover;">
                            }
                            else
                            {
                                <img src="/images/no-image.jpg" class="card-img-top" alt="@accommodation.Name" style="height: 200px; object-fit: cover;">
                            }
                            <div class="card-body">
                                <h5 class="card-title">@accommodation.Name</h5>
                                <p class="card-text text-truncate">@accommodation.Description</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-primary fw-bold">Từ @(accommodation.Rooms != null && accommodation.Rooms.Any() ? accommodation.Rooms.Min(r => r.PricePerNight).ToString("N0") : "0") VNĐ</span>
                                    <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-outline-primary">Chi tiết</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
            <div class="text-center mt-3">
                <a asp-controller="Accommodation" asp-action="Index" class="btn btn-primary">Xem tất cả khách sạn</a>
            </div>
        }
        else
        {
            <div class="alert alert-info text-center">
                <p class="mb-0">Hiện chưa có thông tin khách sạn ở Bến Tre.</p>
            </div>
        }
    </section>
</div>
