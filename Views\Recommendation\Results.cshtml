@model ViVu.Models.RecommendationViewModel
@using Markdig

@{
    ViewData["Title"] = "Gợi Ý Lịch Trình Du Lịch";

    // Hàm chuyển đổi Markdown sang HTML
    string MarkdownToHtml(string markdown)
    {
        if (string.IsNullOrEmpty(markdown))
            return "";

        var pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions()
            .Build();

        return Markdown.ToHtml(markdown, pipeline);
    }
}

<div class="recommendation-results-container">
    <div class="container-fluid px-3 px-md-5 py-4">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a asp-controller="Recommendation" asp-action="Index">AI Gợi Ý Lịch Trình</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Kết quả gợi ý</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="bg-primary text-white p-4 rounded-3">
                    <h1 class="display-5 fw-bold">Gợi Ý Lịch Trình Du Lịch Bến Tre</h1>
                    <p class="fs-5">Dựa trên sở thích của bạn, chúng tôi đã tạo ra các gợi ý lịch trình du lịch phù hợp nhất!</p>
                </div>
            </div>
        </div>

        @if (Model.Recommendations != null && Model.Recommendations.Any())
        {
            <div class="recommendation-tabs">
                <a href="#preferences" class="recommendation-tab" data-target="#preferences-section">
                    <i class="fas fa-sliders-h me-2"></i>Sở thích của bạn
                </a>
                @foreach (var recommendation in Model.Recommendations)
                {
                    <a href="#<EMAIL>" class="recommendation-tab" data-target="#<EMAIL>">
                        <i class="fas fa-@(recommendation.RecommendationSource == "AI" ? "robot" : "map-marked-alt") me-2"></i>@recommendation.Title
                    </a>
                }
            </div>

            <div class="recommendation-content">
                <div id="preferences-section" class="recommendation-tab-content">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h4 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Sở thích của bạn</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Loại hình du lịch</h5>
                                            @{
                                                var travelTypes = new List<string>();
                                                if (Model.UserPreference.PrefersTours) travelTypes.Add("Tour có hướng dẫn");
                                                if (Model.UserPreference.PrefersIndependentTravel) travelTypes.Add("Du lịch tự túc");
                                            }
                                            <p class="mb-0">@(travelTypes.Any() ? string.Join(", ", travelTypes) : "Không xác định")</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Ngân sách</h5>
                                            @if (Model.UserPreference.MinBudget.HasValue && Model.UserPreference.MaxBudget.HasValue)
                                            {
                                                <p class="mb-0">@(Model.UserPreference.MinBudget?.ToString("N0")) - @(Model.UserPreference.MaxBudget?.ToString("N0")) VNĐ</p>
                                            }
                                            else
                                            {
                                                <p class="mb-0">Không xác định</p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Thời gian</h5>
                                            @if (Model.UserPreference.MinDuration.HasValue && Model.UserPreference.MaxDuration.HasValue)
                                            {
                                                <p class="mb-0">@(Model.UserPreference.MinDuration) - @(Model.UserPreference.MaxDuration) ngày</p>
                                            }
                                            else
                                            {
                                                <p class="mb-0">Không xác định</p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-heart"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Sở thích</h5>
                                            @{
                                                var preferences = new List<string>();
                                                if (Model.UserPreference.PrefersNature) preferences.Add("Thiên nhiên");
                                                if (Model.UserPreference.PrefersHistory) preferences.Add("Lịch sử, văn hóa");
                                                if (Model.UserPreference.PrefersFood) preferences.Add("Ẩm thực");
                                                if (Model.UserPreference.PrefersAdventure) preferences.Add("Mạo hiểm");
                                                if (Model.UserPreference.PrefersRelaxation) preferences.Add("Thư giãn");
                                            }
                                            <p class="mb-0">@(preferences.Any() ? string.Join(", ", preferences) : "Không có")</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-hiking"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Hoạt động quan tâm</h5>
                                            @{
                                                var activities = new List<string>();
                                                if (Model.UserPreference.InterestedInCooking) activities.Add("Nấu ăn");
                                                if (Model.UserPreference.InterestedInCrafts) activities.Add("Làm thủ công");
                                                if (Model.UserPreference.InterestedInFarming) activities.Add("Trải nghiệm nông trại");
                                                if (Model.UserPreference.InterestedInBoating) activities.Add("Đi thuyền");
                                                if (Model.UserPreference.InterestedInCycling) activities.Add("Đạp xe");
                                                if (Model.UserPreference.InterestedInFishing) activities.Add("Câu cá");
                                            }
                                            <p class="mb-0">@(activities.Any() ? string.Join(", ", activities) : "Không có")</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Đi cùng</h5>
                                            @{
                                                var companions = new List<string>();
                                                if (Model.UserPreference.TravelingAlone) companions.Add("Đi một mình");
                                                if (Model.UserPreference.TravelingAsCouple) companions.Add("Đi cùng người yêu/vợ chồng");
                                                if (Model.UserPreference.TravelingWithFriends) companions.Add("Đi cùng bạn bè");
                                                if (Model.UserPreference.TravelingWithChildren) companions.Add("Đi cùng trẻ em");
                                                if (Model.UserPreference.TravelingWithElders) companions.Add("Đi cùng người lớn tuổi");
                                            }
                                            <p class="mb-0">@(companions.Any() ? string.Join(", ", companions) : "Không xác định")</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <a asp-action="Index" class="btn btn-primary">
                                    <i class="fas fa-edit me-2"></i>Thay đổi sở thích
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                @foreach (var recommendation in Model.Recommendations)
                {
                    <div id="<EMAIL>" class="recommendation-tab-content">
                        @if (recommendation.RecommendationSource == "AI")
                        {
                            <partial name="_AIRecommendation" model="recommendation" />
                        }
                        else
                        {
                            <div id="<EMAIL>" class="recommendation-card animate-on-scroll">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">@recommendation.Title</h3>
                                        <span class="badge bg-primary">@recommendation.RecommendationType</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p class="lead">@recommendation.Description</p>

                                    <div class="row mb-4">
                                        <div class="col-md-4 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="feature-icon">
                                                    <i class="fas fa-calendar-alt"></i>
                                                </div>
                                                <div class="ms-3">
                                                    <small class="text-muted d-block">Thời gian đề xuất</small>
                                                    <strong>@recommendation.RecommendedDuration ngày</strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="feature-icon">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </div>
                                                <div class="ms-3">
                                                    <small class="text-muted d-block">Ngân sách dự kiến</small>
                                                    <strong>@recommendation.EstimatedMinBudget?.ToString("N0") - @recommendation.EstimatedMaxBudget?.ToString("N0") VNĐ</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <ul class="nav nav-tabs mb-4" id="<EMAIL>" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="itinerary" aria-selected="true">
                                                <i class="fas fa-route me-2"></i>Lịch trình
                                            </button>
                                        </li>
                                        @if (!string.IsNullOrEmpty(recommendation.HighlightsSection))
                                        {
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="highlights" aria-selected="false">
                                                    <i class="fas fa-star me-2"></i>Điểm nổi bật
                                                </button>
                                            </li>
                                        }
                                        @if (!string.IsNullOrEmpty(recommendation.DayByDaySection))
                                        {
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="dayByDay" aria-selected="false">
                                                    <i class="fas fa-calendar-day me-2"></i>Theo ngày
                                                </button>
                                            </li>
                                        }
                                        @if (!string.IsNullOrEmpty(recommendation.TipsSection))
                                        {
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="tips" aria-selected="false">
                                                    <i class="fas fa-lightbulb me-2"></i>Mẹo du lịch
                                                </button>
                                            </li>
                                        }
                                        @if (!string.IsNullOrEmpty(recommendation.BudgetBreakdownSection))
                                        {
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="budget" aria-selected="false">
                                                    <i class="fas fa-money-bill-wave me-2"></i>Chi phí
                                                </button>
                                            </li>
                                        }
                                    </ul>

                                    <div class="tab-content" id="<EMAIL>">
                                        <div class="tab-pane fade show active" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                            <div class="card border-0 shadow-sm">
                                                <div class="card-body">
                                                    @Html.Raw(MarkdownToHtml(recommendation.ItineraryDetails))
                                                </div>
                                            </div>
                                        </div>

                            @if (!string.IsNullOrEmpty(recommendation.HighlightsSection))
                            {
                                <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            @Html.Raw(MarkdownToHtml(recommendation.HighlightsSection))
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(recommendation.DayByDaySection))
                            {
                                <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            @Html.Raw(MarkdownToHtml(recommendation.DayByDaySection))
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(recommendation.TipsSection))
                            {
                                <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            @Html.Raw(MarkdownToHtml(recommendation.TipsSection))
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(recommendation.BudgetBreakdownSection))
                            {
                                <div class="tab-pane fade" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            @Html.Raw(MarkdownToHtml(recommendation.BudgetBreakdownSection))
                                        </div>
                                    </div>
                                </div>
                            }

                            <div class="mt-5">
                                <ul class="nav nav-pills mb-4" id="<EMAIL>" role="tablist">
                                    @if (!string.IsNullOrEmpty(recommendation.RelatedTourIds) && Model.RelatedTours != null && Model.RelatedTours.Any())
                                    {
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="tours" aria-selected="true">
                                                <i class="fas fa-map-marked-alt me-2"></i>Tour đề xuất
                                            </button>
                                        </li>
                                    }
                                    @if (!string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && Model.RelatedAccommodations != null && Model.RelatedAccommodations.Any())
                                    {
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link @(string.IsNullOrEmpty(recommendation.RelatedTourIds) || !Model.RelatedTours.Any() ? "active" : "")" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="accommodations" aria-selected="@(string.IsNullOrEmpty(recommendation.RelatedTourIds) || !Model.RelatedTours.Any() ? "true" : "false")">
                                                <i class="fas fa-hotel me-2"></i>Khách sạn đề xuất
                                            </button>
                                        </li>
                                    }
                                    @if (!string.IsNullOrEmpty(recommendation.RelatedLocationIds) && Model.RelatedLocations != null && Model.RelatedLocations.Any())
                                    {
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link @(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) ? "active" : "")" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="locations" aria-selected="@(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) ? "true" : "false")">
                                                <i class="fas fa-map-marker-alt me-2"></i>Địa điểm đề xuất
                                            </button>
                                        </li>
                                    }
                                    @if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds) && Model.RelatedVehicles != null && Model.RelatedVehicles.Any())
                                    {
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link @(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && string.IsNullOrEmpty(recommendation.RelatedLocationIds) ? "active" : "")" id="<EMAIL>" data-bs-toggle="tab" data-bs-target="#<EMAIL>" type="button" role="tab" aria-controls="vehicles" aria-selected="@(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && string.IsNullOrEmpty(recommendation.RelatedLocationIds) ? "true" : "false")">
                                                <i class="fas fa-car me-2"></i>Phương tiện đề xuất
                                            </button>
                                        </li>
                                    }
                                </ul>

                                <div class="tab-content" id="<EMAIL>">
                                    @if (!string.IsNullOrEmpty(recommendation.RelatedTourIds) && Model.RelatedTours != null && Model.RelatedTours.Any())
                                    {
                                        <div class="tab-pane fade show active" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                            <div class="row">
                                                @foreach (var tourId in recommendation.RelatedTourIds.Split(','))
                                                {
                                                    if (int.TryParse(tourId, out int id) && Model.RelatedTours.ContainsKey(id))
                                                    {
                                                        var tour = Model.RelatedTours[id];
                                                        <div class="col-md-6 mb-3">
                                                            <div class="card h-100 hover-lift">
                                                                <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/no-image.jpg" : tour.ImageUrl)"
                                                                     class="card-img-top" alt="@tour.Name" style="height: 150px; object-fit: cover;">
                                                                <div class="card-body">
                                                                    <h5 class="card-title">@tour.Name</h5>
                                                                    <p class="card-text text-truncate">@tour.Description</p>
                                                                    <p class="card-text">
                                                                        <small class="text-muted">
                                                                            <i class="fas fa-map-marker-alt"></i> @tour.Location?.Name
                                                                        </small>
                                                                    </p>
                                                                    <p class="card-text">
                                                                        <span class="text-primary fw-bold">@tour.Price.ToString("N0") VNĐ</span>
                                                                    </p>
                                                                    <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id"
                                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    }

                                    @if (!string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && Model.RelatedAccommodations != null && Model.RelatedAccommodations.Any())
                                    {
                                        <div class="tab-pane fade @(string.IsNullOrEmpty(recommendation.RelatedTourIds) || !Model.RelatedTours.Any() ? "show active" : "")" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                            <div class="row">
                                                @foreach (var accommodationId in recommendation.RelatedAccommodationIds.Split(','))
                                                {
                                                    if (int.TryParse(accommodationId, out int id) && Model.RelatedAccommodations.ContainsKey(id))
                                                    {
                                                        var accommodation = Model.RelatedAccommodations[id];
                                                        <div class="col-md-6 mb-3">
                                                            <div class="card h-100 hover-lift">
                                                                <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/no-image.jpg" : accommodation.ImageUrl)"
                                                                     class="card-img-top" alt="@accommodation.Name" style="height: 150px; object-fit: cover;">
                                                                <div class="card-body">
                                                                    <h5 class="card-title">@accommodation.Name</h5>
                                                                    <p class="card-text text-truncate">@accommodation.Description</p>
                                                                    <p class="card-text">
                                                                        <small class="text-muted">
                                                                            <i class="fas fa-map-marker-alt"></i> @accommodation.Location?.Name
                                                                        </small>
                                                                    </p>
                                                                    <div class="mb-2">
                                                                        @for (int i = 0; i < accommodation.StarRating; i++)
                                                                        {
                                                                            <i class="fas fa-star text-warning"></i>
                                                                        }
                                                                    </div>
                                                                    <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id"
                                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    }

                                    @if (!string.IsNullOrEmpty(recommendation.RelatedLocationIds) && Model.RelatedLocations != null && Model.RelatedLocations.Any())
                                    {
                                        <div class="tab-pane fade @(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) ? "show active" : "")" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                            <div class="row">
                                                @foreach (var locationId in recommendation.RelatedLocationIds.Split(','))
                                                {
                                                    if (int.TryParse(locationId, out int id) && Model.RelatedLocations.ContainsKey(id))
                                                    {
                                                        var location = Model.RelatedLocations[id];
                                                        <div class="col-md-6 mb-3">
                                                            <div class="card h-100 hover-lift">
                                                                @if (!string.IsNullOrEmpty(location.ImageUrl))
                                                                {
                                                                    <img src="@location.ImageUrl" class="card-img-top" alt="@location.Name" style="height: 150px; object-fit: cover;">
                                                                }
                                                                <div class="card-body">
                                                                    <h5 class="card-title">@location.Name</h5>
                                                                    <p class="card-text">
                                                                        <small class="text-muted">
                                                                            <i class="fas fa-city"></i> @location.City?.Name
                                                                        </small>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    }

                                    @if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds) && Model.RelatedVehicles != null && Model.RelatedVehicles.Any())
                                    {
                                        <div class="tab-pane fade @(string.IsNullOrEmpty(recommendation.RelatedTourIds) && string.IsNullOrEmpty(recommendation.RelatedAccommodationIds) && string.IsNullOrEmpty(recommendation.RelatedLocationIds) ? "show active" : "")" id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>">
                                            <div class="row">
                                                @foreach (var vehicleId in recommendation.RelatedVehicleIds.Split(','))
                                                {
                                                    if (int.TryParse(vehicleId, out int id) && Model.RelatedVehicles.ContainsKey(id))
                                                    {
                                                        var vehicle = Model.RelatedVehicles[id];
                                                        <div class="col-md-6 mb-3">
                                                            <div class="card h-100 hover-lift">
                                                                @if (!string.IsNullOrEmpty(vehicle.ImageUrl))
                                                                {
                                                                    <img src="@vehicle.ImageUrl" class="card-img-top" alt="@vehicle.Name" style="height: 150px; object-fit: cover;">
                                                                }
                                                                <div class="card-body">
                                                                    <h5 class="card-title">@vehicle.Name</h5>
                                                                    <p class="card-text text-truncate">@vehicle.Description</p>
                                                                    <p class="card-text">
                                                                        <small class="text-muted">
                                                                            <i class="fas fa-map-marker-alt"></i> @vehicle.Location?.Name
                                                                        </small>
                                                                    </p>
                                                                    <p class="card-text">
                                                                        <span class="text-primary fw-bold">@vehicle.RentalPrice.ToString("N0") VNĐ</span>
                                                                    </p>
                                                                    <a asp-controller="Vehicle" asp-action="Details" asp-route-id="@vehicle.Id"
                                                                       class="btn btn-sm btn-outline-primary">Xem chi tiết</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-5">
                                <a asp-action="Index" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> Quay lại
                                </a>

                                @if (User.Identity.IsAuthenticated)
                                {
                                    <form asp-controller="Recommendation" asp-action="SaveRecommendation" asp-route-id="@recommendation.Id" method="post">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-bookmark me-2"></i> Lưu lịch trình này
                                        </button>
                                    </form>
                                }
                                else
                                {
                                    <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i> Đăng nhập để lưu lịch trình
                                    </a>
                                }
                            </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h4 class="alert-heading">Không tìm thấy gợi ý phù hợp!</h4>
                        <p>Rất tiếc, chúng tôi không thể tìm thấy gợi ý lịch trình phù hợp với sở thích của bạn. Vui lòng thử lại với các tiêu chí khác.</p>
                        <hr>
                        <a asp-action="Index" class="btn btn-primary">Quay lại trang gợi ý</a>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script src="~/js/recommendation.js"></script>
    <script>
        $(document).ready(function() {
            // Hide the loading screen when results are ready
            hideAILoadingScreen();

            // Show the first tab by default
            $('.recommendation-tab:first').addClass('active');
            $('.recommendation-tab-content:first').show();

            // Handle tab clicks
            $('.recommendation-tab').on('click', function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                $('.recommendation-tab').removeClass('active');

                // Add active class to clicked tab
                $(this).addClass('active');

                // Hide all tab content
                $('.recommendation-tab-content').hide();

                // Show the selected tab content
                var target = $(this).data('target');
                $(target).fadeIn(300);
            });

            // Animate recommendation cards on page load
            $('.recommendation-card').each(function(index) {
                var card = $(this);
                setTimeout(function() {
                    card.addClass('animated');
                }, index * 200);
            });

            // Animate sections when they come into view
            $(window).on('scroll', function() {
                $('.animate-on-scroll').each(function() {
                    if (isInViewport(this) && !$(this).hasClass('animated')) {
                        $(this).addClass('animated');
                    }
                });
            });

            // Check if element is in viewport
            function isInViewport(element) {
                var rect = element.getBoundingClientRect();
                return (
                    rect.top >= 0 &&
                    rect.left >= 0 &&
                    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                );
            }

            // Make the tab navigation sticky when scrolling
            var tabNav = $('.recommendation-tabs');
            var tabNavTop = tabNav.offset().top;

            $(window).on('scroll', function() {
                if ($(window).scrollTop() > tabNavTop) {
                    tabNav.addClass('sticky-tabs');
                } else {
                    tabNav.removeClass('sticky-tabs');
                }
            });
        });
    </script>
}
