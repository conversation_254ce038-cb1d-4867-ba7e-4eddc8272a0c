﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TourCart
    {
        public List<TourCartItem> Items { get; set; } = new List<TourCartItem>();
        
        public void AddItem(TourCartItem item)
        {
            var existingItem = Items.FirstOrDefault(i => i.TourId == item.TourId &&
                                                        i.TourDate == item.TourDate);
            if (existingItem != null)
            {
                existingItem.NumberOfAdults += item.NumberOfAdults;
                existingItem.NumberOfChildren += item.NumberOfChildren;
            }
            else
            {
                Items.Add(item);
            }
        }
        
        public void RemoveItem(int tourId)
        {
            Items.RemoveAll(i => i.TourId == tourId);
        }
        
        public void Clear()
        {
            Items.Clear();
        }
        
        [NotMapped]
        public decimal TotalPrice => Items.Sum(item => item.TotalPrice);
        
        [NotMapped]
        public int TotalItems => Items.Count;
    }
}
