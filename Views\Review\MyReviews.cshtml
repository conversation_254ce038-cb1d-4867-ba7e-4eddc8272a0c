@model IEnumerable<ViVu.Models.Review>
@{
    ViewData["Title"] = "Đánh giá của tôi";
}

<div class="container my-5">
    <h1 class="mb-4"><PERSON><PERSON><PERSON> giá của tôi</h1>
    
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i> Bạn chưa có đánh giá nào.
        </div>
    }
    else
    {
        <div class="row">
            @foreach (var review in Model)
            {
                string itemName = "";
                string itemType = "";
                int itemId = 0;
                string controllerName = "";
                
                if (review.AccommodationId.HasValue)
                {
                    itemName = review.Accommodation?.Name;
                    itemType = "Chỗ ở";
                    itemId = review.AccommodationId.Value;
                    controllerName = "Accommodation";
                }
                else if (review.TourId.HasValue)
                {
                    itemName = review.Tour?.Name;
                    itemType = "Tour";
                    itemId = review.TourId.Value;
                    controllerName = "Tour";
                }
                else if (review.ServiceId.HasValue)
                {
                    itemName = review.Service?.Name;
                    itemType = "Dịch vụ";
                    itemId = review.ServiceId.Value;
                    controllerName = "Service";
                }
                
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">@itemType: @itemName</h5>
                            <span class="badge bg-primary">@review.CreatedAt.ToString("dd/MM/yyyy")</span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-2">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= review.Rating)
                                            {
                                                <i class="bi bi-star-fill text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="bi bi-star text-warning"></i>
                                            }
                                        }
                                    </div>
                                    <span class="text-muted">(@review.Rating/5)</span>
                                </div>
                                <p class="card-text">@review.Comment</p>
                            </div>
                        </div>
                        <div class="card-footer d-flex justify-content-between">
                            <a asp-controller="@controllerName" asp-action="Details" asp-route-id="@itemId" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i> Xem chi tiết
                            </a>
                            <div>
                                <a asp-action="Edit" asp-route-id="@review.Id" class="btn btn-sm btn-outline-secondary me-1">
                                    <i class="bi bi-pencil"></i> Sửa
                                </a>
                                <a asp-action="Delete" asp-route-id="@review.Id" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i> Xóa
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
