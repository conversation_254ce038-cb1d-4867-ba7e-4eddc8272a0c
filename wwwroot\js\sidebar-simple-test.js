// Simple test for sidebar expandable
console.log('Simple test script loaded');

// Test immediately when script loads
setTimeout(() => {
    console.log('Testing sidebar elements...');
    
    // Check if elements exist
    const expandableButtons = document.querySelectorAll('.expandable-toggle');
    const sidebarExpanded = document.getElementById('sidebar-expanded');
    const expandedMenus = document.querySelectorAll('.expanded-menu');
    
    console.log('Elements found:', {
        expandableButtons: expandableButtons.length,
        sidebarExpanded: !!sidebarExpanded,
        expandedMenus: expandedMenus.length
    });
    
    // Add simple click handlers
    expandableButtons.forEach((button, index) => {
        console.log(`Button ${index + 1}:`, button);
        
        button.addEventListener('click', function(e) {
            console.log('CLICK DETECTED on button:', this);
            e.preventDefault();
            e.stopPropagation();
            
            const target = this.getAttribute('data-target');
            console.log('Target menu:', target);
            
            // Simple show/hide logic
            if (sidebarExpanded && target) {
                const targetMenu = document.getElementById(target);
                
                if (targetMenu) {
                    console.log('Target menu found:', targetMenu);
                    
                    // Hide all menus first
                    expandedMenus.forEach(menu => {
                        menu.classList.remove('active');
                    });
                    
                    // Toggle sidebar
                    if (sidebarExpanded.classList.contains('show')) {
                        sidebarExpanded.classList.remove('show');
                        console.log('Sidebar hidden');
                    } else {
                        sidebarExpanded.classList.add('show');
                        targetMenu.classList.add('active');
                        console.log('Sidebar shown with menu:', target);
                    }
                } else {
                    console.error('Target menu not found:', target);
                }
            }
        });
    });
    
    // Test function
    window.testSimple = function() {
        console.log('Manual test triggered');
        const firstButton = document.querySelector('.expandable-toggle');
        if (firstButton) {
            console.log('Clicking first button manually...');
            firstButton.click();
        } else {
            console.log('No button found for manual test');
        }
    };
    
}, 1000); // Wait 1 second for DOM to be fully ready
