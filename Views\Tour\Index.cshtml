@model IEnumerable<ViVu.Models.Tour>

@{
    ViewData["Title"] = "Danh sách tour";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex">
                <input type="text" name="searchTerm" class="form-control me-2" placeholder="Tìm kiếm tour..." value="@ViewBag.SearchTerm">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-md-6 text-md-end">
            <a asp-controller="TourSearch" asp-action="Index" class="btn btn-outline-primary">
                <i class="fas fa-filter me-1"></i>Tìm kiếm nâng cao
            </a>
        </div>
    </div>
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Không tìm thấy tour nào.
        </div>
    }
    else
    {
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            @foreach (var tour in Model)
            {
                <div class="col">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/no-image.jpg" : tour.ImageUrl)" 
                                 class="card-img-top" alt="@tour.Name" style="height: 200px; object-fit: cover;">
                            @if (tour.IsFeatured)
                            {
                                <span class="position-absolute top-0 start-0 badge bg-warning m-2">Nổi bật</span>
                            }
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">@tour.Name</h5>
                            <p class="card-text text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>@(tour.Location != null ? tour.Location.Name : ""), @(tour.City != null ? tour.City.Name : "")
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="fas fa-clock me-1"></i>@tour.Duration ngày
                                </span>
                                @if (tour.Reviews != null && tour.Reviews.Any())
                                {
                                    <span class="text-warning">
                                        @tour.Reviews.Average(r => r.Rating).ToString("0.0")
                                        <i class="fas fa-star"></i>
                                    </span>
                                }
                            </div>
                            <p class="card-text fw-bold text-primary fs-5">
                                @tour.Price.ToString("N0") VNĐ
                            </p>
                        </div>
                        <div class="card-footer bg-white border-top-0">
                            <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-outline-primary w-100">
                                <i class="fas fa-info-circle me-1"></i>Chi tiết
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
