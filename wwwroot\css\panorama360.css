/* CSS cho tính năng Panorama 360 */

.panorama-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 100px);
    min-height: 500px;
    overflow: hidden;
}

#panorama {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.panorama-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px;
    z-index: 2;
}

.panorama-info h2 {
    margin-top: 0;
    font-size: 1.5rem;
}

.panorama-thumbnail {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.panorama-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
}

.panorama-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.panorama-overlay span {
    margin-top: 10px;
    font-weight: bold;
}

.panorama-link:hover .panorama-overlay {
    opacity: 1;
}

/* Responsive styles */
@media (max-width: 768px) {
    .panorama-container {
        height: calc(100vh - 150px);
    }
    
    .panorama-info h2 {
        font-size: 1.2rem;
    }
}
