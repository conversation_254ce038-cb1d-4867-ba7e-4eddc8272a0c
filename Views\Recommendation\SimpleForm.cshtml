@model ViVu.Models.RecommendationViewModel

@{
    ViewData["Title"] = "Gợi Ý Lịch Trình Du Lịch Bến Tre";
}

<div class="container-fluid py-5 recommendation-container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-primary">Khám Phá Bến Tre</h1>
                <p class="lead">H<PERSON>y cho chúng tôi biết sở thích của bạn, AI sẽ gợi ý lịch trình du lịch phù hợp nhất!</p>
            </div>

            <div class="card shadow-lg border-0 rounded-lg overflow-hidden">
                <div class="card-body p-0">
                    <form asp-action="GetRecommendationsFromSimpleForm" method="post" id="simpleRecommendationForm" class="needs-validation" novalidate>

                        <!-- Progress bar -->
                        <div class="progress rounded-0" style="height: 8px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 25%;" id="formProgress"></div>
                        </div>

                        <!-- Form steps container -->
                        <div class="form-steps-container">

                            <!-- Step 1: Travel Type -->
                            <div class="form-step active" id="step1">
                                <div class="p-4 p-md-5">
                                    <h2 class="mb-4 text-center">Bạn thích loại hình du lịch nào?</h2>

                                    <div class="row g-4 justify-content-center">
                                        <div class="col-md-5">
                                            <div class="card h-100 travel-type-card" data-type="guided">
                                                <div class="card-body text-center p-4">
                                                    <div class="icon-container mb-3">
                                                        <i class="fas fa-map-marked-alt fa-3x text-primary"></i>
                                                    </div>
                                                    <h3>Tour Có Hướng Dẫn</h3>
                                                    <p class="text-muted">Trải nghiệm du lịch với hướng dẫn viên chuyên nghiệp</p>
                                                    <input type="checkbox" name="UserPreference.PrefersTours" value="true" id="prefersTours" class="d-none">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-5">
                                            <div class="card h-100 travel-type-card" data-type="independent">
                                                <div class="card-body text-center p-4">
                                                    <div class="icon-container mb-3">
                                                        <i class="fas fa-route fa-3x text-primary"></i>
                                                    </div>
                                                    <h3>Du Lịch Tự Túc</h3>
                                                    <p class="text-muted">Tự do khám phá theo lịch trình của riêng bạn</p>
                                                    <input type="checkbox" name="UserPreference.PrefersIndependentTravel" value="true" id="prefersIndependentTravel" class="d-none">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-center mt-4">
                                        <button type="button" class="btn btn-primary btn-lg px-5 next-step">Tiếp Tục</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Interests -->
                            <div class="form-step" id="step2">
                                <div class="p-4 p-md-5">
                                    <h2 class="mb-4 text-center">Bạn có sở thích gì?</h2>

                                    <div class="row g-3 justify-content-center">
                                        <div class="col-6 col-md-4">
                                            <div class="interest-card" data-interest="nature">
                                                <input type="checkbox" name="UserPreference.PrefersNature" value="true" id="prefersNature" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-tree fa-2x mb-2 text-success"></i>
                                                        <h5>Thiên Nhiên</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="interest-card" data-interest="history">
                                                <input type="checkbox" name="UserPreference.PrefersHistory" value="true" id="prefersHistory" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-landmark fa-2x mb-2 text-warning"></i>
                                                        <h5>Lịch Sử</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="interest-card" data-interest="food">
                                                <input type="checkbox" name="UserPreference.PrefersFood" value="true" id="prefersFood" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-utensils fa-2x mb-2 text-danger"></i>
                                                        <h5>Ẩm Thực</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="interest-card" data-interest="adventure">
                                                <input type="checkbox" name="UserPreference.PrefersAdventure" value="true" id="prefersAdventure" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-hiking fa-2x mb-2 text-info"></i>
                                                        <h5>Mạo Hiểm</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="interest-card" data-interest="relaxation">
                                                <input type="checkbox" name="UserPreference.PrefersRelaxation" value="true" id="prefersRelaxation" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-spa fa-2x mb-2 text-primary"></i>
                                                        <h5>Thư Giãn</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-outline-secondary btn-lg px-4 prev-step">Quay Lại</button>
                                        <button type="button" class="btn btn-primary btn-lg px-5 next-step">Tiếp Tục</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Budget -->
                            <div class="form-step" id="step3">
                                <div class="p-4 p-md-5">
                                    <h2 class="mb-4 text-center">Ngân sách của bạn là bao nhiêu?</h2>

                                    <div class="budget-slider-container text-center">
                                        <div class="budget-display mb-4">
                                            <span class="display-4 fw-bold text-primary" id="budgetDisplay">1.000.000</span>
                                            <span class="fs-4">VNĐ</span>
                                        </div>

                                        <input type="range" class="form-range" min="500000" max="5000000" step="100000" value="1000000" id="budgetSlider">
                                        <input type="hidden" name="UserPreference.MinBudget" id="minBudget" value="500000">
                                        <input type="hidden" name="UserPreference.MaxBudget" id="maxBudget" value="1500000">

                                        <div class="d-flex justify-content-between mt-2 text-muted">
                                            <small>500.000 VNĐ</small>
                                            <small>5.000.000 VNĐ</small>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-5">
                                        <button type="button" class="btn btn-outline-secondary btn-lg px-4 prev-step">Quay Lại</button>
                                        <button type="button" class="btn btn-primary btn-lg px-5 next-step">Tiếp Tục</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Number of People -->
                            <div class="form-step" id="step4">
                                <div class="p-4 p-md-5">
                                    <h2 class="mb-4 text-center">Bạn đi du lịch cùng ai?</h2>

                                    <div class="row g-4 justify-content-center">
                                        <div class="col-6 col-md-4">
                                            <div class="travel-group-card" data-group="solo">
                                                <input type="checkbox" name="UserPreference.TravelingAlone" value="true" id="travelingAlone" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-user fa-2x mb-2 text-primary"></i>
                                                        <h5>Đi Một Mình</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="travel-group-card" data-group="couple">
                                                <input type="checkbox" name="UserPreference.TravelingAsCouple" value="true" id="travelingAsCouple" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-heart fa-2x mb-2 text-danger"></i>
                                                        <h5>Cặp Đôi</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="travel-group-card" data-group="friends">
                                                <input type="checkbox" name="UserPreference.TravelingWithFriends" value="true" id="travelingWithFriends" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-users fa-2x mb-2 text-success"></i>
                                                        <h5>Bạn Bè</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="travel-group-card" data-group="family">
                                                <input type="checkbox" name="UserPreference.TravelingWithChildren" value="true" id="travelingWithChildren" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-child fa-2x mb-2 text-warning"></i>
                                                        <h5>Gia Đình Có Trẻ Em</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 col-md-4">
                                            <div class="travel-group-card" data-group="elderly">
                                                <input type="checkbox" name="UserPreference.TravelingWithElders" value="true" id="travelingWithElders" class="d-none">
                                                <div class="card h-100">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-walking fa-2x mb-2 text-info"></i>
                                                        <h5>Có Người Cao Tuổi</h5>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-5">
                                        <button type="button" class="btn btn-outline-secondary btn-lg px-4 prev-step">Quay Lại</button>
                                        <button type="submit" class="btn btn-success btn-lg px-5">Nhận Gợi Ý</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .recommendation-container {
            background-color: #f8f9fa;
            min-height: 80vh;
        }

        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .travel-type-card, .interest-card, .travel-group-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .travel-type-card:hover, .interest-card:hover, .travel-group-card:hover {
            transform: translateY(-5px);
        }

        .travel-type-card.selected, .interest-card.selected, .travel-group-card.selected {
            border: 2px solid #0d6efd;
            box-shadow: 0 0.5rem 1rem rgba(13, 110, 253, 0.15);
        }

        .travel-type-card.selected .icon-container,
        .interest-card.selected .card-body,
        .travel-group-card.selected .card-body {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .budget-slider-container {
            max-width: 500px;
            margin: 0 auto;
        }

        #budgetSlider {
            height: 8px;
        }

        #budgetSlider::-webkit-slider-thumb {
            height: 24px;
            width: 24px;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Form step navigation
            let currentStep = 1;
            const totalSteps = 4;

            // Update progress bar
            function updateProgress() {
                const progressPercentage = (currentStep / totalSteps) * 100;
                $('#formProgress').css('width', progressPercentage + '%');
            }

            // Next step button click
            $('.next-step').click(function() {
                // Validate current step
                if (validateStep(currentStep)) {
                    $('#step' + currentStep).removeClass('active');
                    currentStep++;
                    $('#step' + currentStep).addClass('active');
                    updateProgress();
                }
            });

            // Previous step button click
            $('.prev-step').click(function() {
                $('#step' + currentStep).removeClass('active');
                currentStep--;
                $('#step' + currentStep).addClass('active');
                updateProgress();
            });

            // Validate step
            function validateStep(step) {
                if (step === 1) {
                    // Check if at least one travel type is selected
                    if (!$('#prefersTours').prop('checked') && !$('#prefersIndependentTravel').prop('checked')) {
                        alert('Vui lòng chọn ít nhất một loại hình du lịch!');
                        return false;
                    }
                }
                return true;
            }

            // Travel type card selection
            $('.travel-type-card').click(function() {
                const type = $(this).data('type');
                $(this).toggleClass('selected');

                if (type === 'guided') {
                    $('#prefersTours').prop('checked', $(this).hasClass('selected'));
                } else if (type === 'independent') {
                    $('#prefersIndependentTravel').prop('checked', $(this).hasClass('selected'));
                }
            });

            // Interest card selection
            $('.interest-card').click(function() {
                $(this).toggleClass('selected');
                const interest = $(this).data('interest');
                const checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', $(this).hasClass('selected'));
            });

            // Travel group card selection
            $('.travel-group-card').click(function() {
                $(this).toggleClass('selected');
                const group = $(this).data('group');
                const checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', $(this).hasClass('selected'));
            });

            // Budget slider
            $('#budgetSlider').on('input', function() {
                const value = $(this).val();
                const formattedValue = new Intl.NumberFormat('vi-VN').format(value);
                $('#budgetDisplay').text(formattedValue);

                // Set min and max budget (min = value, max = value + 50%)
                const maxBudget = Math.round(parseInt(value) * 1.5);
                $('#minBudget').val(value);
                $('#maxBudget').val(maxBudget);
            });

            // Initialize form with default selections
            function initializeForm() {
                // Set default budget
                const defaultBudget = $('#budgetSlider').val();
                const formattedValue = new Intl.NumberFormat('vi-VN').format(defaultBudget);
                $('#budgetDisplay').text(formattedValue);

                // Set min and max budget
                const maxBudget = Math.round(parseInt(defaultBudget) * 1.5);
                $('#minBudget').val(defaultBudget);
                $('#maxBudget').val(maxBudget);

                // Log initial values
                console.log("Initial budget values:");
                console.log("Min budget: " + $('#minBudget').val());
                console.log("Max budget: " + $('#maxBudget').val());
            }

            // Call initialization function
            initializeForm();

            // Form submission
            $('#simpleRecommendationForm').on('submit', function(e) {
                // Ensure all checkboxes have values (true or false)
                $('input[type="checkbox"]').each(function() {
                    const name = $(this).attr('name');
                    const isChecked = $(this).prop('checked');

                    // Remove any existing hidden input with the same name
                    $('input[type="hidden"][name="' + name + '"]').remove();

                    // Set the checkbox value
                    $(this).val(isChecked);

                    // Add a hidden input for unchecked checkboxes
                    if (!isChecked) {
                        $(this).after('<input type="hidden" name="' + name + '" value="false">');
                    }
                });

                // Ensure budget values are set
                if (!$('#minBudget').val()) {
                    $('#minBudget').val($('#budgetSlider').val());
                }

                if (!$('#maxBudget').val()) {
                    $('#maxBudget').val(Math.round(parseInt($('#budgetSlider').val()) * 1.5));
                }

                // Log form data for debugging
                console.log("Form data being submitted:");
                $(this).serializeArray().forEach(function(item) {
                    console.log(item.name + ": " + item.value);
                });
            });
        });
    </script>
}
