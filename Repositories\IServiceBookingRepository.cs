﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IServiceBookingRepository
    {
        Task<IEnumerable<ServiceBooking>> GetAllAsync();
        Task<ServiceBooking> GetByIdAsync(int id);
        Task<IEnumerable<ServiceBooking>> GetByUserIdAsync(string userId);
        Task<ServiceBooking> GetByIdWithDetailsAsync(int id);
        Task AddAsync(ServiceBooking serviceBooking);
        Task UpdateAsync(ServiceBooking serviceBooking);
        Task DeleteAsync(int id);
        Task<IEnumerable<ServiceBooking>> GetRecentBookingsAsync(int count = 5);
        Task<int> GetTotalBookingsAsync();
    }
}
