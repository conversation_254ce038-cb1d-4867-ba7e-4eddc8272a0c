﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IVehicleRepository
    {
        Task<IEnumerable<Vehicle>> GetAllAsync();
        Task<Vehicle> GetByIdAsync(int id);
        Task<Vehicle> GetByIdWithDetailsAsync(int id);
        Task<IEnumerable<Vehicle>> GetByLocationIdAsync(int locationId);
        Task<IEnumerable<Vehicle>> GetByCityIdAsync(int cityId);
        Task<IEnumerable<Vehicle>> GetFeaturedVehiclesAsync(int count = 6);
        Task<IEnumerable<Vehicle>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null, 
            DateTime? startDate = null, DateTime? endDate = null, decimal? minPrice = null, decimal? maxPrice = null, VehicleType? vehicleType = null);
        Task AddAsync(Vehicle vehicle);
        Task UpdateAsync(Vehicle vehicle);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
}
