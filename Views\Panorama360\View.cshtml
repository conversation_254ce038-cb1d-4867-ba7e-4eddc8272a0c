@model ViVu.Models.Panorama360

@{
    ViewData["Title"] = "Du lịch 360° - " + Model.Name;
    Layout = "_Layout";
}

@section Styles {
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css" />
    <link rel="stylesheet" href="~/css/panorama360.css" />
}

<div class="container-fluid p-0">
    <div class="panorama-container">
        <div id="panorama"></div>
        <div class="panorama-info">
            <h2>@Model.Name</h2>
            <p>@Model.Description</p>

            @if (Model.Tour != null)
            {
                <p>
                    <a href="@Url.Action("Details", "Tour", new { id = Model.TourId })" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Quay lại tour
                    </a>
                </p>
            }
            else if (Model.Location != null)
            {
                <p>
                    <a href="@Url.Action("Details", "Location", new { id = Model.LocationId })" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Quay lại địa điểm
                    </a>
                </p>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const panoramaImage = '@Model.ImageUrl';
            const panoramaElement = document.getElementById('panorama');

            // Cấu hình Pannellum
            const viewerConfig = {
                "type": "equirectangular",
                "panorama": panoramaImage,
                "autoLoad": true,
                "autoRotate": 2,
                "compass": true,
                "showControls": true,
                "showFullscreenCtrl": true,
                "showZoomCtrl": true,
                "yaw": 0,
                "pitch": 0,
                "hfov": 100
            };

            // Xử lý hotspot data nếu có
            @if (!string.IsNullOrEmpty(Model.HotspotData))
            {
                <text>
                const hotspotData = @Html.Raw(Model.HotspotData);

                if (hotspotData && Array.isArray(hotspotData)) {
                    viewerConfig.hotSpots = hotspotData.map(function(spot) {
                        return {
                            pitch: spot.pitch || 0,
                            yaw: spot.yaw || 0,
                            type: spot.linkTo ? "scene" : "info",
                            text: spot.text,
                            URL: spot.linkTo || null,
                            sceneId: spot.sceneId || null
                        };
                    });
                }
                </text>
            }

            // Khởi tạo Pannellum viewer
            pannellum.viewer('panorama', viewerConfig);
        });
    </script>
}
