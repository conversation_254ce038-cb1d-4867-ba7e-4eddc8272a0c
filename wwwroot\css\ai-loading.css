/* AI Loading Screen Styles */
.ai-loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 9999;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
}

.ai-loading-content {
    max-width: 500px;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease-in-out;
}

.ai-loading-title {
    margin-top: 1.5rem;
    color: var(--bs-primary);
    font-weight: 600;
}

.ai-loading-subtitle {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.ai-loading-animation {
    position: relative;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-robot {
    font-size: 3rem;
    color: var(--bs-primary);
    animation: float 3s ease-in-out infinite;
}

.ai-thinking {
    display: flex;
    position: absolute;
    top: 30px;
    right: 30%;
}

.ai-thinking-dot {
    width: 10px;
    height: 10px;
    margin: 0 5px;
    background-color: var(--bs-primary);
    border-radius: 50%;
    opacity: 0.7;
}

.ai-thinking-dot:nth-child(1) {
    animation: thinking 1.5s infinite 0s;
}

.ai-thinking-dot:nth-child(2) {
    animation: thinking 1.5s infinite 0.3s;
}

.ai-thinking-dot:nth-child(3) {
    animation: thinking 1.5s infinite 0.6s;
}

.ai-loading-progress {
    margin: 1rem 0;
}

.ai-loading-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.ai-loading-info p {
    margin-bottom: 0.5rem;
}

/* Animations */
@keyframes thinking {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Shimmer effect for text */
.shimmer-text {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.8) 50%, 
        rgba(255, 255, 255, 0) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ai-loading-content {
        max-width: 90%;
        padding: 1.5rem;
    }
    
    .ai-robot {
        font-size: 2.5rem;
    }
    
    .ai-thinking {
        right: 25%;
    }
}
