﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class VehicleBookingDetail
    {
        public int Id { get; set; }
        
        public int VehicleBookingId { get; set; }
        
        public int VehicleId { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; }
        
        [Required]
        public DateTime EndDate { get; set; }
        
        [Required]
        [Range(0.01, 100000.00)]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PricePerDay { get; set; }
        
        [Required]
        [Range(1, 100)]
        public int NumberOfDays { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        
        [ForeignKey("VehicleBookingId")]
        [ValidateNever]
        public VehicleBooking? VehicleBooking { get; set; }
        
        [ForeignKey("VehicleId")]
        [ValidateNever]
        public Vehicle? Vehicle { get; set; }
    }
}
