﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class Panorama360
    {
        public int Id { get; set; }
        
        [Required, StringLength(100)]
        public string Name { get; set; }
        
        [Required]
        public string Description { get; set; }
        
        [Required]
        public string ImageUrl { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // Loại panorama (có thể là Tour hoặc Location)
        public string Type { get; set; } = "Location";
        
        // Quan hệ với Tour (nếu có)
        public int? TourId { get; set; }
        
        [ForeignKey("TourId")]
        public Tour? Tour { get; set; }
        
        // Quan hệ với Location (nếu có)
        public int? LocationId { get; set; }
        
        [ForeignKey("LocationId")]
        public Location? Location { get; set; }
        
        // Thông tin bổ sung cho panorama
        public string? HotspotData { get; set; }
        
        // Thứ tự hiển thị (nếu có nhiều panorama cho cùng một tour/location)
        public int DisplayOrder { get; set; } = 0;
        
        // Ngày tạo
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
