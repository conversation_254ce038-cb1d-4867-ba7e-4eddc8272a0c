@model ViVu.Models.Room
@{
    ViewData["Title"] = "Xóa phòng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Phòng</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa phòng
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo!</h5>
                <p>Bạn có chắc chắn muốn xóa phòng này không? Hành động này không thể hoàn tác.</p>
            </div>
            
            <div asp-validation-summary="All" class="text-danger"></div>
            
            <div class="row">
                <div class="col-md-4">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" />
                </div>
                <div class="col-md-8">
                    <h3>@Model.Name</h3>
                    <p><i class="fas fa-hotel"></i> Thuộc: @Model.Accommodation?.Name</p>
                    
                    <dl class="row">
                        <dt class="col-sm-3">Giá/đêm:</dt>
                        <dd class="col-sm-9">@Model.PricePerNight.ToString("N0") VNĐ</dd>
                        
                        <dt class="col-sm-3">Sức chứa:</dt>
                        <dd class="col-sm-9">@Model.MaxOccupancy người</dd>
                        
                        <dt class="col-sm-3">Trạng thái:</dt>
                        <dd class="col-sm-9">
                            @if (Model.IsAvailable)
                            {
                                <span class="badge bg-success">Có sẵn</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">Không có sẵn</span>
                            }
                        </dd>
                    </dl>
                    
                    <form asp-action="DeleteConfirmed" method="post">
                        <input type="hidden" asp-for="Id" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
