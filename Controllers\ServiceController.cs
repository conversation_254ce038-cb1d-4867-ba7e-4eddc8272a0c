﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Security.Claims;
using ViVu.Models;
using ViVu.Models.ViewModels;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class ServiceController : Controller
    {
        private readonly IServiceRepository _serviceRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly IServiceBookingRepository _serviceBookingRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public ServiceController(
            IServiceRepository serviceRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            IServiceBookingRepository serviceBookingRepository,
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context)
        {
            _serviceRepository = serviceRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _serviceBookingRepository = serviceBookingRepository;
            _userManager = userManager;
            _context = context;
        }

        // GET: Service
        public async Task<IActionResult> Index(string searchTerm, int? locationId, int? cityId, DateTime? serviceDate, decimal? minPrice, decimal? maxPrice)
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            ViewBag.SearchTerm = searchTerm;
            ViewBag.LocationId = locationId;
            ViewBag.CityId = cityId;
            ViewBag.ServiceDate = serviceDate;
            ViewBag.MinPrice = minPrice;
            ViewBag.MaxPrice = maxPrice;

            var services = await _serviceRepository.SearchAsync(searchTerm, locationId, cityId, serviceDate, minPrice, maxPrice);
            return View(services);
        }

        // GET: Service/Details/5
        public async Task<IActionResult> Details(int id, DateTime? serviceDate)
        {
            var service = await _serviceRepository.GetByIdWithDetailsAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            ViewBag.ServiceDate = serviceDate ?? DateTime.Today.AddDays(1);

            return View(service);
        }

        // GET: Service/Book/5
        [Authorize]
        public async Task<IActionResult> Book(int id, DateTime serviceDate, int quantity = 1)
        {
            var service = await _serviceRepository.GetByIdAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            var bookingViewModel = new ServiceBookingViewModel
            {
                ServiceId = service.Id,
                Service = service,
                ServiceDate = serviceDate,
                Quantity = quantity
            };

            return View(bookingViewModel);
        }

        // POST: Service/Book
        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Book(ServiceBookingViewModel model)
        {
            if (ModelState.IsValid)
            {
                var service = await _serviceRepository.GetByIdAsync(model.ServiceId);
                if (service == null)
                {
                    return NotFound();
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                // Tạo đơn đặt dịch vụ mới
                var serviceBooking = new ServiceBooking
                {
                    UserId = userId,
                    ServiceDate = model.ServiceDate,
                    TotalPrice = service.Price * model.Quantity,
                    SpecialRequests = model.SpecialRequests,
                    Status = ServiceBookingStatus.Pending,
                    ServiceBookingDetails = new List<ServiceBookingDetail>
                    {
                        new ServiceBookingDetail
                        {
                            ServiceId = service.Id,
                            Quantity = model.Quantity,
                            Price = service.Price,
                            ServiceDate = model.ServiceDate,
                            Status = ServiceBookingDetailStatus.Pending
                        }
                    }
                };

                await _serviceBookingRepository.AddAsync(serviceBooking);

                TempData["SuccessMessage"] = "Đặt dịch vụ thành công! Mã đặt dịch vụ của bạn là #" + serviceBooking.Id;
                return RedirectToAction("BookingConfirmation", new { id = serviceBooking.Id });
            }

            // Nếu ModelState không hợp lệ, lấy lại thông tin dịch vụ và hiển thị lại form
            model.Service = await _serviceRepository.GetByIdAsync(model.ServiceId);
            return View(model);
        }

        // GET: Service/BookingConfirmation/5
        [Authorize]
        public async Task<IActionResult> BookingConfirmation(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var booking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);

            if (booking == null || booking.UserId != userId)
            {
                return NotFound();
            }

            return View(booking);
        }

        // GET: Service/MyBookings
        [Authorize]
        public async Task<IActionResult> MyBookings()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var bookings = await _serviceBookingRepository.GetByUserIdAsync(userId);

            return View(bookings);
        }

        // POST: Service/CancelBooking/5
        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CancelBooking(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var booking = await _serviceBookingRepository.GetByIdWithDetailsAsync(id);

            if (booking == null || booking.UserId != userId)
            {
                return NotFound();
            }

            if (booking.Status == ServiceBookingStatus.Pending || booking.Status == ServiceBookingStatus.Confirmed)
            {
                booking.Status = ServiceBookingStatus.Cancelled;
                foreach (var detail in booking.ServiceBookingDetails)
                {
                    detail.Status = ServiceBookingDetailStatus.Cancelled;
                }

                await _serviceBookingRepository.UpdateAsync(booking);
                TempData["SuccessMessage"] = "Đơn đặt dịch vụ đã được hủy thành công.";
            }
            else
            {
                TempData["ErrorMessage"] = "Không thể hủy đơn đặt dịch vụ này.";
            }

            return RedirectToAction(nameof(MyBookings));
        }

        // POST: Service/AddReview
        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddReview(ServiceReviewViewModel model)
        {
            if (ModelState.IsValid)
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                var review = new Review
                {
                    ServiceId = model.ServiceId,
                    UserId = userId,
                    Rating = model.Rating,
                    Comment = model.Comment,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Reviews.Add(review);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Cảm ơn bạn đã đánh giá dịch vụ!";
                return RedirectToAction("Details", new { id = model.ServiceId });
            }

            // Nếu ModelState không hợp lệ, lấy lại thông tin dịch vụ và hiển thị lại form
            var service = await _serviceRepository.GetByIdWithDetailsAsync(model.ServiceId);
            model.Service = service;
            model.Reviews = service.Reviews;
            return View("Details", service);
        }
    }
}
