﻿﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class Booking
    {
        public int Id { get; set; }
        
        public string UserId { get; set; }
        
        [Required]
        public DateTime BookingDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        public DateTime CheckInDate { get; set; }
        
        [Required]
        public DateTime CheckOutDate { get; set; }
        
        [Required]
        public decimal TotalPrice { get; set; }
        
        public string? SpecialRequests { get; set; }
        
        public BookingStatus Status { get; set; } = BookingStatus.Pending;
        
        [ForeignKey("UserId")]
        [ValidateNever]
        public ApplicationUser ApplicationUser { get; set; }
        
        public List<BookingDetail> BookingDetails { get; set; }
        
        [NotMapped]
        public int TotalNights => (CheckOutDate - CheckInDate).Days;
    }
    
    public enum BookingStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
