﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class ServiceBooking
    {
        public int Id { get; set; }
        
        public string UserId { get; set; }
        
        [Required]
        public DateTime BookingDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        public DateTime ServiceDate { get; set; }
        
        [Required]
        public decimal TotalPrice { get; set; }
        
        public string? SpecialRequests { get; set; }
        
        public ServiceBookingStatus Status { get; set; } = ServiceBookingStatus.Pending;
        
        [ForeignKey("UserId")]
        [ValidateNever]
        public ApplicationUser ApplicationUser { get; set; }
        
        public List<ServiceBookingDetail> ServiceBookingDetails { get; set; }
    }
    
    public enum ServiceBookingStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
