﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IAccommodationRepository
    {
        Task<IEnumerable<Accommodation>> GetAllAsync();
        Task<Accommodation> GetByIdAsync(int id);
        Task<IEnumerable<Accommodation>> GetByLocationIdAsync(int locationId);
        Task<IEnumerable<Accommodation>> GetByCityIdAsync(int cityId);
        Task<IEnumerable<Accommodation>> GetFeaturedAsync();
        Task<IEnumerable<Accommodation>> SearchAsync(string searchTerm, int? locationId, int? cityId, DateTime? checkIn, DateTime? checkOut, int? minPrice, int? maxPrice, int? starRating);
        Task AddAsync(Accommodation accommodation);
        Task UpdateAsync(Accommodation accommodation);
        Task DeleteAsync(int id);
    }
}
