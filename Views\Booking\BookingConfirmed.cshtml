@model ViVu.Models.Booking

@{
    ViewData["Title"] = "Xác nhận đặt phòng";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-success mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0 text-center">Đặt phòng thành công!</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 5rem;"></i>
                        <h5 class="mt-3">Cảm ơn bạn đã đặt phòng tại ViVu!</h5>
                        <p class="text-muted">Mã đặt phòng của bạn là: <strong>#@Model.Id</strong></p>
                    </div>

                    <div class="alert alert-info">
                        <p class="mb-0">Chúng tôi đã gửi email xác nhận đặt phòng đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư đến hoặc thư rác.</p>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Thông tin đặt phòng</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày đặt phòng:</strong></p>
                                    <p>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Trạng thái:</strong></p>
                                    <p><span class="badge bg-success">@Model.Status</span></p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày nhận phòng:</strong></p>
                                    <p>@Model.CheckInDate.ToString("dd/MM/yyyy")</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày trả phòng:</strong></p>
                                    <p>@Model.CheckOutDate.ToString("dd/MM/yyyy")</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Tổng tiền:</strong></p>
                                    <p class="text-success fw-bold">@Model.TotalPrice.ToString("#,##0") VNĐ</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Phương thức thanh toán:</strong></p>
                                    <p>Thanh toán tại khách sạn</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Chi tiết phòng</h5>
                        </div>
                        <div class="card-body">
                            @foreach (var detail in Model.BookingDetails)
                            {
                                <div class="mb-3 pb-3 @(detail != Model.BookingDetails.Last() ? "border-bottom" : "")">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">@detail.Room.Name</h6>
                                        <span class="badge bg-primary">@detail.NumberOfRooms phòng</span>
                                    </div>
                                    <p class="text-muted mb-2">@detail.Room.Accommodation.Name</p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="d-block mb-1"><strong>Số khách:</strong> @detail.NumberOfGuests người</small>
                                            <small class="d-block mb-1"><strong>Giá/đêm:</strong> @detail.PricePerNight.ToString("#,##0") VNĐ</small>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <small class="d-block mb-1"><strong>Số đêm:</strong> @((Model.CheckOutDate - Model.CheckInDate).Days)</small>
                                            <small class="d-block mb-1"><strong>Thành tiền:</strong> @((detail.PricePerNight * detail.NumberOfRooms * (Model.CheckOutDate - Model.CheckInDate).Days).ToString("#,##0")) VNĐ</small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Yêu cầu đặc biệt</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">@Model.SpecialRequests</p>
                            </div>
                        </div>
                    }

                    <div class="d-flex justify-content-between mt-4">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
                            <i class="bi bi-house me-2"></i>Về trang chủ
                        </a>
                        <a asp-action="MyBookings" class="btn btn-primary">
                            <i class="bi bi-list-check me-2"></i>Xem đặt phòng của tôi
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
