@model ViVu.Models.VehicleBooking

@{
    ViewData["Title"] = "Chi tiết đặt phương tiện";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý đặt phương tiện</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Thông tin đơn đặt phương tiện #@Model.Id
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Thông tin khách hàng</h5>
                    <p><strong>Họ tên:</strong> @Model.ApplicationUser.FullName</p>
                    <p><strong>Email:</strong> @Model.ApplicationUser.Email</p>
                    <p><strong>Số điện thoại:</strong> @Model.ApplicationUser.PhoneNumber</p>
                    <p><strong>Địa chỉ:</strong> @Model.ApplicationUser.Address</p>
                </div>
                <div class="col-md-6">
                    <h5>Thông tin đơn hàng</h5>
                    <p><strong>Mã đơn hàng:</strong> #@Model.Id</p>
                    <p><strong>Ngày đặt:</strong> @Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                    <p><strong>Ngày bắt đầu:</strong> @Model.StartDate.ToString("dd/MM/yyyy")</p>
                    <p><strong>Ngày kết thúc:</strong> @Model.EndDate.ToString("dd/MM/yyyy")</p>
                    <p><strong>Tổng tiền:</strong> @Model.TotalPrice.ToString("N0") VNĐ</p>
                    <p>
                        <strong>Trạng thái:</strong>
                        <span class="badge @(Model.Status == VehicleBookingStatus.Pending ? "bg-warning" :
                                           Model.Status == VehicleBookingStatus.Confirmed ? "bg-primary" :
                                           Model.Status == VehicleBookingStatus.Completed ? "bg-success" : "bg-danger")">
                            @Model.Status.ToString()
                        </span>
                    </p>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(Model.SpecialRequests))
            {
                <div class="mb-4">
                    <h5>Yêu cầu đặc biệt</h5>
                    <p>@Model.SpecialRequests</p>
                </div>
            }

            <h5 class="mb-3">Chi tiết phương tiện</h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Phương tiện</th>
                            <th>Loại</th>
                            <th>Biển số</th>
                            <th>Ngày bắt đầu</th>
                            <th>Ngày kết thúc</th>
                            <th>Số ngày</th>
                            <th>Giá/ngày</th>
                            <th>Tổng tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.VehicleBookingDetails)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(detail.Vehicle?.ImageUrl))
                                        {
                                            <img src="@detail.Vehicle.ImageUrl" alt="@detail.Vehicle.Name" style="width: 50px; height: 40px; object-fit: cover;" class="me-2" />
                                        }
                                        <a asp-area="Admin" asp-controller="Vehicle" asp-action="Details" asp-route-id="@detail.VehicleId">
                                            @detail.Vehicle?.Name
                                        </a>
                                    </div>
                                </td>
                                <td>@detail.Vehicle?.Type.ToString()</td>
                                <td>@detail.Vehicle?.LicensePlate</td>
                                <td>@detail.StartDate.ToString("dd/MM/yyyy")</td>
                                <td>@detail.EndDate.ToString("dd/MM/yyyy")</td>
                                <td>@detail.NumberOfDays</td>
                                <td>@detail.PricePerDay.ToString("N0") VNĐ</td>
                                <td>@detail.TotalPrice.ToString("N0") VNĐ</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="7" class="text-end"><strong>Tổng cộng:</strong></td>
                            <td><strong>@Model.TotalPrice.ToString("N0") VNĐ</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="mt-4">
                <h5>Cập nhật trạng thái</h5>
                <form asp-action="UpdateStatus" method="post" class="row g-3">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <div class="col-md-4">
                        <select name="status" class="form-select">
                            @foreach (VehicleBookingStatus status in Enum.GetValues(typeof(VehicleBookingStatus)))
                            {
                                <option value="@status" selected="@(Model.Status == status)">@status.ToString()</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Cập nhật trạng thái
                        </button>
                    </div>
                </form>
            </div>

            <div class="mt-4">
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
                </a>
                <form asp-action="Delete" method="post" class="d-inline-block" onsubmit="return confirm('Bạn có chắc chắn muốn xóa đơn đặt phương tiện này không?');">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Xóa đơn hàng
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
