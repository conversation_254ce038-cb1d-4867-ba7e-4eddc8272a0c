@model ViVu.Models.VehicleCart

@{
    ViewData["Title"] = "Đặt phương tiện";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body p-4">
                    <h5 class="mb-3">Thông tin đặt phương tiện</h5>
                    
                    <form asp-action="Checkout" method="post">
                        <div class="mb-3">
                            <label class="form-label"><PERSON><PERSON><PERSON> cầu đặc biệt (nếu có)</label>
                            <textarea name="specialRequests" class="form-control" rows="3" placeholder="Nhập yêu cầu đặc biệt của bạn (nếu có)"></textarea>
                        </div>
                        
                        <h5 class="mb-3 mt-4">Phương tiện đã chọn</h5>
                        
                        @foreach (var item in Model.Items)
                        {
                            <div class="card mb-3 border-0 shadow-sm">
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <img src="@(string.IsNullOrEmpty(item.ImageUrl) ? "/images/no-image.jpg" : item.ImageUrl)" 
                                             class="img-fluid rounded-start" alt="@item.VehicleName" style="height: 100%; object-fit: cover;">
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card-body">
                                            <h5 class="card-title">@item.VehicleName</h5>
                                            <p class="card-text">
                                                <span class="badge bg-primary">@item.VehicleType.ToString()</span>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Ngày bắt đầu:</strong> @item.StartDate.ToString("dd/MM/yyyy")</p>
                                                    <p class="mb-1"><strong>Ngày kết thúc:</strong> @item.EndDate.ToString("dd/MM/yyyy")</p>
                                                    <p class="mb-1"><strong>Số ngày thuê:</strong> @item.NumberOfDays ngày</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Giá thuê/ngày:</strong> @item.PricePerDay.ToString("#,##0") VNĐ</p>
                                                    <p class="mb-1"><strong>Số lượng:</strong> @item.Quantity</p>
                                                    <p class="mb-1"><strong>Tổng tiền:</strong> @item.TotalPrice.ToString("#,##0") VNĐ</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>Xác nhận đặt phương tiện
                            </button>
                            <a asp-action="Cart" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Quay lại giỏ hàng
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm border-0 mb-4 sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tổng đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tổng số phương tiện:</span>
                            <span>@Model.TotalItems</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tổng tiền:</span>
                            <span class="fw-bold">@Model.TotalPrice.ToString("#,##0") VNĐ</span>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Bạn sẽ thanh toán khi nhận phương tiện.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
