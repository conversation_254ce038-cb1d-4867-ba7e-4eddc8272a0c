﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface ILocationRepository
    {
        Task<IEnumerable<Location>> GetAllAsync();
        Task<Location> GetByIdAsync(int id);
        Task<Location> GetByIdWithDetailsAsync(int id);
        Task<IEnumerable<Location>> GetByCityIdAsync(int cityId);
        Task<IEnumerable<Location>> GetFeaturedAsync();
        Task AddAsync(Location location);
        Task UpdateAsync(Location location);
        Task DeleteAsync(int id);
    }
}
