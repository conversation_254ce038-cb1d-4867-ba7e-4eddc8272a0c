@model ViVu.Models.Tour

@{
    ViewData["Title"] = "Chỉnh sửa tour";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý tour</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Thông tin tour
        </div>
        <div class="card-body">
            <form asp-action="Edit" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên tour</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="LocationId" class="control-label">Địa điểm</label>
                            <select asp-for="LocationId" class="form-select" asp-items="ViewBag.Locations" required>
                                <option value="">-- Chọn địa điểm --</option>
                            </select>
                            <span asp-validation-for="LocationId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CityId" class="control-label">Thành phố</label>
                            <select asp-for="CityId" class="form-select" asp-items="ViewBag.Cities" required>
                                <option value="">-- Chọn thành phố --</option>
                            </select>
                            <span asp-validation-for="CityId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Address" class="control-label">Địa chỉ</label>
                            <input asp-for="Address" class="form-control" required />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Duration" class="control-label">Thời gian (ngày)</label>
                            <input asp-for="Duration" class="form-control" type="number" min="1" max="30" required />
                            <span asp-validation-for="Duration" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Price" class="control-label">Giá (VNĐ)</label>
                            <input asp-for="Price" class="form-control" type="number" min="0.01" step="0.01" required />
                            <span asp-validation-for="Price" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="MaxGroupSize" class="control-label">Số người tối đa</label>
                            <input asp-for="MaxGroupSize" class="form-control" type="number" min="1" max="100" required />
                            <span asp-validation-for="MaxGroupSize" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="IsFeatured" class="control-label">Nổi bật</label>
                            <div class="form-check form-switch">
                                <input asp-for="IsFeatured" class="form-check-input" />
                                <label class="form-check-label" for="IsFeatured">Hiển thị tour này ở trang chủ</label>
                            </div>
                            <span asp-validation-for="IsFeatured" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="4" required></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Itinerary" class="control-label">Lịch trình</label>
                    <textarea asp-for="Itinerary" class="form-control" rows="6" required></textarea>
                    <span asp-validation-for="Itinerary" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label for="imageFile" class="control-label">Hình ảnh</label>
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <div class="mb-2">
                            <img src="@Model.ImageUrl" alt="@Model.Name" style="max-width: 200px; max-height: 150px;" class="img-thumbnail" />
                        </div>
                    }
                    <input type="file" id="imageFile" name="imageFile" class="form-control" accept="image/*" />
                    <small class="form-text text-muted">Để trống nếu không muốn thay đổi hình ảnh.</small>
                </div>

                <div class="form-group mb-4">
                    <label class="control-label">Tiện ích</label>
                    <div class="row">
                        @foreach (var item in ViewBag.Amenities)
                        {
                            var amenity = item.Amenity;
                            var isSelected = item.IsSelected;

                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="selectedAmenities" value="@amenity.Id" id="<EMAIL>" checked="@isSelected">
                                    <label class="form-check-label" for="<EMAIL>">
                                        @if (!string.IsNullOrEmpty(amenity.Icon))
                                        {
                                            <i class="@amenity.Icon me-1"></i>
                                        }
                                        @amenity.Name
                                    </label>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Lưu thay đổi
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/disable-price-validation.js"></script>
}
