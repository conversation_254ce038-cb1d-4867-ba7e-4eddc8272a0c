using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Data;
using ViVu.Models;
using System.Threading.Tasks;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class AccommodationController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public AccommodationController(ApplicationDbContext context, IWebHostEnvironment hostEnvironment)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Accommodation
        public async Task<IActionResult> Index()
        {
            var accommodations = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Rooms)
                .ToListAsync();

            // T<PERSON>h giá thấp nhất cho mỗi chỗ ở
            foreach (var accommodation in accommodations)
            {
                accommodation.MinPrice = accommodation.Rooms.Any()
                    ? accommodation.Rooms.Min(r => r.PricePer<PERSON>ight)
                    : 0;
            }

            return View(accommodations);
        }

        // GET: Admin/Accommodation/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var accommodation = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Rooms)
                .Include(a => a.AccommodationAmenities)
                    .ThenInclude(aa => aa.Amenity)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (accommodation == null)
            {
                return NotFound();
            }

            return View(accommodation);
        }

        // GET: Admin/Accommodation/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Locations = new SelectList(await _context.Locations.ToListAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _context.Cities.ToListAsync(), "Id", "Name");
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View();
        }

        // POST: Admin/Accommodation/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Accommodation accommodation, IFormFile imageFile, int[] selectedAmenities)
        {
            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    accommodation.ImageUrl = await SaveImage(imageFile);
                }

                // Thêm chỗ ở vào cơ sở dữ liệu
                _context.Add(accommodation);
                await _context.SaveChangesAsync();

                // Thêm tiện nghi cho chỗ ở
                if (selectedAmenities != null && selectedAmenities.Length > 0)
                {
                    foreach (var amenityId in selectedAmenities)
                    {
                        _context.AccommodationAmenities.Add(new AccommodationAmenity
                        {
                            AccommodationId = accommodation.Id,
                            AmenityId = amenityId
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _context.Locations.ToListAsync(), "Id", "Name", accommodation.LocationId);
            ViewBag.Cities = new SelectList(await _context.Cities.ToListAsync(), "Id", "Name", accommodation.CityId);
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View(accommodation);
        }

        // GET: Admin/Accommodation/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var accommodation = await _context.Accommodations
                .Include(a => a.AccommodationAmenities)
                    .ThenInclude(aa => aa.Amenity)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (accommodation == null)
            {
                return NotFound();
            }

            ViewBag.Locations = new SelectList(await _context.Locations.ToListAsync(), "Id", "Name", accommodation.LocationId);
            ViewBag.Cities = new SelectList(await _context.Cities.ToListAsync(), "Id", "Name", accommodation.CityId);

            // Lấy danh sách tất cả tiện nghi và đánh dấu những tiện nghi đã được chọn
            var allAmenities = await _context.Amenities.ToListAsync();
            var selectedAmenityIds = accommodation.AccommodationAmenities.Select(aa => aa.AmenityId).ToList();

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = selectedAmenityIds.Contains(a.Id)
            }).ToList();

            return View(accommodation);
        }

        // POST: Admin/Accommodation/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Accommodation accommodation, IFormFile imageFile, int[] selectedAmenities)
        {
            if (id != accommodation.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Lấy thông tin chỗ ở hiện tại từ cơ sở dữ liệu
                    var existingAccommodation = await _context.Accommodations
                        .Include(a => a.AccommodationAmenities)
                            .ThenInclude(aa => aa.Amenity)
                        .FirstOrDefaultAsync(a => a.Id == id);

                    if (existingAccommodation == null)
                    {
                        return NotFound();
                    }

                    // Cập nhật thông tin chỗ ở
                    existingAccommodation.Name = accommodation.Name;
                    existingAccommodation.Description = accommodation.Description;
                    existingAccommodation.Address = accommodation.Address;
                    existingAccommodation.LocationId = accommodation.LocationId;
                    existingAccommodation.CityId = accommodation.CityId;
                    existingAccommodation.StarRating = accommodation.StarRating;
                    existingAccommodation.IsFeatured = accommodation.IsFeatured;

                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        existingAccommodation.ImageUrl = await SaveImage(imageFile);
                    }

                    // Cập nhật tiện nghi
                    // Xóa tất cả các tiện nghi hiện tại
                    var currentAmenities = await _context.AccommodationAmenities
                        .Where(aa => aa.AccommodationId == id)
                        .ToListAsync();

                    _context.AccommodationAmenities.RemoveRange(currentAmenities);

                    // Thêm lại các tiện nghi đã chọn
                    if (selectedAmenities != null && selectedAmenities.Length > 0)
                    {
                        foreach (var amenityId in selectedAmenities)
                        {
                            _context.AccommodationAmenities.Add(new AccommodationAmenity
                            {
                                AccommodationId = id,
                                AmenityId = amenityId
                            });
                        }
                    }

                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AccommodationExists(accommodation.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _context.Locations.ToListAsync(), "Id", "Name", accommodation.LocationId);
            ViewBag.Cities = new SelectList(await _context.Cities.ToListAsync(), "Id", "Name", accommodation.CityId);
            ViewBag.Amenities = await _context.Amenities.ToListAsync();

            return View(accommodation);
        }

        // GET: Admin/Accommodation/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var accommodation = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (accommodation == null)
            {
                return NotFound();
            }

            return View(accommodation);
        }

        // POST: Admin/Accommodation/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var accommodation = await _context.Accommodations.FindAsync(id);
            if (accommodation == null)
            {
                return NotFound();
            }

            // Kiểm tra xem có phòng nào thuộc chỗ ở này không
            var hasRooms = await _context.Rooms.AnyAsync(r => r.AccommodationId == id);
            if (hasRooms)
            {
                ModelState.AddModelError("", "Không thể xóa chỗ ở này vì có phòng thuộc chỗ ở này.");
                return View(accommodation);
            }

            // Kiểm tra xem có đơn đặt phòng nào liên quan đến chỗ ở này không
            var hasBookings = await _context.BookingDetails
                .Include(bd => bd.Room)
                .AnyAsync(bd => bd.Room.AccommodationId == id);

            if (hasBookings)
            {
                ModelState.AddModelError("", "Không thể xóa chỗ ở này vì có đơn đặt phòng liên quan.");
                return View(accommodation);
            }

            // Xóa các liên kết với tiện nghi
            var accommodationAmenities = await _context.AccommodationAmenities
                .Where(aa => aa.AccommodationId == id)
                .ToListAsync();

            _context.AccommodationAmenities.RemoveRange(accommodationAmenities);

            // Xóa chỗ ở
            _context.Accommodations.Remove(accommodation);
            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        private bool AccommodationExists(int id)
        {
            return _context.Accommodations.Any(e => e.Id == id);
        }

        // Lưu hình ảnh vào thư mục wwwroot/images/accommodations
        private async Task<string> SaveImage(IFormFile image)
        {
            // Tạo tên file duy nhất để tránh trùng lặp
            string uniqueFileName = Guid.NewGuid().ToString() + "_" + image.FileName;

            // Tạo đường dẫn đến thư mục lưu trữ
            string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "accommodations");

            // Tạo thư mục nếu chưa tồn tại
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            // Tạo đường dẫn đầy đủ đến file
            string filePath = Path.Combine(uploadsFolder, uniqueFileName);

            // Lưu file
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await image.CopyToAsync(fileStream);
            }

            // Trả về đường dẫn tương đối để lưu vào cơ sở dữ liệu
            return "/images/accommodations/" + uniqueFileName;
        }
    }
}
