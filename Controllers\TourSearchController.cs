﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class TourSearchController : Controller
    {
        private readonly ITourRepository _tourRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly ApplicationDbContext _context;

        public TourSearchController(
            ITourRepository tourRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            ApplicationDbContext context)
        {
            _tourRepository = tourRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _context = context;
        }

        // GET: TourSearch
        public async Task<IActionResult> Index(string searchTerm = null, int? locationId = null, int? cityId = null, 
            DateTime? tourDate = null, decimal? minPrice = null, decimal? maxPrice = null, int? duration = null)
        {
            // Lấy danh sách địa điểm và thành phố cho dropdown
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            
            // Lấy danh sách thời gian tour cho dropdown
            ViewBag.Durations = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "Tất cả" },
                new SelectListItem { Value = "1", Text = "1 ngày" },
                new SelectListItem { Value = "2", Text = "2 ngày" },
                new SelectListItem { Value = "3", Text = "3 ngày" },
                new SelectListItem { Value = "4", Text = "4-7 ngày" },
                new SelectListItem { Value = "8", Text = "8+ ngày" }
            };
            
            // Lấy danh sách giá tour cho dropdown
            ViewBag.PriceRanges = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "Tất cả" },
                new SelectListItem { Value = "0-500000", Text = "Dưới 500,000 VNĐ" },
                new SelectListItem { Value = "500000-1000000", Text = "500,000 - 1,000,000 VNĐ" },
                new SelectListItem { Value = "1000000-2000000", Text = "1,000,000 - 2,000,000 VNĐ" },
                new SelectListItem { Value = "2000000-3000000", Text = "2,000,000 - 3,000,000 VNĐ" },
                new SelectListItem { Value = "3000000-0", Text = "Trên 3,000,000 VNĐ" }
            };
            
            // Xử lý khoảng giá nếu được chọn từ dropdown
            if (Request.Query.ContainsKey("priceRange") && !string.IsNullOrEmpty(Request.Query["priceRange"]))
            {
                var priceRange = Request.Query["priceRange"].ToString().Split('-');
                if (priceRange.Length == 2)
                {
                    if (decimal.TryParse(priceRange[0], out var min))
                    {
                        minPrice = min;
                    }
                    
                    if (decimal.TryParse(priceRange[1], out var max) && max > 0)
                    {
                        maxPrice = max;
                    }
                }
            }
            
            // Xử lý thời gian tour nếu được chọn từ dropdown
            if (duration.HasValue)
            {
                ViewBag.SelectedDuration = duration.Value.ToString();
            }
            
            // Lưu các giá trị tìm kiếm để hiển thị lại trên form
            ViewBag.SearchTerm = searchTerm;
            ViewBag.SelectedLocationId = locationId;
            ViewBag.SelectedCityId = cityId;
            ViewBag.SelectedTourDate = tourDate?.ToString("yyyy-MM-dd");
            ViewBag.SelectedMinPrice = minPrice;
            ViewBag.SelectedMaxPrice = maxPrice;
            
            // Tìm kiếm tour
            var query = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();
                
            // Lọc theo từ khóa
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(t => t.Name.Contains(searchTerm) || 
                                        t.Description.Contains(searchTerm) ||
                                        t.Itinerary.Contains(searchTerm));
            }
            
            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(t => t.LocationId == locationId.Value);
            }
            
            // Lọc theo thành phố
            if (cityId.HasValue)
            {
                query = query.Where(t => t.CityId == cityId.Value);
            }
            
            // Lọc theo giá
            if (minPrice.HasValue)
            {
                query = query.Where(t => t.Price >= minPrice.Value);
            }
            
            if (maxPrice.HasValue)
            {
                query = query.Where(t => t.Price <= maxPrice.Value);
            }
            
            // Lọc theo thời gian tour
            if (duration.HasValue)
            {
                if (duration.Value < 8)
                {
                    // Tìm tour có thời gian chính xác
                    query = query.Where(t => t.Duration == duration.Value);
                }
                else if (duration.Value == 8)
                {
                    // Tìm tour có thời gian từ 8 ngày trở lên
                    query = query.Where(t => t.Duration >= 8);
                }
                else if (duration.Value == 4)
                {
                    // Tìm tour có thời gian từ 4-7 ngày
                    query = query.Where(t => t.Duration >= 4 && t.Duration <= 7);
                }
            }
            
            var tours = await query.ToListAsync();
            
            return View(tours);
        }
    }
}
