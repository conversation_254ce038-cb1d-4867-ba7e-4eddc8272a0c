/* Unified Styles for ViVu Travel Website
 * This file contains standardized styles to ensure consistency across all pages
 */

/* Modern Card Styles */
.modern-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 30px;
}

.modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.modern-card .card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.modern-card:hover .card-img-top {
  transform: scale(1.05);
}

.modern-card .card-body {
  padding: 1.5rem;
}

.modern-card .card-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.modern-card .card-text {
  color: #666;
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
}

/* Modern Button Styles */
.btn-modern {
  border-radius: 30px;
  padding: 0.8rem 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn-modern:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary {
  background-color: #4caf50;
  color: white;
}

.btn-modern-primary:hover {
  background-color: #3d8b40;
  color: white;
}

.btn-modern-outline {
  background-color: transparent;
  border: 2px solid #4caf50;
  color: #4caf50;
}

.btn-modern-outline:hover {
  background-color: #4caf50;
  color: white;
}

.btn-modern-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-modern-secondary:hover {
  background-color: #5a6268;
  color: white;
}

/* Section Title Styles */
.section-title {
  position: relative;
  margin-bottom: 2.5rem;
  text-align: center;
}

.section-title h2 {
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  font-size: 2.5rem;
  color: #333;
}

.section-title h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 50px;
  height: 3px;
  background-color: #4caf50;
  transform: translateX(-50%);
}

/* Form Styles */
.modern-form .form-control,
.modern-form .form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 1px solid #eee;
  box-shadow: none;
  transition: all 0.3s ease;
}

.modern-form .form-control:focus,
.modern-form .form-select:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.modern-form .input-group-text {
  border-radius: 8px 0 0 8px;
  background-color: white;
  border: 1px solid #eee;
  border-right: none;
}

/* Badge Styles */
.badge-modern {
  padding: 0.5em 1em;
  border-radius: 30px;
  font-weight: 500;
  font-size: 0.75em;
}

.badge-modern-primary {
  background-color: #4caf50;
  color: white;
}

.badge-modern-secondary {
  background-color: #6c757d;
  color: white;
}

.badge-modern-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-modern-info {
  background-color: #17a2b8;
  color: white;
}

/* Feature Box Styles */
.feature-box {
  padding: 2rem;
  border-radius: 15px;
  background-color: white;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  margin-bottom: 30px;
}

.feature-box:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.feature-box .feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  font-size: 2rem;
  transition: all 0.3s ease;
}

.feature-box:hover .feature-icon {
  background-color: #4caf50;
  color: white;
  transform: scale(1.1);
}

/* Breadcrumb Styles */
.modern-breadcrumb {
  background-color: transparent;
  padding: 1rem 0;
  margin-bottom: 2rem;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: #6c757d;
}

.modern-breadcrumb .breadcrumb-item a {
  color: #4caf50;
  text-decoration: none;
  transition: all 0.3s ease;
}

.modern-breadcrumb .breadcrumb-item a:hover {
  color: #3d8b40;
}

.modern-breadcrumb .breadcrumb-item.active {
  color: #6c757d;
}

/* Pagination Styles */
.modern-pagination .page-item .page-link {
  border: none;
  color: #4caf50;
  margin: 0 5px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modern-pagination .page-item .page-link:hover {
  background-color: rgba(76, 175, 80, 0.1);
  color: #3d8b40;
}

.modern-pagination .page-item.active .page-link {
  background-color: #4caf50;
  color: white;
}

.modern-pagination .page-item.disabled .page-link {
  color: #6c757d;
  opacity: 0.5;
}
