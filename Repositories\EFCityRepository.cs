﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFCityRepository : ICityRepository
    {
        private readonly ApplicationDbContext _context;
        
        public EFCityRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IEnumerable<City>> GetAllAsync()
        {
            return await _context.Cities
                .Include(c => c.Country)
                .ToListAsync();
        }
        
        public async Task<City> GetByIdAsync(int id)
        {
            return await _context.Cities
                .Include(c => c.Country)
                .Include(c => c.Locations)
                .Include(c => c.Accommodations)
                .FirstOrDefaultAsync(c => c.Id == id);
        }
        
        public async Task<IEnumerable<City>> GetByCountryIdAsync(int countryId)
        {
            return await _context.Cities
                .Where(c => c.CountryId == countryId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<City>> GetFeaturedAsync()
        {
            return await _context.Cities
                .Include(c => c.Country)
                .Where(c => c.IsFeatured)
                .ToListAsync();
        }
        
        public async Task AddAsync(City city)
        {
            await _context.Cities.AddAsync(city);
            await _context.SaveChangesAsync();
        }
        
        public async Task UpdateAsync(City city)
        {
            _context.Cities.Update(city);
            await _context.SaveChangesAsync();
        }
        
        public async Task DeleteAsync(int id)
        {
            var city = await _context.Cities.FindAsync(id);
            if (city != null)
            {
                _context.Cities.Remove(city);
                await _context.SaveChangesAsync();
            }
        }
    }
}
