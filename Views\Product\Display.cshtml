﻿@model ViVu.Models.Product

<div class="container mt-4">
    <h2 class="text-center">@Model.Name</h2>

    <div class="card shadow-lg p-4 mt-3">
        <div class="row">
            <!-- Hi<PERSON><PERSON> thị hình ảnh sản phẩm (nếu có) -->
            <div class="col-md-4 text-center">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" alt="Product Image" class="img-fluid rounded shadow-sm" style="max-width: 250px;">
                }
                else
                {
                    <p class="text-muted">No image available</p>
                }
            </div>

            <!-- Hi<PERSON>n thị thông tin sản phẩm -->
            <div class="col-md-8">
                <h4 class="text-primary">Price: @Model.Price.ToString("#,##0") Vnđ</h4>
                <p class="mt-3">@Model.Description</p>

                <div class="mt-4">
                    <a asp-action="Index" class="btn btn-secondary">⬅ Back to List</a>
                    <a asp-action="Update" asp-route-id="@Model.Id" class="btn btn-warning">✏ Edit</a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to delete this product?');">
                        🗑 Delete
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
