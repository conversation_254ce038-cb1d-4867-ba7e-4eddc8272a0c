@model ViVu.Models.Service
@{
    ViewData["Title"] = "Chỉnh sửa dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chỉnh sửa dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Dịch vụ</a></li>
        <li class="breadcrumb-item active">Chỉnh sửa</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Chỉnh sửa thông tin dịch vụ
        </div>
        <div class="card-body">
            <form asp-action="Edit" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên dịch vụ</label>
                            <input asp-for="Name" class="form-control" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Price" class="control-label">Giá (VNĐ)</label>
                            <input asp-for="Price" class="form-control" type="number" min="0.01" step="0.01" required />
                            <span asp-validation-for="Price" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Duration" class="control-label">Thời gian (phút)</label>
                            <input asp-for="Duration" class="form-control" />
                            <span asp-validation-for="Duration" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="LocationId" class="control-label">Địa điểm</label>
                            <select asp-for="LocationId" class="form-select" asp-items="ViewBag.Locations"></select>
                            <span asp-validation-for="LocationId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CityId" class="control-label">Thành phố</label>
                            <select asp-for="CityId" class="form-select" asp-items="ViewBag.Cities"></select>
                            <span asp-validation-for="CityId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input asp-for="IsFeatured" class="form-check-input" />
                                <label asp-for="IsFeatured" class="form-check-label">Dịch vụ nổi bật</label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="control-label">Hình ảnh hiện tại</label>
                            @if (!string.IsNullOrEmpty(Model.ImageUrl))
                            {
                                <div>
                                    <img src="@Model.ImageUrl" alt="@Model.Name" style="max-width: 200px; max-height: 150px;" class="mb-2" />
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">Chưa có hình ảnh</p>
                            }
                            <input type="file" name="imageFile" class="form-control mt-2" />
                            <small class="text-muted">Để trống nếu không muốn thay đổi hình ảnh</small>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Description" class="control-label">Mô tả ngắn</label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Details" class="control-label">Chi tiết dịch vụ</label>
                            <textarea asp-for="Details" class="form-control" rows="6"></textarea>
                            <span asp-validation-for="Details" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/disable-price-validation.js"></script>

    <script>
        // Hiển thị xem trước hình ảnh khi chọn file
        document.querySelector('input[name="imageFile"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    // Tìm hoặc tạo phần tử xem trước hình ảnh
                    let preview = document.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview mt-2';
                        preview.style.maxWidth = '200px';
                        preview.style.maxHeight = '150px';
                        e.target.parentNode.appendChild(preview);
                    }
                    preview.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
}
