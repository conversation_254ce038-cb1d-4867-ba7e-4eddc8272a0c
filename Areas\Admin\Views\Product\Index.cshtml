﻿@model IEnumerable<ViVu.Models.Product>
@{
    ViewData["Title"] = "Quản lý sản phẩm";
}
<h2 class="text-center my-4">Products</h2>

<!-- Nút "Add" -->
<div class="d-flex justify-content-end mb-3">
    <a asp-area="Admin" asp-controller="Product" asp-action="Add" class="btn btn-success">➕ Add new item</a>
</div>
<table class="table table-striped table-bordered">
    <thead class="table-dark">
        <tr>
            <th>Name</th>
            <th>Price</th>
            <th>Description</th>
            <th>Category</th>
            <th class="text-center">Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var product in Model)
        {
            <tr>
                <td>@product.Name</td>
                <td>@product.Price.ToString("#,##0") Vnđ</td> <!-- Hi<PERSON><PERSON> thị giá theo định dạng tiền tệ -->
                <td>@product.Description</td>
                <td>@(product.Category?.Name ?? "No Category")</td> <!-- <PERSON><PERSON><PERSON> tra nếu không có Category -->
                <td class="text-center">
                    <a asp-action="Display" asp-route-id="@product.Id" class="btn btn-info btn-sm">View</a>
                    <a asp-action="Update" asp-route-id="@product.Id" class="btn btn-warning btn-sm">Edit</a>
                    <a asp-action="Delete" asp-route-id="@product.Id" class="btn btn-danger btn-sm"
                       onclick="return confirm('Are you sure you want to delete this product?');">
                        Delete
                    </a>
                </td>
            </tr>
        }
    </tbody>
</table>
