﻿﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class UserPreference
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; }

        // Preferences for travel
        public bool PrefersTours { get; set; } = false;
        public bool PrefersIndependentTravel { get; set; } = false;

        // Budget preferences
        public decimal? MinBudget { get; set; }
        public decimal? MaxBudget { get; set; }

        // Duration preferences
        public int? MinDuration { get; set; }
        public int? MaxDuration { get; set; }

        // Location preferences (can be multiple, stored as comma-separated IDs)
        public string? PreferredLocationIds { get; set; }

        // Activity preferences
        public bool PrefersNature { get; set; } = false;
        public bool PrefersHistory { get; set; } = false;
        public bool PrefersFood { get; set; } = false;
        public bool PrefersAdventure { get; set; } = false;
        public bool PrefersRelaxation { get; set; } = false;

        // Accommodation preferences
        public int? PreferredStarRating { get; set; }

        // Detailed preferences
        public bool HasVisitedBenTreBefore { get; set; } = false;
        public bool HasVisitedMekongDeltaBefore { get; set; } = false;

        // Specific activity interests
        public bool InterestedInCooking { get; set; } = false;
        public bool InterestedInCrafts { get; set; } = false;
        public bool InterestedInFarming { get; set; } = false;
        public bool InterestedInBoating { get; set; } = false;
        public bool InterestedInCycling { get; set; } = false;
        public bool InterestedInFishing { get; set; } = false;

        // Dietary preferences
        public bool HasDietaryRestrictions { get; set; } = false;
        public string? DietaryRestrictions { get; set; }

        // Accessibility needs
        public string? AccessibilityNeeds { get; set; }

        // Travel companions
        public bool TravelingWithChildren { get; set; } = false;
        public bool TravelingWithElders { get; set; } = false;
        public bool TravelingAlone { get; set; } = false;
        public bool TravelingAsCouple { get; set; } = false;
        public bool TravelingWithFriends { get; set; } = false;

        // Search frequency tracking (for internal use)
        public int NatureSearchCount { get; set; } = 0;
        public int HistorySearchCount { get; set; } = 0;
        public int FoodSearchCount { get; set; } = 0;
        public int AdventureSearchCount { get; set; } = 0;
        public int RelaxationSearchCount { get; set; } = 0;

        // AI Preferences
        public bool UseAI { get; set; } = false;
        public string? AIPrompt { get; set; }

        // Last updated
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        [ForeignKey("UserId")]
        public ApplicationUser User { get; set; }
    }
}
