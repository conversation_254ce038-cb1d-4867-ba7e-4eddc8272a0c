﻿@model ViVu.Models.Booking

@{
    ViewData["Title"] = "Thanh toán";
}

<div class="container mt-4">
    <h1 class="text-center mb-4">Thanh toán đặt phòng</h1>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin khách hàng</h5>
                </div>
                <div class="card-body">
                    <form asp-action="Checkout" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="mb-3">
                            <label class="form-label">Y<PERSON>u cầu đặc biệt</label>
                            <textarea asp-for="SpecialRequests" class="form-control" rows="3" placeholder="Nhập yêu cầu đặc biệt của bạn (nếu có)..."></textarea>
                            <span asp-validation-for="SpecialRequests" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="termsCheck" required>
                            <label class="form-check-label" for="termsCheck">
                                Tôi đồng ý với <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">điều khoản và điều kiện</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle me-2"></i> Xác nhận đặt phòng
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin đặt phòng</h5>
                </div>
                <div class="card-body">
                    <p>Thông tin đặt phòng sẽ hiển thị ở đây.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Điều khoản và điều kiện</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Đặt phòng và thanh toán</h6>
                <p>Khi đặt phòng, bạn đồng ý thanh toán đầy đủ số tiền khi nhận phòng tại khách sạn. Giá phòng đã bao gồm thuế và phí dịch vụ.</p>
                
                <h6>2. Chính sách hủy đặt phòng</h6>
                <p>Bạn có thể hủy đặt phòng miễn phí trước 24 giờ so với thời gian nhận phòng. Nếu hủy sau thời gian này, bạn có thể phải trả phí tương đương với giá một đêm.</p>
                
                <h6>3. Nhận và trả phòng</h6>
                <p>Thời gian nhận phòng thông thường là từ 14:00 và trả phòng trước 12:00. Nếu bạn cần nhận phòng sớm hoặc trả phòng muộn, vui lòng liên hệ trước với khách sạn.</p>
                
                <h6>4. Trách nhiệm của khách hàng</h6>
                <p>Bạn có trách nhiệm giữ gìn tài sản trong phòng và khu vực công cộng của khách sạn. Mọi hư hỏng do bạn gây ra sẽ phải chịu chi phí sửa chữa hoặc thay thế.</p>
                
                <h6>5. Quyền riêng tư</h6>
                <p>Chúng tôi cam kết bảo vệ thông tin cá nhân của bạn và chỉ sử dụng cho mục đích đặt phòng và cung cấp dịch vụ.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Đồng ý</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
