// Vô hiệu hóa xác thực giới hạn giá
$(document).ready(function () {
  // Vô hiệu hóa tất cả các xác thực liên quan đến giá
  function disableValidation(element) {
    $(element).removeAttr("data-val-range-max");
    $(element).removeAttr("data-val-range");
    $(element).removeAttr("data-val-range-min");
    $(element).removeAttr("max");
    $(element).removeAttr("min");
    $(element).attr("data-val", "false");
  }

  // Áp dụng cho tất cả các trường input có chứa "Price" trong name hoặc id
  $('input[name*="Price"], input[id*="Price"]').each(function () {
    disableValidation(this);
  });

  // Áp dụng cho tất cả các trường input có chứa "PricePerNight" trong name hoặc id
  $('input[name*="PricePerNight"], input[id*="PricePerNight"]').each(
    function () {
      disableValidation(this);
    }
  );

  // Áp dụng cho tất cả các trường input có chứa "PricePerDay" trong name hoặc id
  $('input[name*="PricePerDay"], input[id*="PricePerDay"]').each(function () {
    disableValidation(this);
  });

  // Ghi đè phương thức xác thực của jQuery
  if ($.validator) {
    var originalMethod = $.validator.methods.range;
    $.validator.methods.range = function (value, element, param) {
      if (
        $(element).attr("name") &&
        ($(element).attr("name").indexOf("Price") !== -1 ||
          $(element).attr("name").indexOf("PricePerNight") !== -1 ||
          $(element).attr("name").indexOf("PricePerDay") !== -1)
      ) {
        return true;
      }
      return originalMethod.call(this, value, element, param);
    };
  }
});
