@model ViVu.Models.RecommendationViewModel

@{
    ViewData["Title"] = "AI Gợi Ý Lịch Trình Du Lịch";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">AI Gợi Ý Lịch Trình</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary text-white p-4 rounded-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="display-5 fw-bold">AI Gợi Ý Lịch Trình Du Lịch Bến Tre</h1>
                        <p class="fs-5">H<PERSON><PERSON> cho chúng tôi biết sở thích của bạn, AI sẽ gợi ý lịch trình du lịch phù hợp nhất!</p>
                    </div>
                    <div>
                        <a asp-action="AIFeatures" class="btn btn-outline-light me-2">
                            <i class="fas fa-robot me-2"></i>Tính năng AI
                        </a>
                        @if (User.Identity.IsAuthenticated)
                        {
                            <a asp-action="AISettings" class="btn btn-outline-light">
                                <i class="fas fa-cog me-2"></i>Cài đặt AI
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">Cho chúng tôi biết sở thích của bạn</h3>
                </div>
                <div class="card-body">
                    <form asp-action="GetRecommendations" method="post" id="recommendationForm">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <h5>Loại hình du lịch bạn thích</h5>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersTours" value="true" id="prefersTours"
                                           @(Model.UserPreference.PrefersTours ? "checked" : "")>
                                    <input type="hidden" name="UserPreference.PrefersTours" value="false" />
                                    <label class="form-check-label" for="prefersTours">Tour có hướng dẫn</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersIndependentTravel" value="true" id="prefersIndependentTravel"
                                           @(Model.UserPreference.PrefersIndependentTravel ? "checked" : "")>
                                    <input type="hidden" name="UserPreference.PrefersIndependentTravel" value="false" />
                                    <label class="form-check-label" for="prefersIndependentTravel">Du lịch tự túc</label>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Ngân sách của bạn</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Từ (VNĐ)</label>
                                        <input type="number" class="form-control" asp-for="UserPreference.MinBudget" placeholder="500,000">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Đến (VNĐ)</label>
                                        <input type="number" class="form-control" asp-for="UserPreference.MaxBudget" placeholder="2,000,000">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Thời gian du lịch (ngày)</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Tối thiểu</label>
                                        <input type="number" class="form-control" asp-for="UserPreference.MinDuration" placeholder="2">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Tối đa</label>
                                        <input type="number" class="form-control" asp-for="UserPreference.MaxDuration" placeholder="5">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Sở thích của bạn</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersNature" value="true" id="prefersNature"
                                                   @(Model.UserPreference.PrefersNature ? "checked" : "")>
                                            <input type="hidden" name="UserPreference.PrefersNature" value="false" />
                                            <label class="form-check-label" for="prefersNature">Thiên nhiên</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersHistory" value="true" id="prefersHistory"
                                                   @(Model.UserPreference.PrefersHistory ? "checked" : "")>
                                            <input type="hidden" name="UserPreference.PrefersHistory" value="false" />
                                            <label class="form-check-label" for="prefersHistory">Lịch sử, văn hóa</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersFood" value="true" id="prefersFood"
                                                   @(Model.UserPreference.PrefersFood ? "checked" : "")>
                                            <input type="hidden" name="UserPreference.PrefersFood" value="false" />
                                            <label class="form-check-label" for="prefersFood">Ẩm thực</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersAdventure" value="true" id="prefersAdventure"
                                                   @(Model.UserPreference.PrefersAdventure ? "checked" : "")>
                                            <input type="hidden" name="UserPreference.PrefersAdventure" value="false" />
                                            <label class="form-check-label" for="prefersAdventure">Mạo hiểm</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input preference-checkbox" type="checkbox" name="UserPreference.PrefersRelaxation" value="true" id="prefersRelaxation"
                                                   @(Model.UserPreference.PrefersRelaxation ? "checked" : "")>
                                            <input type="hidden" name="UserPreference.PrefersRelaxation" value="false" />
                                            <label class="form-check-label" for="prefersRelaxation">Thư giãn</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Hoạt động cụ thể bạn quan tâm</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInCooking" value="true" id="interestedInCooking">
                                            <input type="hidden" name="UserPreference.InterestedInCooking" value="false" />
                                            <label class="form-check-label" for="interestedInCooking">Nấu ăn, học làm món địa phương</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInCrafts" value="true" id="interestedInCrafts">
                                            <input type="hidden" name="UserPreference.InterestedInCrafts" value="false" />
                                            <label class="form-check-label" for="interestedInCrafts">Làm thủ công, nghề truyền thống</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInFarming" value="true" id="interestedInFarming">
                                            <input type="hidden" name="UserPreference.InterestedInFarming" value="false" />
                                            <label class="form-check-label" for="interestedInFarming">Trải nghiệm nông trại</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInBoating" value="true" id="interestedInBoating">
                                            <input type="hidden" name="UserPreference.InterestedInBoating" value="false" />
                                            <label class="form-check-label" for="interestedInBoating">Đi thuyền, khám phá sông nước</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInCycling" value="true" id="interestedInCycling">
                                            <input type="hidden" name="UserPreference.InterestedInCycling" value="false" />
                                            <label class="form-check-label" for="interestedInCycling">Đạp xe khám phá</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.InterestedInFishing" value="true" id="interestedInFishing">
                                            <input type="hidden" name="UserPreference.InterestedInFishing" value="false" />
                                            <label class="form-check-label" for="interestedInFishing">Câu cá</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Bạn đi du lịch cùng ai?</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.TravelingAlone" value="true" id="travelingAlone">
                                            <input type="hidden" name="UserPreference.TravelingAlone" value="false" />
                                            <label class="form-check-label" for="travelingAlone">Đi một mình</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.TravelingAsCouple" value="true" id="travelingAsCouple">
                                            <input type="hidden" name="UserPreference.TravelingAsCouple" value="false" />
                                            <label class="form-check-label" for="travelingAsCouple">Đi cùng người yêu/vợ chồng</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.TravelingWithFriends" value="true" id="travelingWithFriends">
                                            <input type="hidden" name="UserPreference.TravelingWithFriends" value="false" />
                                            <label class="form-check-label" for="travelingWithFriends">Đi cùng bạn bè</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.TravelingWithChildren" value="true" id="travelingWithChildren">
                                            <input type="hidden" name="UserPreference.TravelingWithChildren" value="false" />
                                            <label class="form-check-label" for="travelingWithChildren">Đi cùng trẻ em</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.TravelingWithElders" value="true" id="travelingWithElders">
                                            <input type="hidden" name="UserPreference.TravelingWithElders" value="false" />
                                            <label class="form-check-label" for="travelingWithElders">Đi cùng người lớn tuổi</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Kinh nghiệm du lịch</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.HasVisitedBenTreBefore" value="true" id="hasVisitedBenTreBefore">
                                            <input type="hidden" name="UserPreference.HasVisitedBenTreBefore" value="false" />
                                            <label class="form-check-label" for="hasVisitedBenTreBefore">Đã từng đến Bến Tre trước đây</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="UserPreference.HasVisitedMekongDeltaBefore" value="true" id="hasVisitedMekongDeltaBefore">
                                            <input type="hidden" name="UserPreference.HasVisitedMekongDeltaBefore" value="false" />
                                            <label class="form-check-label" for="hasVisitedMekongDeltaBefore">Đã từng đến Đồng bằng sông Cửu Long</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-4">
                                <h5>Tiêu chuẩn khách sạn</h5>
                                <select class="form-select" asp-for="UserPreference.PreferredStarRating">
                                    <option value="">-- Không quan trọng --</option>
                                    <option value="3">3 sao trở lên</option>
                                    <option value="4">4 sao trở lên</option>
                                    <option value="5">5 sao</option>
                                </select>
                            </div>

                            @if (User.Identity.IsAuthenticated)
                            {
                                <div class="col-md-12 mt-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="UserPreference.UseAI" value="true" id="useAI">
                                        <input type="hidden" name="UserPreference.UseAI" value="false" />
                                        <label class="form-check-label" for="useAI">Sử dụng AI để tạo gợi ý cá nhân hóa</label>
                                    </div>
                                    <div class="form-text text-muted">
                                        Khi bật tính năng này, AI sẽ tạo gợi ý du lịch dựa trên sở thích và lịch sử du lịch của bạn.
                                        <a asp-action="AISettings" class="text-decoration-none">Tùy chỉnh cài đặt AI</a>
                                    </div>
                                </div>
                            }

                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-magic me-2"></i> Gợi Ý Lịch Trình
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            @if (User.Identity.IsAuthenticated && ViewBag.RecentSearches != null)
            {
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Lịch sử tìm kiếm gần đây</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            @{
                                var recentSearches = ViewBag.RecentSearches as List<ViVu.Models.SearchHistory>;
                                if (recentSearches != null && recentSearches.Any())
                                {
                                    foreach (var search in recentSearches)
                                    {
                                        if (search != null)
                                        {
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-primary rounded-pill me-2">@search.SearchType</span>
                                                    @{
                                                        string displayText = "Tìm kiếm";
                                                        if (!string.IsNullOrEmpty(search.SearchTerm))
                                                        {
                                                            displayText = search.SearchTerm;
                                                        }
                                                        else if (search.LocationId.HasValue && search.Location != null)
                                                        {
                                                            displayText = search.Location.Name;
                                                        }
                                                    }
                                                    <span>@displayText</span>
                                                </div>
                                                <small class="text-muted">@search.SearchDate.ToString("dd/MM/yyyy")</small>
                                            </li>
                                        }
                                    }
                                }
                                else
                                {
                                    <li class="list-group-item">Không có lịch sử tìm kiếm gần đây</li>
                                }
                            }
                        </ul>
                    </div>
                </div>
            }

            @if (ViewBag.PopularLocations != null)
            {
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Địa điểm phổ biến</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @{
                                var popularLocations = ViewBag.PopularLocations as List<ViVu.Models.Location>;
                                if (popularLocations != null && popularLocations.Any())
                                {
                                    foreach (var location in popularLocations)
                                    {
                                        if (location != null)
                                        {
                                            <div class="col-md-6 mb-3">
                                                <div class="card h-100">
                                                    @if (!string.IsNullOrEmpty(location.ImageUrl))
                                                    {
                                                        <img src="@location.ImageUrl" class="card-img-top" alt="@location.Name" style="height: 120px; object-fit: cover;">
                                                    }
                                                    <div class="card-body p-2">
                                                        <h6 class="card-title mb-0">@location.Name</h6>
                                                        <p class="card-text small text-muted">@(location.City?.Name ?? "")</p>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                }
                                else
                                {
                                    <div class="col-12">
                                        <p class="text-center">Không có địa điểm phổ biến</p>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Đảm bảo ít nhất một loại hình du lịch được chọn
            $('#recommendationForm').on('submit', function(e) {
                if (!$('#prefersTours').is(':checked') && !$('#prefersIndependentTravel').is(':checked')) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một loại hình du lịch bạn thích!');
                    return;
                }

                // Gỡ lỗi - hiển thị thông tin form trước khi gửi
                console.log("Form đang được gửi với các giá trị:");

                // Log all checkbox values
                $('.preference-checkbox').each(function() {
                    var id = $(this).attr('id');
                    var name = $(this).attr('name');
                    var isChecked = $(this).is(':checked');
                    console.log(name + ": " + isChecked);
                });

                // Fix for checkbox handling - remove hidden inputs for checked checkboxes
                $('.preference-checkbox').each(function() {
                    var checkbox = $(this);
                    var name = checkbox.attr('name');
                    var isChecked = checkbox.is(':checked');

                    // Find the corresponding hidden input
                    var hiddenInput = $('input[type="hidden"][name="' + name + '"]');

                    if (hiddenInput.length > 0) {
                        if (isChecked) {
                            // If checkbox is checked, remove the hidden input completely
                            // This ensures only the checked value is sent
                            hiddenInput.remove();
                        }
                    }
                });

                // Debug form data that will be sent
                var formData = new FormData(this);
                console.log("Form data that will be sent:");
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                console.log("Form submission prepared. All preferences should be correctly set.");
            });

            // Initialize all checkboxes to ensure they're properly set on page load
            $('.preference-checkbox').each(function() {
                var checkbox = $(this);
                var isChecked = checkbox.is(':checked');
                console.log("Initial state: " + checkbox.attr('name') + " = " + isChecked);
            });
        });
    </script>
}
