@model IEnumerable<ViVu.Models.Panorama360>

@{
    ViewData["Title"] = "Du lịch 360° - " + ViewBag.Location.Name;
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Du lịch 360° - @ViewBag.Location.Name</h1>
            
            <div class="card mb-4">
                <div class="card-body">
                    <p>@ViewBag.Location.Description</p>
                    <p>
                        <a href="@Url.Action("Details", "Location", new { id = ViewBag.Location.Id })" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Quay lại địa điểm
                        </a>
                        <a href="@Url.Action("Guide", "Panorama360")" class="btn btn-info">
                            <i class="fas fa-question-circle"></i> Hướng dẫn sử dụng
                        </a>
                    </p>
                </div>
            </div>
            
            @if (!Model.Any())
            {
                <div class="alert alert-info">
                    Hiện chưa có dữ liệu panorama 360° cho địa điểm này. Vui lòng quay lại sau.
                </div>
            }
            else
            {
                <div class="row">
                    @foreach (var item in Model.Where(p => p.IsActive))
                    {
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="panorama-thumbnail" style="background-image: url('@item.ImageUrl');">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="panorama-link">
                                        <div class="panorama-overlay">
                                            <i class="fas fa-vr-cardboard fa-3x"></i>
                                            <span>Xem 360°</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">@item.Name</h5>
                                    <p class="card-text">@(item.Description.Length > 100 ? item.Description.Substring(0, 100) + "..." : item.Description)</p>
                                </div>
                                <div class="card-footer">
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id })" class="btn btn-primary btn-sm">
                                        <i class="fas fa-vr-cardboard"></i> Xem 360°
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>
