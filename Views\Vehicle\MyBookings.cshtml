@model IEnumerable<ViVu.Models.VehicleBooking>

@{
    ViewData["Title"] = "Đơn đặt phương tiện của tôi";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Bạn chưa có đơn đặt phương tiện nào.
        </div>
        
        <div class="text-center mt-4">
            <a asp-controller="Vehicle" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Tìm phương tiện ngay
            </a>
        </div>
    }
    else
    {
        <div class="card shadow-sm border-0">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead class="table-light">
                            <tr>
                                <th><PERSON><PERSON> đơn</th>
                                <th><PERSON><PERSON><PERSON> đặt</th>
                                <th><PERSON><PERSON><PERSON> b<PERSON>t đầu</th>
                                <th><PERSON><PERSON><PERSON> kết thúc</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var booking in Model.OrderByDescending(b => b.BookingDate))
                            {
                                <tr>
                                    <td>#@booking.Id</td>
                                    <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>@booking.StartDate.ToString("dd/MM/yyyy")</td>
                                    <td>@booking.EndDate.ToString("dd/MM/yyyy")</td>
                                    <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                    <td>
                                        <span class="badge @(booking.Status == VehicleBookingStatus.Pending ? "bg-warning" : 
                                                           booking.Status == VehicleBookingStatus.Confirmed ? "bg-primary" :
                                                           booking.Status == VehicleBookingStatus.Completed ? "bg-success" : "bg-danger")">
                                            @booking.Status.ToString()
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-action="BookingConfirmation" asp-route-id="@booking.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Chi tiết
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
