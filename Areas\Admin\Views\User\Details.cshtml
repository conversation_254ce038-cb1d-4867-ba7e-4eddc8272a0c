@model ViVu.Areas.Admin.Controllers.UserDetailsViewModel
@{
    ViewData["Title"] = "Chi tiết người dùng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết người dùng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Ngư<PERSON>i dùng</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-user me-1"></i>
                    Thông tin người dùng
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="display-4 rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                            @Model.User.FullName.Substring(0, 1)
                        </div>
                        <h3 class="mt-3">@Model.User.FullName</h3>
                        <div>
                            @foreach (var role in Model.Roles)
                            {
                                <span class="badge bg-primary me-1">@role</span>
                            }
                        </div>
                    </div>
                    
                    <dl class="row">
                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.User.Email</dd>
                        
                        <dt class="col-sm-4">Điện thoại:</dt>
                        <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.User.PhoneNumber) ? "Chưa cập nhật" : Model.User.PhoneNumber)</dd>
                        
                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.User.Address) ? "Chưa cập nhật" : Model.User.Address)</dd>
                        
                        <dt class="col-sm-4">Ngày tạo:</dt>
                        <dd class="col-sm-8">@Model.User.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                    
                    <div class="mt-3">
                        <a asp-action="Edit" asp-route-id="@Model.User.Id" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-calendar-check me-1"></i>
                    Lịch sử đặt phòng
                </div>
                <div class="card-body">
                    @if (Model.Bookings != null && Model.Bookings.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>Mã đơn</th>
                                        <th>Ngày đặt</th>
                                        <th>Ngày nhận phòng</th>
                                        <th>Ngày trả phòng</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var booking in Model.Bookings)
                                    {
                                        <tr>
                                            <td>@booking.Id</td>
                                            <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@booking.CheckInDate.ToString("dd/MM/yyyy")</td>
                                            <td>@booking.CheckOutDate.ToString("dd/MM/yyyy")</td>
                                            <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                            <td>
                                                @switch (booking.Status)
                                                {
                                                    case BookingStatus.Pending:
                                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                                        break;
                                                    case BookingStatus.Confirmed:
                                                        <span class="badge bg-primary">Đã xác nhận</span>
                                                        break;
                                                    case BookingStatus.Completed:
                                                        <span class="badge bg-success">Hoàn thành</span>
                                                        break;
                                                    case BookingStatus.Cancelled:
                                                        <span class="badge bg-danger">Đã hủy</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <a asp-area="Admin" asp-controller="Booking" asp-action="Details" asp-route-id="@booking.Id" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Người dùng này chưa có đơn đặt phòng nào.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
