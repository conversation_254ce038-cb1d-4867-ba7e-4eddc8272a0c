﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface IVehicleBookingRepository
    {
        Task<IEnumerable<VehicleBooking>> GetAllAsync();
        Task<VehicleBooking> GetByIdAsync(int id);
        Task<VehicleBooking> GetByIdWithDetailsAsync(int id);
        Task<IEnumerable<VehicleBooking>> GetByUserIdAsync(string userId);
        Task AddAsync(VehicleBooking vehicleBooking);
        Task UpdateAsync(VehicleBooking vehicleBooking);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<VehicleBooking>> GetRecentBookingsAsync(int count = 5);
        Task<int> GetTotalBookingsAsync();
    }
}
