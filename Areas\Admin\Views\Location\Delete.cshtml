@model ViVu.Models.Location
@{
    ViewData["Title"] = "Xóa địa điểm";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa địa điểm</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý địa điểm</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa địa điểm
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5>Bạn có chắc chắn muốn xóa địa điểm này?</h5>
                <p>Việc xóa sẽ không thể hoàn tác và có thể ảnh hưởng đến các dữ liệu liên quan.</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Tên địa điểm:</dt>
                        <dd class="col-sm-8">@Model.Name</dd>

                        <dt class="col-sm-4">Thành phố:</dt>
                        <dd class="col-sm-8">@Model.City?.Name</dd>

                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8">@Model.Address</dd>

                        <dt class="col-sm-4">Nổi bật:</dt>
                        <dd class="col-sm-8">
                            @if (Model.IsFeatured)
                            {
                                <span class="badge bg-success">Có</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Không</span>
                            }
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" style="max-height: 200px;" />
                    }
                    else
                    {
                        <img src="/images/default-location.jpg" alt="Default Image" class="img-fluid rounded" style="max-height: 200px;" />
                    }
                </div>
            </div>

            <form asp-action="Delete" class="mt-4">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">Xác nhận xóa</button>
                <a asp-action="Index" class="btn btn-secondary">Hủy</a>
            </form>
        </div>
    </div>
</div>
