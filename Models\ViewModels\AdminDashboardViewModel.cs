using System.Collections.Generic;

namespace ViVu.Models
{
    public class AdminDashboardViewModel
    {
        public IEnumerable<Booking> RecentBookings { get; set; }
        public IEnumerable<TourBooking> RecentTourBookings { get; set; }
        public IEnumerable<ServiceBooking> RecentServiceBookings { get; set; }
        public IEnumerable<VehicleBooking> RecentVehicleBookings { get; set; }
        public IEnumerable<Review> RecentReviews { get; set; }
        public IEnumerable<ApplicationUser> RecentUsers { get; set; }
    }
}
