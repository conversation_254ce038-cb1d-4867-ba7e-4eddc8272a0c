@model IEnumerable<ViVu.Models.Vehicle>

@{
    ViewData["Title"] = "Quản lý phương tiện";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-car me-1"></i>
                Danh sách phương tiện
            </div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Thêm mới
            </a>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Hình ảnh</th>
                        <th>Tên phương tiện</th>
                        <th>Loại</th>
                        <th>Biển số</th>
                        <th>Địa điểm</th>
                        <th>Giá/ngày</th>
                        <th>Trạng thái</th>
                        <th>Nổi bật</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Id</td>
                            <td>
                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                {
                                    <img src="@item.ImageUrl" alt="@item.Name" style="width: 80px; height: 60px; object-fit: cover;" />
                                }
                                else
                                {
                                    <span class="text-muted">Không có ảnh</span>
                                }
                            </td>
                            <td>@item.Name</td>
                            <td>@item.Type.ToString()</td>
                            <td>@item.LicensePlate</td>
                            <td>@item.Location?.Name, @item.City?.Name</td>
                            <td>@item.PricePerDay.ToString("N0") VNĐ</td>
                            <td>
                                @if (item.IsAvailable)
                                {
                                    <span class="badge bg-success">Có sẵn</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không có sẵn</span>
                                }
                            </td>
                            <td>
                                @if (item.IsFeatured)
                                {
                                    <span class="badge bg-success">Có</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Không</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
