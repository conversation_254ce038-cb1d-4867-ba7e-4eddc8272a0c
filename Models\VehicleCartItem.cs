﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class VehicleCartItem
    {
        public int VehicleId { get; set; }
        
        public string VehicleName { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PricePerDay { get; set; }
        
        public int Quantity { get; set; } = 1;
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        public string? ImageUrl { get; set; }
        
        public VehicleType VehicleType { get; set; }
        
        [NotMapped]
        public int NumberOfDays => (EndDate - StartDate).Days;
        
        [NotMapped]
        public decimal TotalPrice => PricePerDay * NumberOfDays * Quantity;
    }
}
