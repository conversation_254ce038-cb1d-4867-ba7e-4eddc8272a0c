﻿@page
@model RegisterModel
@{
    ViewData["Title"] = "Register";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-0 rounded-3">
                <div class="card-header bg-primary text-white text-center">
                    <h2 class="mb-0">Create a New Account</h2>
                </div>
                <div class="card-body p-4">
                    <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="Input.FullName" class="form-label">Full Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input asp-for="Input.FullName" class="form-control" autocomplete="fullname" />
                            </div>
                            <span asp-validation-for="Input.FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input asp-for="Input.Email" class="form-control" autocomplete="username" placeholder="<EMAIL>" />
                            </div>
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input asp-for="Input.Password" type="password" class="form-control" autocomplete="new-password" />
                            </div>
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.ConfirmPassword" class="form-label">Confirm Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                <input asp-for="Input.ConfirmPassword" type="password" class="form-control" autocomplete="new-password" />
                            </div>
                            <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Role" class="form-label">Role</label>
                            <select asp-for="Input.Role" asp-items="@Model.Input.RoleList" class="form-select">
                                <option disabled selected>Select Role</option>
                            </select>
                        </div>

                        <button id="registerSubmit" type="submit" class="btn btn-primary w-100 fw-bold">
                            <i class="bi bi-person-plus"></i> Register
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
