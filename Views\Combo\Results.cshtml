@model ViVu.Models.ComboViewModel

@{
    ViewData["Title"] = "Kết quả tìm kiếm combo";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Combo" asp-action="Index">Combo du lịch</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Kết quả tìm kiếm</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title mb-3"><PERSON><PERSON> lọc tìm kiếm</h4>
                    <form asp-controller="Combo" asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Tất cả địa điểm --</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Ngày đi</label>
                                <input type="date" name="startDate" class="form-control" value="@(ViewBag.StartDate?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(1).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Ngày về</label>
                                <input type="date" name="endDate" class="form-control" value="@(ViewBag.EndDate?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(3).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i> Lọc kết quả
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tours Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Tour du lịch</h2>
            <p>Tìm thấy @Model.Tours.Count() tour phù hợp với tiêu chí của bạn</p>
        </div>
    </div>

    <div class="row mb-5">
        @if (Model.Tours.Any())
        {
            foreach (var tour in Model.Tours.Take(3))
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/default/default-tour.jpg" : tour.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@tour.Name">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-primary">Tour</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@tour.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @tour.Location?.Name, @tour.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @tour.Duration ngày
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @tour.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(tour.Description?.Length > 100 ? tour.Description.Substring(0, 100) + "..." : tour.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@tour.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-3">
                <div class="alert alert-info">
                    <p>Không có tour nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
                </div>
            </div>
        }
    </div>

    <!-- Accommodations Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Khách sạn</h2>
            <p>Tìm thấy @Model.Accommodations.Count() khách sạn phù hợp với tiêu chí của bạn</p>
        </div>
    </div>

    <div class="row">
        @if (Model.Accommodations.Any())
        {
            foreach (var accommodation in Model.Accommodations.Take(3))
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm hover-lift">
                        <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default/default-hotel.jpg" : accommodation.ImageUrl)"
                             class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title mb-0">@accommodation.Name</h5>
                                <div>
                                    @for (int i = 0; i < accommodation.StarRating; i++)
                                    {
                                        <i class="bi bi-star-fill text-warning"></i>
                                    }
                                </div>
                            </div>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @accommodation.Location?.Name, @accommodation.City?.Name
                            </p>
                            <p class="card-text">@(accommodation.Description?.Length > 100 ? accommodation.Description.Substring(0, 100) + "..." : accommodation.Description)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-3">
                <div class="alert alert-info">
                    <p>Không có khách sạn nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
                </div>
            </div>
        }
    </div>

    <!-- Combo Suggestions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body p-4 text-center">
                    <h3>Tạo combo du lịch tiết kiệm</h3>
                    <p>Kết hợp tour và khách sạn để nhận ưu đãi đặc biệt!</p>
                    <a href="#" class="btn btn-primary">Tạo combo ngay</a>
                </div>
            </div>
        </div>
    </div>
</div>
