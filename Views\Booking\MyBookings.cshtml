@model IEnumerable<ViVu.Models.Booking>

@{
    ViewData["Title"] = "L<PERSON>ch sử đặt phòng";
}

<div class="container mt-4">
    <h1 class="text-center mb-4"><PERSON><PERSON><PERSON> sử đặt phòng</h1>

    @if (!Model.Any())
    {
        <div class="alert alert-info text-center">
            <p class="mb-0">Bạn chưa có đặt phòng nào.</p>
            <a asp-controller="Home" asp-action="Index" class="btn btn-primary mt-3">Tìm kiếm khách sạn</a>
        </div>
    }
    else
    {
        <div class="row">
            @foreach (var booking in Model)
            {
                <div class="col-md-12 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Mã đặt phòng: #@booking.Id</h5>
                            <span>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    @foreach (var detail in booking.BookingDetails)
                                    {
                                        <div class="mb-3 pb-3 border-bottom">
                                            <div class="d-flex justify-content-between">
                                                <h6>@detail.Room.Accommodation.Name</h6>
                                                <span class="badge bg-success">@(detail.Status)</span>
                                            </div>
                                            <p class="mb-1">@detail.Room.Name - @detail.NumberOfRooms phòng</p>
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>Nhận phòng:</small>
                                                <small>@detail.CheckInDate.ToString("dd/MM/yyyy")</small>
                                            </div>
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>Trả phòng:</small>
                                                <small>@detail.CheckOutDate.ToString("dd/MM/yyyy")</small>
                                            </div>
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>Số đêm:</small>
                                                <small>@((detail.CheckOutDate - detail.CheckInDate).Days) đêm</small>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <small>Tổng tiền:</small>
                                                <small class="fw-bold">@detail.TotalPrice.ToString("#,##0") VNĐ</small>
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">Thông tin thanh toán</h6>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Tổng tiền:</span>
                                                <span class="fw-bold">@booking.TotalPrice.ToString("#,##0") VNĐ</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Phương thức:</span>
                                                <span>Thanh toán tại khách sạn</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Trạng thái:</span>
                                                @switch (booking.Status)
                                                {
                                                    case BookingStatus.Pending:
                                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                                        break;
                                                    case BookingStatus.Confirmed:
                                                        <span class="badge bg-success">Đã xác nhận</span>
                                                        break;
                                                    case BookingStatus.Cancelled:
                                                        <span class="badge bg-danger">Đã hủy</span>
                                                        break;
                                                    case BookingStatus.Completed:
                                                        <span class="badge bg-info">Đã hoàn thành</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@booking.Status</span>
                                                        break;
                                                }
                                            </div>
                                            
                                            @if (!string.IsNullOrEmpty(booking.SpecialRequests))
                                            {
                                                <div class="mt-3">
                                                    <h6>Yêu cầu đặc biệt:</h6>
                                                    <p class="small">@booking.SpecialRequests</p>
                                                </div>
                                            }
                                            
                                            <div class="mt-3">
                                                <a asp-action="Details" asp-route-id="@booking.Id" class="btn btn-outline-primary btn-sm w-100 mb-2">
                                                    <i class="bi bi-info-circle"></i> Xem chi tiết
                                                </a>
                                                @if (booking.Status == BookingStatus.Pending || booking.Status == BookingStatus.Confirmed)
                                                {
                                                    <form asp-action="Cancel" method="post" onsubmit="return confirm('Bạn có chắc chắn muốn hủy đặt phòng này?');">
                                                        <input type="hidden" name="id" value="@booking.Id" />
                                                        <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                                            <i class="bi bi-x-circle"></i> Hủy đặt phòng
                                                        </button>
                                                    </form>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
