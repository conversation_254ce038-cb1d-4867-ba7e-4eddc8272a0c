using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Data;
using ViVu.Models;
using System.Threading.Tasks;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class RoomController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public RoomController(ApplicationDbContext context, IWebHostEnvironment hostEnvironment)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Room
        public async Task<IActionResult> Index()
        {
            var rooms = await _context.Rooms
                .Include(r => r.Accommodation)
                .ToListAsync();

            return View(rooms);
        }

        // GET: Admin/Room/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.Accommodation)
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (room == null)
            {
                return NotFound();
            }

            return View(room);
        }

        // GET: Admin/Room/Create
        public async Task<IActionResult> Create(int? accommodationId)
        {
            ViewBag.Accommodations = new SelectList(await _context.Accommodations.ToListAsync(), "Id", "Name", accommodationId);

            // Lấy danh sách tất cả tiện nghi
            var allAmenities = await _context.Amenities.ToListAsync();

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = false
            }).ToList();

            var room = new Room();
            if (accommodationId.HasValue)
            {
                room.AccommodationId = accommodationId.Value;
            }

            return View(room);
        }

        // POST: Admin/Room/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Room room, IFormFile imageFile, int[] selectedAmenities)
        {
            // Bỏ qua xác thực ModelState cho trường PricePerNight
            if (room.PricePerNight > 0)
            {
                ModelState.Remove("PricePerNight");
            }

            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    room.ImageUrl = await SaveImage(imageFile);
                }

                // Thêm phòng vào cơ sở dữ liệu
                _context.Add(room);
                await _context.SaveChangesAsync();

                // Thêm tiện nghi cho phòng
                if (selectedAmenities != null && selectedAmenities.Length > 0)
                {
                    foreach (var amenityId in selectedAmenities)
                    {
                        _context.RoomAmenities.Add(new RoomAmenity
                        {
                            RoomId = room.Id,
                            AmenityId = amenityId
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                return RedirectToAction(nameof(Index));
            }

            ViewBag.Accommodations = new SelectList(await _context.Accommodations.ToListAsync(), "Id", "Name", room.AccommodationId);

            // Lấy danh sách tất cả tiện nghi và đánh dấu những tiện nghi đã được chọn
            var allAmenities = await _context.Amenities.ToListAsync();
            var selectedAmenityIds = selectedAmenities ?? new int[0];

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = selectedAmenityIds.Contains(a.Id)
            }).ToList();

            return View(room);
        }

        // GET: Admin/Room/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.RoomAmenities)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (room == null)
            {
                return NotFound();
            }

            ViewBag.Accommodations = new SelectList(await _context.Accommodations.ToListAsync(), "Id", "Name", room.AccommodationId);

            // Lấy danh sách tất cả tiện nghi và đánh dấu những tiện nghi đã được chọn
            var allAmenities = await _context.Amenities.ToListAsync();
            var selectedAmenityIds = room.RoomAmenities.Select(ra => ra.AmenityId).ToList();

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = selectedAmenityIds.Contains(a.Id)
            }).ToList();

            return View(room);
        }

        // POST: Admin/Room/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Room room, IFormFile imageFile, int[] selectedAmenities)
        {
            if (id != room.Id)
            {
                return NotFound();
            }

            // Bỏ qua xác thực ModelState cho trường PricePerNight
            if (room.PricePerNight > 0)
            {
                ModelState.Remove("PricePerNight");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Lấy thông tin phòng hiện tại từ cơ sở dữ liệu
                    var existingRoom = await _context.Rooms
                        .Include(r => r.RoomAmenities)
                        .FirstOrDefaultAsync(r => r.Id == id);

                    if (existingRoom == null)
                    {
                        return NotFound();
                    }

                    // Cập nhật thông tin phòng
                    existingRoom.Name = room.Name;
                    existingRoom.Description = room.Description;
                    existingRoom.PricePerNight = room.PricePerNight;
                    existingRoom.MaxOccupancy = room.MaxOccupancy;
                    existingRoom.IsAvailable = room.IsAvailable;
                    existingRoom.AccommodationId = room.AccommodationId;

                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        existingRoom.ImageUrl = await SaveImage(imageFile);
                    }

                    // Cập nhật tiện nghi
                    // Xóa tất cả các tiện nghi hiện tại
                    var currentAmenities = await _context.RoomAmenities
                        .Where(ra => ra.RoomId == id)
                        .ToListAsync();

                    _context.RoomAmenities.RemoveRange(currentAmenities);

                    // Thêm lại các tiện nghi đã chọn
                    if (selectedAmenities != null && selectedAmenities.Length > 0)
                    {
                        foreach (var amenityId in selectedAmenities)
                        {
                            _context.RoomAmenities.Add(new RoomAmenity
                            {
                                RoomId = id,
                                AmenityId = amenityId
                            });
                        }
                    }

                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RoomExists(room.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Accommodations = new SelectList(await _context.Accommodations.ToListAsync(), "Id", "Name", room.AccommodationId);

            // Lấy danh sách tất cả tiện nghi và đánh dấu những tiện nghi đã được chọn
            var allAmenities = await _context.Amenities.ToListAsync();
            var selectedAmenityIds = selectedAmenities ?? new int[0];

            ViewBag.Amenities = allAmenities.Select(a => new
            {
                Amenity = a,
                IsSelected = selectedAmenityIds.Contains(a.Id)
            }).ToList();

            return View(room);
        }

        // GET: Admin/Room/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.Accommodation)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (room == null)
            {
                return NotFound();
            }

            return View(room);
        }

        // POST: Admin/Room/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return NotFound();
            }

            // Kiểm tra xem có đơn đặt phòng nào liên quan đến phòng này không
            var hasBookings = await _context.BookingDetails.AnyAsync(bd => bd.RoomId == id);
            if (hasBookings)
            {
                ModelState.AddModelError("", "Không thể xóa phòng này vì có đơn đặt phòng liên quan.");

                // Lấy lại thông tin phòng để hiển thị
                room = await _context.Rooms
                    .Include(r => r.Accommodation)
                    .FirstOrDefaultAsync(r => r.Id == id);

                return View(room);
            }

            // Xóa các liên kết với tiện nghi
            var roomAmenities = await _context.RoomAmenities
                .Where(ra => ra.RoomId == id)
                .ToListAsync();

            _context.RoomAmenities.RemoveRange(roomAmenities);

            // Xóa phòng
            _context.Rooms.Remove(room);
            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        private bool RoomExists(int id)
        {
            return _context.Rooms.Any(e => e.Id == id);
        }

        // Lưu hình ảnh vào thư mục wwwroot/images/rooms
        private async Task<string> SaveImage(IFormFile image)
        {
            // Tạo tên file duy nhất để tránh trùng lặp
            string uniqueFileName = Guid.NewGuid().ToString() + "_" + image.FileName;

            // Tạo đường dẫn đến thư mục lưu trữ
            string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "rooms");

            // Tạo thư mục nếu chưa tồn tại
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            // Tạo đường dẫn đầy đủ đến file
            string filePath = Path.Combine(uploadsFolder, uniqueFileName);

            // Lưu file
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await image.CopyToAsync(fileStream);
            }

            // Trả về đường dẫn tương đối để lưu vào cơ sở dữ liệu
            return "/images/rooms/" + uniqueFileName;
        }
    }
}
