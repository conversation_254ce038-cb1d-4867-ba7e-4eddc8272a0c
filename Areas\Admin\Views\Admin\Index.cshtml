@model ViVu.Models.AdminDashboardViewModel
@using ViVu.Models
@using static ViVu.Models.Booking
@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="container-fluid">
    <h1 class="mt-4">Dashboard</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">Dashboard</li>
    </ol>

    <!-- Thống kê tổng quan -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalUsers</h4>
                    <div>Người dùng</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="User" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalAccommodations</h4>
                    <div>Chỗ ở</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Accommodation" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalTours</h4>
                    <div>Tour</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Tour" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalRooms</h4>
                    <div>Phòng</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Room" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalServices</h4>
                    <div>Dịch vụ</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Service" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalVehicles</h4>
                    <div>Phương tiện</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Vehicle" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalBookings</h4>
                    <div>Đặt phòng</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="Booking" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalTourBookings</h4>
                    <div>Đặt tour</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="TourBooking" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-dark text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalServiceBookings</h4>
                    <div>Đặt dịch vụ</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="ServiceBooking" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <h4>@ViewBag.TotalVehicleBookings</h4>
                    <div>Đặt phương tiện</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" asp-area="Admin" asp-controller="VehicleBooking" asp-action="Index">Xem chi tiết</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê đặt phòng theo trạng thái -->
    <div class="row">
        <div class="col-xl-3">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Thống kê đặt phòng theo trạng thái
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.PendingBookings</div>
                            <div class="small text-muted">Chờ xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.ConfirmedBookings</div>
                            <div class="small text-muted">Đã xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CompletedBookings</div>
                            <div class="small text-muted">Hoàn thành</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CancelledBookings</div>
                            <div class="small text-muted">Đã hủy</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Thống kê đặt tour theo trạng thái
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.PendingTourBookings</div>
                            <div class="small text-muted">Chờ xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.ConfirmedTourBookings</div>
                            <div class="small text-muted">Đã xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CompletedTourBookings</div>
                            <div class="small text-muted">Hoàn thành</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CancelledTourBookings</div>
                            <div class="small text-muted">Đã hủy</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Thống kê đặt dịch vụ theo trạng thái
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.PendingServiceBookings</div>
                            <div class="small text-muted">Chờ xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.ConfirmedServiceBookings</div>
                            <div class="small text-muted">Đã xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CompletedServiceBookings</div>
                            <div class="small text-muted">Hoàn thành</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CancelledServiceBookings</div>
                            <div class="small text-muted">Đã hủy</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Thống kê đặt phương tiện theo trạng thái
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.PendingVehicleBookings</div>
                            <div class="small text-muted">Chờ xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.ConfirmedVehicleBookings</div>
                            <div class="small text-muted">Đã xác nhận</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CompletedVehicleBookings</div>
                            <div class="small text-muted">Hoàn thành</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5">@ViewBag.CancelledVehicleBookings</div>
                            <div class="small text-muted">Đã hủy</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Đơn đặt phòng mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Đơn đặt phòng mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Khách hàng</th>
                            <th>Ngày đặt</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var booking in Model.RecentBookings)
                        {
                            <tr>
                                <td>@booking.Id</td>
                                <td>@booking.ApplicationUser.FullName</td>
                                <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (booking.Status)
                                    {
                                        case BookingStatus.Pending:
                                            <span class="badge bg-warning">Chờ xác nhận</span>
                                            break;
                                        case BookingStatus.Confirmed:
                                            <span class="badge bg-primary">Đã xác nhận</span>
                                            break;
                                        case BookingStatus.Completed:
                                            <span class="badge bg-success">Hoàn thành</span>
                                            break;
                                        case BookingStatus.Cancelled:
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <a asp-area="Admin" asp-controller="Booking" asp-action="Details" asp-route-id="@booking.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="Booking" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>

    <!-- Đơn đặt tour mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Đơn đặt tour mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Khách hàng</th>
                            <th>Ngày đặt</th>
                            <th>Ngày khởi hành</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var booking in Model.RecentTourBookings)
                        {
                            <tr>
                                <td>@booking.Id</td>
                                <td>@booking.ApplicationUser.FullName</td>
                                <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@booking.TourDate.ToString("dd/MM/yyyy")</td>
                                <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (booking.Status)
                                    {
                                        case TourBookingStatus.Pending:
                                            <span class="badge bg-warning">Chờ xác nhận</span>
                                            break;
                                        case TourBookingStatus.Confirmed:
                                            <span class="badge bg-primary">Đã xác nhận</span>
                                            break;
                                        case TourBookingStatus.Completed:
                                            <span class="badge bg-success">Hoàn thành</span>
                                            break;
                                        case TourBookingStatus.Cancelled:
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <a asp-area="Admin" asp-controller="TourBooking" asp-action="Details" asp-route-id="@booking.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="TourBooking" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>

    <!-- Đơn đặt dịch vụ mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Đơn đặt dịch vụ mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Khách hàng</th>
                            <th>Ngày đặt</th>
                            <th>Ngày sử dụng</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var booking in Model.RecentServiceBookings)
                        {
                            <tr>
                                <td>@booking.Id</td>
                                <td>@booking.ApplicationUser.FullName</td>
                                <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@booking.ServiceDate.ToString("dd/MM/yyyy")</td>
                                <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (booking.Status)
                                    {
                                        case ServiceBookingStatus.Pending:
                                            <span class="badge bg-warning">Chờ xác nhận</span>
                                            break;
                                        case ServiceBookingStatus.Confirmed:
                                            <span class="badge bg-primary">Đã xác nhận</span>
                                            break;
                                        case ServiceBookingStatus.Completed:
                                            <span class="badge bg-success">Hoàn thành</span>
                                            break;
                                        case ServiceBookingStatus.Cancelled:
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <a asp-area="Admin" asp-controller="ServiceBooking" asp-action="Details" asp-route-id="@booking.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="ServiceBooking" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>

    <!-- Đơn đặt phương tiện mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Đơn đặt phương tiện mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Khách hàng</th>
                            <th>Ngày đặt</th>
                            <th>Ngày bắt đầu</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var booking in Model.RecentVehicleBookings)
                        {
                            <tr>
                                <td>@booking.Id</td>
                                <td>@booking.ApplicationUser.FullName</td>
                                <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@booking.StartDate.ToString("dd/MM/yyyy")</td>
                                <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (booking.Status)
                                    {
                                        case VehicleBookingStatus.Pending:
                                            <span class="badge bg-warning">Chờ xác nhận</span>
                                            break;
                                        case VehicleBookingStatus.Confirmed:
                                            <span class="badge bg-primary">Đã xác nhận</span>
                                            break;
                                        case VehicleBookingStatus.Completed:
                                            <span class="badge bg-success">Hoàn thành</span>
                                            break;
                                        case VehicleBookingStatus.Cancelled:
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <a asp-area="Admin" asp-controller="VehicleBooking" asp-action="Details" asp-route-id="@booking.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="VehicleBooking" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>

    <!-- Đánh giá mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-star me-1"></i>
            Đánh giá mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Người dùng</th>
                            <th>Đối tượng</th>
                            <th>Đánh giá</th>
                            <th>Nhận xét</th>
                            <th>Ngày đánh giá</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var review in Model.RecentReviews)
                        {
                            string itemName = "";
                            string itemType = "";

                            if (review.AccommodationId.HasValue && review.Accommodation != null)
                            {
                                itemName = review.Accommodation.Name;
                                itemType = "Chỗ ở";
                            }
                            else if (review.TourId.HasValue && review.Tour != null)
                            {
                                itemName = review.Tour.Name;
                                itemType = "Tour";
                            }
                            else if (review.ServiceId.HasValue && review.Service != null)
                            {
                                itemName = review.Service.Name;
                                itemType = "Dịch vụ";
                            }
                            else if (review.VehicleId.HasValue && review.Vehicle != null)
                            {
                                itemName = review.Vehicle.Name;
                                itemType = "Phương tiện";
                            }

                            <tr>
                                <td>@review.User?.FullName</td>
                                <td>
                                    <span class="badge bg-info">@itemType</span>
                                    @itemName
                                </td>
                                <td>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= review.Rating)
                                            {
                                                <i class="fas fa-star"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star"></i>
                                            }
                                        }
                                    </div>
                                </td>
                                <td>@(review.Comment.Length > 50 ? review.Comment.Substring(0, 50) + "..." : review.Comment)</td>
                                <td>@review.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="Review" asp-action="Details" asp-route-id="@review.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="Review" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>

    <!-- Người dùng mới nhất -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users me-1"></i>
            Người dùng mới nhất
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Họ tên</th>
                            <th>Email</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.RecentUsers)
                        {
                            <tr>
                                <td>@user.FullName</td>
                                <td>@user.Email</td>
                                <td>@user.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="User" asp-action="Details" asp-route-id="@user.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="text-end mt-2">
                <a asp-area="Admin" asp-controller="User" asp-action="Index" class="btn btn-primary">Xem tất cả</a>
            </div>
        </div>
    </div>
</div>
