@model ViVu.Models.ServiceBooking
@{
    ViewData["Title"] = "Cập nhật đặt dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Cập nhật đặt dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Đặt dịch vụ</a></li>
        <li class="breadcrumb-item active">Cập nhật</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Cập nhật trạng thái đặt dịch vụ #@Model.Id
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Thông tin khách hàng</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Họ tên:</th>
                            <td>@Model.ApplicationUser.FullName</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>@Model.ApplicationUser.Email</td>
                        </tr>
                        <tr>
                            <th>Địa chỉ:</th>
                            <td>@(Model.ApplicationUser.Address ?? "Không có")</td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-md-6">
                    <h5>Thông tin đặt dịch vụ</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Mã đặt dịch vụ:</th>
                            <td>#@Model.Id</td>
                        </tr>
                        <tr>
                            <th>Ngày đặt:</th>
                            <td>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                        </tr>
                        <tr>
                            <th>Ngày sử dụng:</th>
                            <td>@Model.ServiceDate.ToString("dd/MM/yyyy")</td>
                        </tr>
                        <tr>
                            <th>Tổng tiền:</th>
                            <td>@Model.TotalPrice.ToString("N0") VNĐ</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <h5>Chi tiết dịch vụ đã đặt</h5>
            <div class="table-responsive mb-4">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Dịch vụ</th>
                            <th>Địa điểm</th>
                            <th>Số lượng</th>
                            <th>Đơn giá</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.ServiceBookingDetails)
                        {
                            <tr>
                                <td>@detail.Service.Name</td>
                                <td>@detail.Service.Location?.Name, @detail.Service.City?.Name</td>
                                <td>@detail.Quantity</td>
                                <td>@detail.Price.ToString("N0") VNĐ</td>
                                <td>@detail.TotalPrice.ToString("N0") VNĐ</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">Tổng cộng:</th>
                            <th>@Model.TotalPrice.ToString("N0") VNĐ</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <form asp-action="Edit" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Id" />
                <input type="hidden" asp-for="UserId" />
                <input type="hidden" asp-for="BookingDate" />
                <input type="hidden" asp-for="ServiceDate" />
                <input type="hidden" asp-for="TotalPrice" />
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Status" class="control-label">Trạng thái</label>
                            <select asp-for="Status" class="form-select">
                                <option value="@ServiceBookingStatus.Pending">Chờ xác nhận</option>
                                <option value="@ServiceBookingStatus.Confirmed">Đã xác nhận</option>
                                <option value="@ServiceBookingStatus.Completed">Hoàn thành</option>
                                <option value="@ServiceBookingStatus.Cancelled">Đã hủy</option>
                            </select>
                            <span asp-validation-for="Status" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="SpecialRequests" class="control-label">Yêu cầu đặc biệt</label>
                            <textarea asp-for="SpecialRequests" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="SpecialRequests" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
