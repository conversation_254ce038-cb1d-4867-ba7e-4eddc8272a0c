@model ViVu.Models.Tour

@{
    ViewData["Title"] = "Xóa tour";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý tour</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Xác nhận xóa
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><PERSON>ạn có chắc chắn muốn xóa tour này?</h5>
                <p>Hành động này không thể hoàn tác.</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">@Model.Name</h5>
                    
                    <div class="mb-3">
                        <strong>Địa điểm:</strong>
                        <span>@(Model.Location != null ? Model.Location.Name : ""), @(Model.City != null ? Model.City.Name : "")</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Thời gian:</strong>
                        <span>@Model.Duration ngày</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Giá:</strong>
                        <span>@Model.Price.ToString("N0") VNĐ</span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" style="max-height: 200px; object-fit: cover;" />
                    }
                </div>
            </div>
            
            <form asp-action="Delete" method="post" class="mt-4">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
