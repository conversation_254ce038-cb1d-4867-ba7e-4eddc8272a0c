@model IEnumerable<ViVu.Models.Vehicle>

@{
    ViewData["Title"] = "Thuê phương tiện";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">T<PERSON><PERSON> kiếm phương tiện</h5>
        </div>
        <div class="card-body">
            <form method="get" action="@Url.Action("Index", "Vehicle")">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Từ khóa</label>
                            <input type="text" name="searchTerm" class="form-control" placeholder="Tên phương tiện, mô tả..." value="@ViewBag.SearchTerm" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Đ<PERSON><PERSON> điểm</label>
                            <select name="locationId" class="form-select">
                                <option value="">Tất cả địa điểm</option>
                                @foreach (var location in ViewBag.Locations)
                                {
                                    <option value="@location.Value" selected="@(ViewBag.LocationId == int.Parse(location.Value))">@location.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Thành phố</label>
                            <select name="cityId" class="form-select">
                                <option value="">Tất cả thành phố</option>
                                @foreach (var city in ViewBag.Cities)
                                {
                                    <option value="@city.Value" selected="@(ViewBag.CityId == int.Parse(city.Value))">@city.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Ngày bắt đầu</label>
                            <input type="date" name="startDate" class="form-control" value="@ViewBag.StartDate.ToString("yyyy-MM-dd")" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Ngày kết thúc</label>
                            <input type="date" name="endDate" class="form-control" value="@ViewBag.EndDate.ToString("yyyy-MM-dd")" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Loại phương tiện</label>
                            <select name="vehicleType" class="form-select">
                                <option value="">Tất cả loại</option>
                                @foreach (var type in ViewBag.VehicleTypes)
                                {
                                    <option value="@type.Value" selected="@(ViewBag.VehicleType != null && (int)ViewBag.VehicleType == int.Parse(type.Value))">@type.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Giá (VNĐ/ngày)</label>
                            <div class="input-group">
                                <input type="number" name="minPrice" class="form-control" placeholder="Tối thiểu" value="@ViewBag.MinPrice" />
                                <span class="input-group-text">-</span>
                                <input type="number" name="maxPrice" class="form-control" placeholder="Tối đa" value="@ViewBag.MaxPrice" />
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>Tìm kiếm
                        </button>
                        <a href="@Url.Action("Index", "Vehicle")" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>Xóa bộ lọc
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>Không tìm thấy phương tiện nào phù hợp với tiêu chí tìm kiếm của bạn.
        </div>
    }
    else
    {
        <div class="row row-cols-1 row-cols-md-3 g-4">
            @foreach (var vehicle in Model)
            {
                <div class="col">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            @if (!string.IsNullOrEmpty(vehicle.ImageUrl))
                            {
                                <img src="@vehicle.ImageUrl" class="card-img-top" alt="@vehicle.Name" style="height: 200px; object-fit: cover;">
                            }
                            else
                            {
                                <img src="/images/no-image.jpg" class="card-img-top" alt="No Image" style="height: 200px; object-fit: cover;">
                            }

                            @if (vehicle.IsFeatured)
                            {
                                <span class="position-absolute top-0 end-0 badge bg-warning m-2">Nổi bật</span>
                            }

                            <span class="position-absolute top-0 start-0 badge bg-primary m-2">@vehicle.Type.ToString()</span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">@vehicle.Name</h5>
                            <p class="card-text text-muted">
                                <i class="bi bi-geo-alt"></i> @vehicle.Location?.Name, @vehicle.City?.Name
                            </p>
                            <p class="card-text">
                                @if (vehicle.Description.Length > 100)
                                {
                                    @(vehicle.Description.Substring(0, 100) + "...")
                                }
                                else
                                {
                                    @vehicle.Description
                                }
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="text-primary fw-bold">@vehicle.PricePerDay.ToString("#,##0") VNĐ</span>
                                    <small class="text-muted">/ ngày</small>
                                </div>
                                <a href="@Url.Action("Details", "Vehicle", new { id = vehicle.Id, startDate = ViewBag.StartDate.ToString("yyyy-MM-dd"), endDate = ViewBag.EndDate.ToString("yyyy-MM-dd") })" class="btn btn-outline-primary">
                                    <i class="bi bi-info-circle"></i> Chi tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
