﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFTourRepository : ITourRepository
    {
        private readonly ApplicationDbContext _context;

        public EFTourRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Tour>> GetAllAsync()
        {
            return await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .ToListAsync();
        }

        public async Task<Tour> GetByIdAsync(int id)
        {
            return await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<Tour> GetByIdWithDetailsAsync(int id)
        {
            return await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .Include(t => t.Images)
                .Include(t => t.Reviews)
                    .ThenInclude(r => r.User)
                .Include(t => t.TourAmenities)
                    .ThenInclude(ta => ta.Amenity)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<IEnumerable<Tour>> GetFeaturedToursAsync(int count = 6)
        {
            return await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .Where(t => t.IsFeatured)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<Tour>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null,
            DateTime? tourDate = null, decimal? minPrice = null, decimal? maxPrice = null)
        {
            var query = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Tìm theo tên hoặc mô tả
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(t => t.Name.Contains(searchTerm) ||
                                        t.Description.Contains(searchTerm) ||
                                        t.Itinerary.Contains(searchTerm));
            }

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(t => t.LocationId == locationId.Value);
            }

            // Lọc theo thành phố
            if (cityId.HasValue)
            {
                query = query.Where(t => t.CityId == cityId.Value);
            }

            // Lọc theo giá
            if (minPrice.HasValue)
            {
                query = query.Where(t => t.Price >= minPrice.Value);
            }

            if (maxPrice.HasValue)
            {
                query = query.Where(t => t.Price <= maxPrice.Value);
            }

            return await query.ToListAsync();
        }

        public async Task AddAsync(Tour tour)
        {
            _context.Tours.Add(tour);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Tour tour)
        {
            _context.Tours.Update(tour);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var tour = await _context.Tours.FindAsync(id);
            if (tour != null)
            {
                _context.Tours.Remove(tour);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Tours.AnyAsync(t => t.Id == id);
        }
    }
}
