﻿﻿using ViVu.Models;

namespace ViVu.Repositories
{
    public interface ICityRepository
    {
        Task<IEnumerable<City>> GetAllAsync();
        Task<City> GetByIdAsync(int id);
        Task<IEnumerable<City>> GetByCountryIdAsync(int countryId);
        Task<IEnumerable<City>> GetFeaturedAsync();
        Task AddAsync(City city);
        Task UpdateAsync(City city);
        Task DeleteAsync(int id);
    }
}
