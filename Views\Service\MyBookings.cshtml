@model IEnumerable<ViVu.Models.ServiceBooking>
@{
    ViewData["Title"] = "Đơn đặt dịch vụ của tôi";
}

<div class="container my-5">
    <h1 class="mb-4">Đơn đặt dịch vụ của tôi</h1>
    
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i> Bạn chưa có đơn đặt dịch vụ nào.
        </div>
        
        <div class="text-center mt-4">
            <a asp-action="Index" class="btn btn-primary">
                <i class="bi bi-search"></i> Tìm dịch vụ ngay
            </a>
        </div>
    }
    else
    {
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Danh sách đơn đặt dịch vụ</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Mã đơn</th>
                                <th>Ngày đặt</th>
                                <th>Ngày sử dụng</th>
                                <th>Dịch vụ</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var booking in Model)
                            {
                                <tr>
                                    <td>#@booking.Id</td>
                                    <td>@booking.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>@booking.ServiceDate.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        @if (booking.ServiceBookingDetails != null && booking.ServiceBookingDetails.Any())
                                        {
                                            var detail = booking.ServiceBookingDetails.First();
                                            <span>@detail.Service.Name</span>
                                            
                                            if (booking.ServiceBookingDetails.Count > 1)
                                            {
                                                <span class="text-muted"> và @(booking.ServiceBookingDetails.Count - 1) dịch vụ khác</span>
                                            }
                                        }
                                    </td>
                                    <td>@booking.TotalPrice.ToString("N0") VNĐ</td>
                                    <td>
                                        @switch (booking.Status)
                                        {
                                            case ServiceBookingStatus.Pending:
                                                <span class="badge bg-warning">Chờ xác nhận</span>
                                                break;
                                            case ServiceBookingStatus.Confirmed:
                                                <span class="badge bg-primary">Đã xác nhận</span>
                                                break;
                                            case ServiceBookingStatus.Completed:
                                                <span class="badge bg-success">Hoàn thành</span>
                                                break;
                                            case ServiceBookingStatus.Cancelled:
                                                <span class="badge bg-danger">Đã hủy</span>
                                                break;
                                        }
                                    </td>
                                    <td>
                                        <a asp-action="BookingConfirmation" asp-route-id="@booking.Id" class="btn btn-sm btn-info">
                                            <i class="bi bi-info-circle"></i>
                                        </a>
                                        
                                        @if (booking.Status == ServiceBookingStatus.Pending || booking.Status == ServiceBookingStatus.Confirmed)
                                        {
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                            
                                            <!-- Modal xác nhận hủy đơn -->
                                            <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="<EMAIL>">Xác nhận hủy đơn</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Bạn có chắc chắn muốn hủy đơn đặt dịch vụ #@booking.Id không?</p>
                                                            <p class="text-danger">Lưu ý: Hành động này không thể hoàn tác.</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                                                            <form asp-action="CancelBooking" method="post">
                                                                <input type="hidden" name="id" value="@booking.Id" />
                                                                <button type="submit" class="btn btn-danger">Xác nhận hủy</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
