@model IEnumerable<ViVu.Models.VehicleBooking>

@{
    ViewData["Title"] = "Quản lý đặt phương tiện";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-list me-1"></i>
                Danh sách đơn đặt phương tiện
            </div>
            <div>
                <form method="get" class="d-flex">
                    <select name="status" class="form-select me-2" onchange="this.form.submit()">
                        <option value="">Tất cả trạng thái</option>
                        @foreach (var status in ViewBag.Statuses)
                        {
                            <option value="@status.Value" selected="@(ViewBag.SelectedStatus == status.Value)">@status.Text</option>
                        }
                    </select>
                </form>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Khách hàng</th>
                        <th>Ngày đặt</th>
                        <th>Ngày bắt đầu</th>
                        <th>Ngày kết thúc</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Id</td>
                            <td>@item.ApplicationUser.FullName</td>
                            <td>@item.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>@item.StartDate.ToString("dd/MM/yyyy")</td>
                            <td>@item.EndDate.ToString("dd/MM/yyyy")</td>
                            <td>@item.TotalPrice.ToString("N0") VNĐ</td>
                            <td>
                                <span class="badge @(item.Status == VehicleBookingStatus.Pending ? "bg-warning" :
                                                   item.Status == VehicleBookingStatus.Confirmed ? "bg-primary" :
                                                   item.Status == VehicleBookingStatus.Completed ? "bg-success" : "bg-danger")">
                                    @item.Status.ToString()
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
