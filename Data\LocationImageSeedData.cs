﻿﻿using ViVu.Models;

namespace ViVu.Data
{
    public static class LocationImageSeedData
    {
        public static void UpdateLocationImages(ApplicationDbContext context)
        {
            // Cập nhật hình ảnh cho các địa điểm hiện có
            var locations = context.Locations.ToList();
            
            foreach (var location in locations)
            {
                // Đặt đường dẫn hình ảnh mặc định dựa trên tên địa điểm
                string imageName = location.Name.ToLower()
                    .Replace(" ", "-")
                    .Replace("(", "")
                    .Replace(")", "")
                    .Replace(",", "");
                
                // Đặt đường dẫn hình ảnh
                location.ImageUrl = $"/images/destinations/{imageName}.jpg";
                
                // Cập nhật địa điểm
                context.Update(location);
            }
            
            context.SaveChanges();
        }
    }
}
