﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class BookingDetail
    {
        public int Id { get; set; }

        public int BookingId { get; set; }

        public int RoomId { get; set; }

        public int NumberOfRooms { get; set; } = 1;

        public int NumberOfGuests { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PricePerNight { get; set; }

        public DateTime CheckInDate { get; set; }

        public DateTime CheckOutDate { get; set; }

        public BookingDetailStatus Status { get; set; } = BookingDetailStatus.Pending;

        public Booking Booking { get; set; }

        public Room Room { get; set; }

        [NotMapped]
        public int NumberOfNights => (CheckOutDate - CheckInDate).Days;

        [NotMapped]
        public decimal TotalPrice => PricePerNight * NumberOfRooms * NumberOfNights;
    }

    public enum BookingDetailStatus
    {
        Pending,
        Confirmed,
        Cancelled,
        Completed
    }
}
