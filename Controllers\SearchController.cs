﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class SearchController : Controller
    {
        private readonly IAccommodationRepository _accommodationRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;

        public SearchController(
            IAccommodationRepository accommodationRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository)
        {
            _accommodationRepository = accommodationRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
        }

        // Hiển thị trang tìm kiếm
        public async Task<IActionResult> Index()
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View();
        }

        // Xử lý tìm kiếm
        [HttpPost]
        public async Task<IActionResult> Search(string searchTerm, int? locationId, int? cityId, DateTime? checkIn, DateTime? checkOut, int? minPrice, int? maxPrice, int? starRating)
        {
            // Kiểm tra ngày check-in và check-out
            if (!checkIn.HasValue)
            {
                checkIn = DateTime.Today.AddDays(1);
            }

            if (!checkOut.HasValue)
            {
                checkOut = checkIn.Value.AddDays(1);
            }

            // Tìm kiếm khách sạn
            var results = await _accommodationRepository.SearchAsync(
                searchTerm, locationId, cityId, checkIn, checkOut, minPrice, maxPrice, starRating);

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.SearchTerm = searchTerm;
            ViewBag.LocationId = locationId;
            ViewBag.CityId = cityId;
            ViewBag.CheckIn = checkIn;
            ViewBag.CheckOut = checkOut;
            ViewBag.MinPrice = minPrice;
            ViewBag.MaxPrice = maxPrice;
            ViewBag.StarRating = starRating;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View("Results", results);
        }

        // Hiển thị kết quả tìm kiếm
        public async Task<IActionResult> Results(string searchTerm, int? locationId, int? cityId, DateTime? checkIn, DateTime? checkOut, int? minPrice, int? maxPrice, int? starRating, string sortOrder)
        {
            // Kiểm tra ngày check-in và check-out
            if (!checkIn.HasValue)
            {
                checkIn = DateTime.Today.AddDays(1);
            }

            if (!checkOut.HasValue)
            {
                checkOut = checkIn.Value.AddDays(1);
            }

            // Tìm kiếm khách sạn
            var results = await _accommodationRepository.SearchAsync(
                searchTerm, locationId, cityId, checkIn, checkOut, minPrice, maxPrice, starRating);

            // Sắp xếp kết quả
            ViewBag.CurrentSort = sortOrder;
            switch (sortOrder)
            {
                case "price_asc":
                    results = results.OrderBy(a => a.MinPrice).ToList();
                    break;
                case "price_desc":
                    results = results.OrderByDescending(a => a.MinPrice).ToList();
                    break;
                case "rating_desc":
                    results = results.OrderByDescending(a => a.AverageRating).ToList();
                    break;
                default:
                    results = results.OrderBy(a => a.Name).ToList();
                    break;
            }

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.SearchTerm = searchTerm;
            ViewBag.LocationId = locationId;
            ViewBag.CityId = cityId;
            ViewBag.CheckIn = checkIn;
            ViewBag.CheckOut = checkOut;
            ViewBag.MinPrice = minPrice;
            ViewBag.MaxPrice = maxPrice;
            ViewBag.StarRating = starRating;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View(results);
        }
    }
}
