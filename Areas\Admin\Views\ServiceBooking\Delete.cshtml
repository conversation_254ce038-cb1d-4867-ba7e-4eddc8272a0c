@model ViVu.Models.ServiceBooking
@{
    ViewData["Title"] = "Xóa đặt dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa đặt dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Đặt dịch vụ</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa đặt dịch vụ #@Model.Id
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Bạn có chắc chắn muốn xóa đơn đặt dịch vụ này?</h5>
                <p>Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến đơn đặt dịch vụ này sẽ bị xóa.</p>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Thông tin khách hàng</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Họ tên:</th>
                            <td>@Model.ApplicationUser.FullName</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>@Model.ApplicationUser.Email</td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-md-6">
                    <h5>Thông tin đặt dịch vụ</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Mã đặt dịch vụ:</th>
                            <td>#@Model.Id</td>
                        </tr>
                        <tr>
                            <th>Ngày đặt:</th>
                            <td>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                        </tr>
                        <tr>
                            <th>Ngày sử dụng:</th>
                            <td>@Model.ServiceDate.ToString("dd/MM/yyyy")</td>
                        </tr>
                        <tr>
                            <th>Tổng tiền:</th>
                            <td>@Model.TotalPrice.ToString("N0") VNĐ</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <form asp-action="Delete" method="post">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
