﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Data;
using ViVu.Models;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public AdminController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // Hiển thị trang Dashboard
        public async Task<IActionResult> Index()
        {
            // Thống kê tổng quan
            ViewBag.TotalUsers = await _context.Users.CountAsync();
            ViewBag.TotalAccommodations = await _context.Accommodations.CountAsync();
            ViewBag.TotalRooms = await _context.Rooms.CountAsync();
            ViewBag.TotalBookings = await _context.Bookings.CountAsync();
            ViewBag.TotalTours = await _context.Tours.CountAsync();
            ViewBag.TotalTourBookings = await _context.TourBookings.CountAsync();
            ViewBag.TotalServices = await _context.Services.CountAsync();
            ViewBag.TotalServiceBookings = await _context.ServiceBookings.CountAsync();
            ViewBag.TotalVehicles = await _context.Vehicles.CountAsync();
            ViewBag.TotalVehicleBookings = await _context.VehicleBookings.CountAsync();

            // Đơn đặt phòng mới nhất
            var recentBookings = await _context.Bookings
                .Include(b => b.ApplicationUser)
                .OrderByDescending(b => b.BookingDate)
                .Take(5)
                .ToListAsync();

            // Đơn đặt tour mới nhất
            var recentTourBookings = await _context.TourBookings
                .Include(tb => tb.ApplicationUser)
                .OrderByDescending(tb => tb.BookingDate)
                .Take(5)
                .ToListAsync();

            // Đơn đặt dịch vụ mới nhất
            var recentServiceBookings = await _context.ServiceBookings
                .Include(sb => sb.ApplicationUser)
                .OrderByDescending(sb => sb.BookingDate)
                .Take(5)
                .ToListAsync();

            // Đơn đặt phương tiện mới nhất
            var recentVehicleBookings = await _context.VehicleBookings
                .Include(vb => vb.ApplicationUser)
                .OrderByDescending(vb => vb.BookingDate)
                .Take(5)
                .ToListAsync();

            // Đánh giá mới nhất
            var recentReviews = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .OrderByDescending(r => r.CreatedAt)
                .Take(5)
                .ToListAsync();

            // Người dùng mới nhất
            var recentUsers = await _userManager.Users
                .OrderByDescending(u => u.CreatedDate)
                .Take(5)
                .ToListAsync();

            // Thống kê đặt phòng theo trạng thái
            ViewBag.PendingBookings = await _context.Bookings.CountAsync(b => b.Status == BookingStatus.Pending);
            ViewBag.ConfirmedBookings = await _context.Bookings.CountAsync(b => b.Status == BookingStatus.Confirmed);
            ViewBag.CompletedBookings = await _context.Bookings.CountAsync(b => b.Status == BookingStatus.Completed);
            ViewBag.CancelledBookings = await _context.Bookings.CountAsync(b => b.Status == BookingStatus.Cancelled);

            // Thống kê đặt tour theo trạng thái
            ViewBag.PendingTourBookings = await _context.TourBookings.CountAsync(tb => tb.Status == TourBookingStatus.Pending);
            ViewBag.ConfirmedTourBookings = await _context.TourBookings.CountAsync(tb => tb.Status == TourBookingStatus.Confirmed);
            ViewBag.CompletedTourBookings = await _context.TourBookings.CountAsync(tb => tb.Status == TourBookingStatus.Completed);
            ViewBag.CancelledTourBookings = await _context.TourBookings.CountAsync(tb => tb.Status == TourBookingStatus.Cancelled);

            // Thống kê đặt dịch vụ theo trạng thái
            ViewBag.PendingServiceBookings = await _context.ServiceBookings.CountAsync(sb => sb.Status == ServiceBookingStatus.Pending);
            ViewBag.ConfirmedServiceBookings = await _context.ServiceBookings.CountAsync(sb => sb.Status == ServiceBookingStatus.Confirmed);
            ViewBag.CompletedServiceBookings = await _context.ServiceBookings.CountAsync(sb => sb.Status == ServiceBookingStatus.Completed);
            ViewBag.CancelledServiceBookings = await _context.ServiceBookings.CountAsync(sb => sb.Status == ServiceBookingStatus.Cancelled);

            // Thống kê đặt phương tiện theo trạng thái
            ViewBag.PendingVehicleBookings = await _context.VehicleBookings.CountAsync(vb => vb.Status == VehicleBookingStatus.Pending);
            ViewBag.ConfirmedVehicleBookings = await _context.VehicleBookings.CountAsync(vb => vb.Status == VehicleBookingStatus.Confirmed);
            ViewBag.CompletedVehicleBookings = await _context.VehicleBookings.CountAsync(vb => vb.Status == VehicleBookingStatus.Completed);
            ViewBag.CancelledVehicleBookings = await _context.VehicleBookings.CountAsync(vb => vb.Status == VehicleBookingStatus.Cancelled);

            var dashboardViewModel = new AdminDashboardViewModel
            {
                RecentBookings = recentBookings,
                RecentTourBookings = recentTourBookings,
                RecentServiceBookings = recentServiceBookings,
                RecentVehicleBookings = recentVehicleBookings,
                RecentReviews = recentReviews,
                RecentUsers = recentUsers
            };

            return View(dashboardViewModel);
        }


    }
}
