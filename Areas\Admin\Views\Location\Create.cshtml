@model ViVu.Models.Location
@{
    ViewData["Title"] = "Thêm địa điểm mới";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Thêm địa điểm mới</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý địa điểm</a></li>
        <li class="breadcrumb-item active">Thêm mới</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus-circle me-1"></i>
            Thêm địa điểm mới
        </div>
        <div class="card-body">
            <form asp-action="Create" enctype="multipart/form-data" method="post">
                <div asp-validation-summary="All" class="text-danger"></div>
                <!-- Hidden field to help with debugging -->
                <input type="hidden" name="formSubmitted" value="true" />

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên địa điểm</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CityId" class="control-label">Thành phố</label>
                            <select asp-for="CityId" class="form-select" asp-items="ViewBag.Cities" required>
                                <option value="">-- Chọn thành phố --</option>
                            </select>
                            <span asp-validation-for="CityId" class="text-danger"></span>
                            <input type="hidden" name="City" value="" />
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Address" class="control-label">Địa chỉ</label>
                            <input asp-for="Address" class="form-control" />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Phone" class="control-label">Số điện thoại</label>
                            <input asp-for="Phone" class="form-control" />
                            <span asp-validation-for="Phone" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Email" class="control-label">Email</label>
                            <input asp-for="Email" class="form-control" type="email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Website" class="control-label">Website</label>
                            <input asp-for="Website" class="form-control" />
                            <span asp-validation-for="Website" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Description" class="control-label">Mô tả</label>
                            <textarea asp-for="Description" class="form-control" rows="5"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label for="imageFile" class="control-label">Hình ảnh</label>
                            <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" onchange="previewImage(this)" />
                            <small class="text-muted">Chấp nhận các định dạng: JPG, JPEG, PNG, GIF, WEBP. Kích thước tối đa: 10MB</small>

                            <div id="imagePreview" class="mt-2 d-none">
                                <img id="preview" src="#" alt="Xem trước" style="max-width: 200px; max-height: 150px;" />
                                <p class="text-muted small">Hình ảnh đã chọn. Nhấn "Lưu" để cập nhật.</p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Latitude" class="control-label">Vĩ độ</label>
                                    <input asp-for="Latitude" class="form-control" />
                                    <span asp-validation-for="Latitude" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Longitude" class="control-label">Kinh độ</label>
                                    <input asp-for="Longitude" class="form-control" />
                                    <span asp-validation-for="Longitude" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group form-check mb-3">
                            <input asp-for="IsFeatured" class="form-check-input" />
                            <label asp-for="IsFeatured" class="form-check-label">Đánh dấu là địa điểm nổi bật</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Lưu</button>
                    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        function previewImage(input) {
            var preview = document.getElementById('preview');
            var previewDiv = document.getElementById('imagePreview');

            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    preview.src = e.target.result;
                    previewDiv.classList.remove('d-none');
                }

                reader.readAsDataURL(input.files[0]);
            } else {
                previewDiv.classList.add('d-none');
            }
        }

        // Ensure form is properly submitting with file data
        document.addEventListener('DOMContentLoaded', function() {
            var form = document.querySelector('form');
            var fileInput = document.getElementById('imageFile');

            // Log when file is selected
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    var file = fileInput.files[0];
                    console.log('File selected:', file.name);
                    console.log('File size:', file.size, 'bytes');
                    console.log('File type:', file.type);

                    // Validate file type
                    var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        alert('Chú ý: Loại file không được hỗ trợ. Vui lòng chọn file hình ảnh (JPG, JPEG, PNG, GIF, WEBP).');
                    }

                    // Validate file size (max 10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('Chú ý: Kích thước file quá lớn. Giới hạn tối đa là 10MB.');
                    }
                }
            });

            // Add submit event listener for validation and logging
            form.addEventListener('submit', function(e) {
                var hasFile = fileInput.files.length > 0;
                var formData = new FormData(form);

                console.log('Form submission started');

                // Log all form fields for debugging
                for (var pair of formData.entries()) {
                    if (pair[0] !== 'imageFile') { // Don't log the file binary data
                        console.log(pair[0] + ': ' + pair[1]);
                    } else {
                        console.log('imageFile: [File object]');
                    }
                }

                if (hasFile) {
                    var file = fileInput.files[0];
                    console.log('Submitting form with file:', file.name, 'Size:', file.size, 'bytes', 'Type:', file.type);

                    // Final validation before submit
                    var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        e.preventDefault();
                        alert('Lỗi: Loại file không được hỗ trợ. Vui lòng chọn file hình ảnh (JPG, JPEG, PNG, GIF, WEBP).');
                        return false;
                    }

                    if (file.size > 10 * 1024 * 1024) {
                        e.preventDefault();
                        alert('Lỗi: Kích thước file quá lớn. Giới hạn tối đa là 10MB.');
                        return false;
                    }
                } else {
                    console.log('Submitting form without file');
                }

                console.log('Form submission proceeding...');
                return true;
            });
        });
    </script>
}
