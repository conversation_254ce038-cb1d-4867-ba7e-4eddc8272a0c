﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class ServiceImage
    {
        public int Id { get; set; }
        
        [Required]
        public string ImageUrl { get; set; }
        
        public int ServiceId { get; set; }
        
        [ForeignKey("ServiceId")]
        public Service Service { get; set; }
        
        public bool IsMain { get; set; } = false;
        
        public string? Caption { get; set; }
    }
}
