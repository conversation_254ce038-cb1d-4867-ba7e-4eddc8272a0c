@model ViVu.Models.Accommodation
@{
    ViewData["Title"] = "Xóa chỗ ở";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa chỗ ở</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Chỗ ở</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa chỗ ở
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo!</h5>
                <p>Bạn có chắc chắn muốn xóa chỗ ở này không? Hành động này không thể hoàn tác.</p>
            </div>
            
            <div asp-validation-summary="All" class="text-danger"></div>
            
            <div class="row">
                <div class="col-md-4">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" />
                </div>
                <div class="col-md-8">
                    <h3>@Model.Name</h3>
                    <p><i class="fas fa-map-marker-alt"></i> @Model.Address</p>
                    
                    <dl class="row">
                        <dt class="col-sm-3">Địa điểm:</dt>
                        <dd class="col-sm-9">@Model.Location?.Name</dd>
                        
                        <dt class="col-sm-3">Thành phố:</dt>
                        <dd class="col-sm-9">@Model.City?.Name</dd>
                        
                        <dt class="col-sm-3">Đánh giá:</dt>
                        <dd class="col-sm-9">
                            @for (int i = 1; i <= 5; i++)
                            {
                                if (i <= Model.StarRating)
                                {
                                    <i class="fas fa-star text-warning"></i>
                                }
                                else
                                {
                                    <i class="far fa-star text-warning"></i>
                                }
                            }
                        </dd>
                        
                        <dt class="col-sm-3">Nổi bật:</dt>
                        <dd class="col-sm-9">
                            @if (Model.IsFeatured)
                            {
                                <span class="badge bg-success">Có</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Không</span>
                            }
                        </dd>
                    </dl>
                    
                    <form asp-action="DeleteConfirmed" method="post">
                        <input type="hidden" asp-for="Id" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
