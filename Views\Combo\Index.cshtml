@model ViVu.Models.ComboViewModel

@{
    ViewData["Title"] = "Combo du lịch";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Combo du lịch</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="position-relative rounded overflow-hidden">
                <img src="/images/banners/banner_combo.jpg" class="img-fluid w-100" style="height: 350px; object-fit: cover;" alt="Combo du lịch">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);">
                    <div class="container-fluid px-4 px-md-5">
                        <div class="row">
                            <div class="col-md-7">
                                <h1 class="text-white mb-3 text-shadow-strong">Combo du lịch tiết kiệm</h1>
                                <p class="text-white mb-3 fs-5">Kết hợp tour và khách sạn để nhận ưu đãi đặc biệt lên đến 30%</p>
                                <div class="d-flex align-items-center">
                                    <span class="text-white me-3 fs-4 fw-bold">Tiết kiệm ngay hôm nay!</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h4 class="card-title mb-3">Tìm kiếm combo</h4>
                    <form asp-controller="Combo" asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Điểm đến</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn điểm đến --</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Ngày đi</label>
                                <input type="date" name="startDate" class="form-control" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Ngày về</label>
                                <input type="date" name="endDate" class="form-control" value="@DateTime.Today.AddDays(3).ToString("yyyy-MM-dd")">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Combos -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Combo nổi bật</h2>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-lift">
                <div class="position-relative">
                    <img src="/images/combos/combo_bentre_3n2d.jpg" class="card-img-top" style="height: 200px; object-fit: cover;" alt="Combo Bến Tre 3N2Đ">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-danger">Tiết kiệm 25%</span>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title mb-2">Combo Bến Tre 3 ngày 2 đêm</h5>
                    <p class="card-text text-muted mb-2">
                        <i class="bi bi-geo-alt-fill"></i> Bến Tre, Việt Nam
                    </p>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <i class="bi bi-clock"></i> 3 ngày 2 đêm
                        </span>
                        <span>
                            <i class="bi bi-building"></i> Khách sạn 4 sao
                        </span>
                    </div>
                    <p class="card-text">Trọn gói tour khám phá Bến Tre và nghỉ dưỡng tại khách sạn 4 sao với nhiều ưu đãi đặc biệt.</p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-decoration-line-through text-muted">3,000,000 VNĐ</span>
                            <span class="text-success fw-bold d-block">2,250,000 VNĐ</span>
                        </div>
                        <a href="#" class="btn btn-outline-primary">Xem chi tiết</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-lift">
                <div class="position-relative">
                    <img src="/images/combos/combo_mekong_4n3d.jpg" class="card-img-top" style="height: 200px; object-fit: cover;" alt="Combo Mekong 4N3Đ">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-danger">Tiết kiệm 30%</span>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title mb-2">Combo Mekong 4 ngày 3 đêm</h5>
                    <p class="card-text text-muted mb-2">
                        <i class="bi bi-geo-alt-fill"></i> Bến Tre - Cần Thơ, Việt Nam
                    </p>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <i class="bi bi-clock"></i> 4 ngày 3 đêm
                        </span>
                        <span>
                            <i class="bi bi-building"></i> Khách sạn 5 sao
                        </span>
                    </div>
                    <p class="card-text">Hành trình khám phá Đồng bằng sông Cửu Long với tour đặc biệt và nghỉ dưỡng tại khách sạn 5 sao.</p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-decoration-line-through text-muted">5,000,000 VNĐ</span>
                            <span class="text-success fw-bold d-block">3,500,000 VNĐ</span>
                        </div>
                        <a href="#" class="btn btn-outline-primary">Xem chi tiết</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-lift">
                <div class="position-relative">
                    <img src="/images/combos/combo_weekend_2n1d.jpg" class="card-img-top" style="height: 200px; object-fit: cover;" alt="Combo Cuối tuần 2N1Đ">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-danger">Tiết kiệm 20%</span>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title mb-2">Combo Cuối tuần 2 ngày 1 đêm</h5>
                    <p class="card-text text-muted mb-2">
                        <i class="bi bi-geo-alt-fill"></i> Bến Tre, Việt Nam
                    </p>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <i class="bi bi-clock"></i> 2 ngày 1 đêm
                        </span>
                        <span>
                            <i class="bi bi-building"></i> Khách sạn 3 sao
                        </span>
                    </div>
                    <p class="card-text">Trải nghiệm cuối tuần thú vị tại Bến Tre với tour ngắn ngày và nghỉ dưỡng tại khách sạn 3 sao.</p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-decoration-line-through text-muted">1,800,000 VNĐ</span>
                            <span class="text-success fw-bold d-block">1,440,000 VNĐ</span>
                        </div>
                        <a href="#" class="btn btn-outline-primary">Xem chi tiết</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tours Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Tour du lịch</h2>
            <p>Chọn tour du lịch phù hợp để kết hợp với khách sạn</p>
        </div>
    </div>

    <div class="row mb-5">
        @if (Model.Tours.Any())
        {
            foreach (var tour in Model.Tours.Take(3))
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/default/default-tour.jpg" : tour.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@tour.Name">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@tour.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @tour.Location?.Name, @tour.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @tour.Duration ngày
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @tour.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(tour.Description?.Length > 100 ? tour.Description.Substring(0, 100) + "..." : tour.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@tour.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-3">
                <div class="alert alert-info">
                    <p>Chưa có tour nào được đăng ký.</p>
                </div>
            </div>
        }
    </div>

    <!-- Accommodations Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Khách sạn</h2>
            <p>Chọn khách sạn phù hợp để kết hợp với tour du lịch</p>
        </div>
    </div>

    <div class="row">
        @if (Model.Accommodations.Any())
        {
            foreach (var accommodation in Model.Accommodations.Take(3))
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm hover-lift">
                        <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default/default-hotel.jpg" : accommodation.ImageUrl)"
                             class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title mb-0">@accommodation.Name</h5>
                                <div>
                                    @for (int i = 0; i < accommodation.StarRating; i++)
                                    {
                                        <i class="bi bi-star-fill text-warning"></i>
                                    }
                                </div>
                            </div>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @accommodation.Location?.Name, @accommodation.City?.Name
                            </p>
                            <p class="card-text">@(accommodation.Description?.Length > 100 ? accommodation.Description.Substring(0, 100) + "..." : accommodation.Description)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-3">
                <div class="alert alert-info">
                    <p>Chưa có khách sạn nào được đăng ký.</p>
                </div>
            </div>
        }
    </div>
</div>
