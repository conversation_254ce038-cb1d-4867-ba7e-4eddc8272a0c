﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Data;
using ViVu.Extensions;
using ViVu.Models;

namespace ViVu.Controllers
{
    [Authorize]
    public class BookingController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public BookingController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // Hiển thị giỏ đặt phòng
        public IActionResult Index()
        {
            var cart = HttpContext.Session.GetObjectFromJson<BookingCart>("BookingCart") ?? new BookingCart();
            return View(cart);
        }

        // Thêm phòng vào giỏ đặt phòng
        [HttpPost]
        public async Task<IActionResult> AddToCart(int roomId, int numberOfRooms = 1, int numberOfGuests = 1, DateTime? checkInDate = null, DateTime? checkOutDate = null)
        {
            var room = await _context.Rooms
                .Include(r => r.Accommodation)
                .FirstOrDefaultAsync(r => r.Id == roomId);

            if (room == null)
            {
                return NotFound();
            }

            // Kiểm tra số lượng phòng và số khách
            if (numberOfRooms <= 0)
            {
                numberOfRooms = 1;
            }

            if (numberOfGuests <= 0)
            {
                numberOfGuests = 1;
            }

            // Kiểm tra ngày check-in và check-out
            if (!checkInDate.HasValue || checkInDate.Value < DateTime.Today)
            {
                checkInDate = DateTime.Today.AddDays(1);
            }

            if (!checkOutDate.HasValue || checkOutDate.Value <= checkInDate.Value)
            {
                checkOutDate = checkInDate.Value.AddDays(1);
            }

            var cart = HttpContext.Session.GetObjectFromJson<BookingCart>("BookingCart") ?? new BookingCart();

            var item = new BookingItem
            {
                RoomId = room.Id,
                RoomName = room.Name,
                AccommodationId = room.AccommodationId,
                AccommodationName = room.Accommodation?.Name,
                PricePerNight = room.PricePerNight,
                NumberOfRooms = numberOfRooms,
                NumberOfGuests = numberOfGuests,
                CheckInDate = checkInDate.Value,
                CheckOutDate = checkOutDate.Value,
                ImageUrl = room.ImageUrl
            };

            cart.AddItem(item);
            HttpContext.Session.SetObjectAsJson("BookingCart", cart);

            return RedirectToAction(nameof(Index));
        }

        // Xóa phòng khỏi giỏ đặt phòng
        [HttpPost]
        public IActionResult RemoveFromCart(int roomId)
        {
            var cart = HttpContext.Session.GetObjectFromJson<BookingCart>("BookingCart");
            if (cart != null)
            {
                cart.RemoveItem(roomId);
                HttpContext.Session.SetObjectAsJson("BookingCart", cart);
            }

            return RedirectToAction(nameof(Index));
        }

        // Hiển thị form thanh toán
        public IActionResult Checkout()
        {
            var cart = HttpContext.Session.GetObjectFromJson<BookingCart>("BookingCart");
            if (cart == null || !cart.Items.Any())
            {
                return RedirectToAction(nameof(Index));
            }

            return View(new Booking());
        }

        // Xử lý thanh toán
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Checkout(Booking booking)
        {
            var cart = HttpContext.Session.GetObjectFromJson<BookingCart>("BookingCart");
            if (cart == null || !cart.Items.Any())
            {
                return RedirectToAction(nameof(Index));
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account", new { returnUrl = Url.Action("Checkout", "Booking") });
            }

            booking.UserId = user.Id;
            booking.BookingDate = DateTime.UtcNow;
            booking.TotalPrice = cart.TotalPrice;
            booking.Status = BookingStatus.Pending;

            // Lấy ngày check-in và check-out từ item đầu tiên trong giỏ hàng
            booking.CheckInDate = cart.Items.First().CheckInDate;
            booking.CheckOutDate = cart.Items.First().CheckOutDate;

            booking.BookingDetails = cart.Items.Select(i => new BookingDetail
            {
                RoomId = i.RoomId,
                NumberOfRooms = i.NumberOfRooms,
                NumberOfGuests = i.NumberOfGuests,
                PricePerNight = i.PricePerNight,
                CheckInDate = i.CheckInDate,
                CheckOutDate = i.CheckOutDate,
                Status = BookingDetailStatus.Pending
            }).ToList();

            _context.Bookings.Add(booking);
            await _context.SaveChangesAsync();
            HttpContext.Session.Remove("BookingCart");

            return RedirectToAction(nameof(BookingConfirmed), new { id = booking.Id });
        }

        // Hiển thị trang xác nhận đặt phòng
        public async Task<IActionResult> BookingConfirmed(int id)
        {
            var booking = await _context.Bookings
                .Include(b => b.BookingDetails)
                .ThenInclude(bd => bd.Room)
                .ThenInclude(r => r.Accommodation)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // Hiển thị danh sách đặt phòng của người dùng
        public async Task<IActionResult> MyBookings()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account", new { returnUrl = Url.Action("MyBookings", "Booking") });
            }

            var bookings = await _context.Bookings
                .Include(b => b.BookingDetails)
                .ThenInclude(bd => bd.Room)
                .ThenInclude(r => r.Accommodation)
                .Where(b => b.UserId == user.Id)
                .OrderByDescending(b => b.BookingDate)
                .ToListAsync();

            return View(bookings);
        }

        // Hiển thị chi tiết đặt phòng
        public async Task<IActionResult> Details(int id)
        {
            var booking = await _context.Bookings
                .Include(b => b.BookingDetails)
                .ThenInclude(bd => bd.Room)
                .ThenInclude(r => r.Accommodation)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null || (booking.UserId != user.Id && !User.IsInRole("Admin")))
            {
                return Forbid();
            }

            return View(booking);
        }

        // Hủy đặt phòng
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id)
        {
            var booking = await _context.Bookings.FindAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null || (booking.UserId != user.Id && !User.IsInRole("Admin")))
            {
                return Forbid();
            }

            // Chỉ cho phép hủy đặt phòng nếu chưa check-in
            if (booking.Status == BookingStatus.Pending || booking.Status == BookingStatus.Confirmed)
            {
                booking.Status = BookingStatus.Cancelled;
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(MyBookings));
            }

            return RedirectToAction(nameof(Details), new { id });
        }
    }
}
