@model ViVu.Models.Review
@{
    ViewData["Title"] = "Xóa đánh giá";
    Layout = "_AdminLayout";
    
    string itemName = "";
    string itemType = "";
    
    if (Model.AccommodationId.HasValue && Model.Accommodation != null)
    {
        itemName = Model.Accommodation.Name;
        itemType = "Chỗ ở";
    }
    else if (Model.TourId.HasValue && Model.Tour != null)
    {
        itemName = Model.Tour.Name;
        itemType = "Tour";
    }
    else if (Model.ServiceId.HasValue && Model.Service != null)
    {
        itemName = Model.Service.Name;
        itemType = "Dịch vụ";
    }
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Quản lý đánh giá</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>

    <div class="row">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Xác nhận xóa đánh giá
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Bạn có chắc chắn muốn xóa đánh giá này không? Hành động này không thể hoàn tác.
                    </div>

                    <div class="mb-4">
                        <h5>Thông tin đánh giá</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th>Người dùng:</th>
                                <td>@Model.User?.FullName</td>
                            </tr>
                            <tr>
                                <th>Đối tượng:</th>
                                <td>@itemType: @itemName</td>
                            </tr>
                            <tr>
                                <th>Đánh giá:</th>
                                <td>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= Model.Rating)
                                            {
                                                <i class="fas fa-star"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star"></i>
                                            }
                                        }
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>Nhận xét:</th>
                                <td>@Model.Comment</td>
                            </tr>
                            <tr>
                                <th>Ngày đánh giá:</th>
                                <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                            </tr>
                        </table>
                    </div>

                    <form asp-action="Delete" method="post">
                        <input type="hidden" asp-for="Id" />
                        <div class="d-flex">
                            <a asp-action="Index" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Xác nhận xóa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
