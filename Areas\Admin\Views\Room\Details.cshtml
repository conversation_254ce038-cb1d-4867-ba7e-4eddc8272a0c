@model ViVu.Models.Room
@{
    ViewData["Title"] = "Chi tiết phòng";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Phòng</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-info-circle me-1"></i> Thông tin chi tiết</div>
                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit"></i> Chỉnh sửa
                    </a>
                    <a asp-action="Index" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" />
                </div>
                <div class="col-md-8">
                    <h2>@Model.Name</h2>
                    <p class="text-muted">
                        <i class="fas fa-hotel"></i> Thuộc: <a asp-area="Admin" asp-controller="Accommodation" asp-action="Details" asp-route-id="@Model.AccommodationId">@Model.Accommodation?.Name</a>
                    </p>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Giá/đêm:</strong> @Model.PricePerNight.ToString("N0") VNĐ</p>
                            <p><strong>Sức chứa:</strong> @Model.MaxOccupancy người</p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <strong>Trạng thái:</strong>
                                @if (Model.IsAvailable)
                                {
                                    <span class="badge bg-success">Có sẵn</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không có sẵn</span>
                                }
                            </p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Mô tả</h5>
                        <p>@Model.Description</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Tiện nghi</h5>
                        <div class="row">
                            @if (Model.RoomAmenities != null && Model.RoomAmenities.Any())
                            {
                                foreach (var roomAmenity in Model.RoomAmenities)
                                {
                                    <div class="col-md-3 mb-2">
                                        <i class="fas fa-check text-success"></i> @roomAmenity.Amenity.Name
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="col-12">
                                    <p class="text-muted">Không có tiện nghi nào.</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
