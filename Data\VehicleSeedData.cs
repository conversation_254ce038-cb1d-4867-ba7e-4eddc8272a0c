﻿﻿using ViVu.Models;

namespace ViVu.Data
{
    public static class VehicleSeedData
    {
        public static void SeedVehicles(ApplicationDbContext context)
        {
            if (context.Vehicles.Any())
            {
                return; // Đã có dữ liệu phương tiện
            }

            // Lấy các địa điểm và thành phố có sẵn
            var benTreCity = context.Cities.FirstOrDefault(c => c.Name.Contains("Bến Tre"));
            var benTreLocations = context.Locations.Where(l => l.CityId == benTreCity.Id).ToList();

            if (benTreCity == null || !benTreLocations.Any())
            {
                return; // Không tìm thấy dữ liệu cần thiết
            }

            // Seed Vehicles
            var vehicles = new List<Vehicle>
            {
                new Vehicle
                {
                    Name = "Honda Air Blade",
                    Description = "Xe máy Honda Air Blade 125cc, phù hợp cho 1-2 người, tiết kiệm nhiên liệu và dễ dàng di chuyển trong thành phố.",
                    Details = "- Loại xe: Xe tay ga\n- Dung tích xi-lanh: 125cc\n- Tiêu thụ nhiên liệu: 2.2L/100km\n- Bao gồm: Mũ bảo hiểm, áo mưa\n- Giấy tờ cần thiết: CMND/CCCD, bằng lái xe A1 trở lên",
                    ImageUrl = "/images/vehicles/airblade.jpg",
                    PricePerDay = 150000,
                    Type = VehicleType.Motorbike,
                    LicensePlate = "59P1-12345",
                    Capacity = 2,
                    IsFeatured = true,
                    IsAvailable = true,
                    LocationId = benTreLocations[0].Id,
                    CityId = benTreCity.Id
                },
                new Vehicle
                {
                    Name = "Toyota Vios",
                    Description = "Xe ô tô Toyota Vios 4 chỗ, phù hợp cho gia đình nhỏ hoặc nhóm bạn, đầy đủ tiện nghi và an toàn.",
                    Details = "- Loại xe: Sedan\n- Số chỗ ngồi: 4+1\n- Nhiên liệu: Xăng\n- Tiêu thụ nhiên liệu: 6L/100km\n- Trang bị: Điều hòa, màn hình giải trí, camera lùi\n- Giấy tờ cần thiết: CMND/CCCD, bằng lái xe B1 trở lên, đặt cọc",
                    ImageUrl = "/images/vehicles/vios.jpg",
                    PricePerDay = 800000,
                    Type = VehicleType.Car,
                    LicensePlate = "59A-12345",
                    Capacity = 5,
                    IsFeatured = true,
                    IsAvailable = true,
                    LocationId = benTreLocations[0].Id,
                    CityId = benTreCity.Id
                },
                new Vehicle
                {
                    Name = "Xe đạp thể thao Giant",
                    Description = "Xe đạp thể thao Giant, phù hợp cho việc khám phá các con đường nhỏ và tham quan thành phố.",
                    Details = "- Loại xe: Xe đạp thể thao\n- Khung: Nhôm\n- Phanh: Đĩa\n- Trang bị: Mũ bảo hiểm, khóa xe\n- Giấy tờ cần thiết: CMND/CCCD, đặt cọc",
                    ImageUrl = "/images/vehicles/bicycle.jpg",
                    PricePerDay = 100000,
                    Type = VehicleType.Bicycle,
                    LicensePlate = "XD-001",
                    Capacity = 1,
                    IsFeatured = false,
                    IsAvailable = true,
                    LocationId = benTreLocations.Count > 1 ? benTreLocations[1].Id : benTreLocations[0].Id,
                    CityId = benTreCity.Id
                },
                new Vehicle
                {
                    Name = "Thuyền du lịch sông Bến Tre",
                    Description = "Thuyền du lịch trên sông Bến Tre, phù hợp cho việc khám phá các kênh rạch và vườn trái cây.",
                    Details = "- Loại: Thuyền gỗ truyền thống\n- Sức chứa: 10 người\n- Trang bị: Áo phao, mái che, ghế ngồi\n- Bao gồm: Người lái thuyền\n- Giấy tờ cần thiết: CMND/CCCD, đặt cọc",
                    ImageUrl = "/images/vehicles/boat.jpg",
                    PricePerDay = 500000,
                    Type = VehicleType.Boat,
                    LicensePlate = "BT-001",
                    Capacity = 10,
                    IsFeatured = true,
                    IsAvailable = true,
                    LocationId = benTreLocations.Count > 2 ? benTreLocations[2].Id : benTreLocations[0].Id,
                    CityId = benTreCity.Id
                },
                new Vehicle
                {
                    Name = "Honda SH Mode",
                    Description = "Xe máy Honda SH Mode 125cc, phù hợp cho 1-2 người, thiết kế sang trọng và tiện nghi.",
                    Details = "- Loại xe: Xe tay ga\n- Dung tích xi-lanh: 125cc\n- Tiêu thụ nhiên liệu: 2.0L/100km\n- Bao gồm: Mũ bảo hiểm, áo mưa\n- Giấy tờ cần thiết: CMND/CCCD, bằng lái xe A1 trở lên",
                    ImageUrl = "/images/vehicles/shmode.jpg",
                    PricePerDay = 200000,
                    Type = VehicleType.Motorbike,
                    LicensePlate = "59P2-12345",
                    Capacity = 2,
                    IsFeatured = false,
                    IsAvailable = true,
                    LocationId = benTreLocations.Count > 1 ? benTreLocations[1].Id : benTreLocations[0].Id,
                    CityId = benTreCity.Id
                },
                new Vehicle
                {
                    Name = "Ford Everest",
                    Description = "Xe ô tô Ford Everest 7 chỗ, phù hợp cho gia đình lớn hoặc nhóm bạn, đầy đủ tiện nghi và an toàn.",
                    Details = "- Loại xe: SUV\n- Số chỗ ngồi: 7\n- Nhiên liệu: Dầu\n- Tiêu thụ nhiên liệu: 7L/100km\n- Trang bị: Điều hòa, màn hình giải trí, camera 360, cảm biến lùi\n- Giấy tờ cần thiết: CMND/CCCD, bằng lái xe B2 trở lên, đặt cọc",
                    ImageUrl = "/images/vehicles/everest.jpg",
                    PricePerDay = 1200000,
                    Type = VehicleType.Car,
                    LicensePlate = "59A-54321",
                    Capacity = 7,
                    IsFeatured = true,
                    IsAvailable = true,
                    LocationId = benTreLocations[0].Id,
                    CityId = benTreCity.Id
                }
            };

            // Thêm phương tiện vào cơ sở dữ liệu
            context.Vehicles.AddRange(vehicles);
            context.SaveChanges();

            // Seed Vehicle Images
            var vehicleImages = new List<VehicleImage>();

            // Lấy danh sách phương tiện đã thêm vào cơ sở dữ liệu
            var savedVehicles = context.Vehicles.ToList();

            // Thêm hình ảnh cho Honda Air Blade
            var airBlade = savedVehicles.FirstOrDefault(v => v.Name == "Honda Air Blade");
            if (airBlade != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = airBlade.Id, ImageUrl = "/images/vehicles/airblade_1.jpg" });
                vehicleImages.Add(new VehicleImage { VehicleId = airBlade.Id, ImageUrl = "/images/vehicles/airblade_2.jpg" });
            }

            // Thêm hình ảnh cho Toyota Vios
            var vios = savedVehicles.FirstOrDefault(v => v.Name == "Toyota Vios");
            if (vios != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = vios.Id, ImageUrl = "/images/vehicles/vios_1.jpg" });
                vehicleImages.Add(new VehicleImage { VehicleId = vios.Id, ImageUrl = "/images/vehicles/vios_2.jpg" });
            }

            // Thêm hình ảnh cho Xe đạp thể thao Giant
            var bicycle = savedVehicles.FirstOrDefault(v => v.Name == "Xe đạp thể thao Giant");
            if (bicycle != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = bicycle.Id, ImageUrl = "/images/vehicles/bicycle_1.jpg" });
            }

            // Thêm hình ảnh cho Thuyền du lịch sông Bến Tre
            var boat = savedVehicles.FirstOrDefault(v => v.Name == "Thuyền du lịch sông Bến Tre");
            if (boat != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = boat.Id, ImageUrl = "/images/vehicles/boat_1.jpg" });
                vehicleImages.Add(new VehicleImage { VehicleId = boat.Id, ImageUrl = "/images/vehicles/boat_2.jpg" });
            }

            // Thêm hình ảnh cho Honda SH Mode
            var shMode = savedVehicles.FirstOrDefault(v => v.Name == "Honda SH Mode");
            if (shMode != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = shMode.Id, ImageUrl = "/images/vehicles/shmode_1.jpg" });
            }

            // Thêm hình ảnh cho Ford Everest
            var everest = savedVehicles.FirstOrDefault(v => v.Name == "Ford Everest");
            if (everest != null)
            {
                vehicleImages.Add(new VehicleImage { VehicleId = everest.Id, ImageUrl = "/images/vehicles/everest_1.jpg" });
                vehicleImages.Add(new VehicleImage { VehicleId = everest.Id, ImageUrl = "/images/vehicles/everest_2.jpg" });
            }

            // Thêm hình ảnh vào cơ sở dữ liệu
            context.VehicleImages.AddRange(vehicleImages);
            context.SaveChanges();
        }
    }
}
