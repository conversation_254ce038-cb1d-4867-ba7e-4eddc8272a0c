@model ViVu.Models.BookingCart

@{
    ViewData["Title"] = "Giỏ đặt phòng";
}

<div class="container mt-4">
    <h1 class="mb-4">Giỏ đặt phòng</h1>

    @if (Model.Items.Any())
    {
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Thông tin đặt phòng</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Phòng</th>
                                <th><PERSON>h<PERSON><PERSON> sạn</th>
                                <th>Số lượng</th>
                                <th>Số khách</th>
                                <th>Ngày nhận phòng</th>
                                <th>Ngày trả phòng</th>
                                <th>Giá/đêm</th>
                                <th>Thành tiền</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Items)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="@item.ImageUrl" alt="@item.RoomName" class="img-thumbnail me-2" style="width: 80px; height: 60px; object-fit: cover;">
                                            <span>@item.RoomName</span>
                                        </div>
                                    </td>
                                    <td>@item.AccommodationName</td>
                                    <td>
                                        <form asp-action="UpdateCart" method="post" class="d-flex align-items-center">
                                            <input type="hidden" name="roomId" value="@item.RoomId" />
                                            <input type="number" name="numberOfRooms" value="@item.NumberOfRooms" min="1" max="10" class="form-control form-control-sm" style="width: 60px;">
                                            <button type="submit" class="btn btn-sm btn-outline-primary ms-2">
                                                <i class="bi bi-arrow-repeat"></i>
                                            </button>
                                        </form>
                                    </td>
                                    <td>@item.NumberOfGuests</td>
                                    <td>@item.CheckInDate.ToString("dd/MM/yyyy")</td>
                                    <td>@item.CheckOutDate.ToString("dd/MM/yyyy")</td>
                                    <td>@item.PricePerNight.ToString("#,##0") VNĐ</td>
                                    <td>@item.TotalPrice.ToString("#,##0") VNĐ</td>
                                    <td>
                                        <form asp-action="RemoveFromCart" method="post">
                                            <input type="hidden" name="roomId" value="@item.RoomId" />
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="7" class="text-end fw-bold">Tổng cộng:</td>
                                <td class="fw-bold text-success">@Model.TotalPrice.ToString("#,##0") VNĐ</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <a asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Tiếp tục tìm kiếm
                </a>
                <a asp-action="Checkout" class="btn btn-primary">
                    <i class="bi bi-credit-card"></i> Tiến hành thanh toán
                </a>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <h4 class="alert-heading">Giỏ đặt phòng trống!</h4>
            <p>Bạn chưa có phòng nào trong giỏ đặt phòng.</p>
            <hr>
            <p class="mb-0">Hãy tìm kiếm và chọn phòng phù hợp với nhu cầu của bạn.</p>
        </div>
        <div class="text-center mt-4">
            <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                <i class="bi bi-search"></i> Tìm kiếm phòng
            </a>
        </div>
    }

    @if (Model.Items.Any())
    {
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Chính sách đặt phòng</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="bi bi-clock me-2 text-primary"></i>
                                Nhận phòng từ 14:00, trả phòng trước 12:00
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-shield-check me-2 text-primary"></i>
                                Đảm bảo giá tốt nhất
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-credit-card me-2 text-primary"></i>
                                Thanh toán an toàn và bảo mật
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-x-circle me-2 text-primary"></i>
                                Miễn phí hủy đặt phòng trước 3 ngày
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Khuyến mãi</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="promoCode" class="form-label">Mã khuyến mãi</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="promoCode" placeholder="Nhập mã khuyến mãi">
                                <button class="btn btn-outline-primary" type="button">Áp dụng</button>
                            </div>
                        </div>
                        <div class="alert alert-info mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            Nhập mã <strong>BENTRE2023</strong> để được giảm 20% khi tham gia tour khám phá Bến Tre.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
