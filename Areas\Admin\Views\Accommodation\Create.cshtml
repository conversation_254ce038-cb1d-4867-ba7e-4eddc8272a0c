@model ViVu.Models.Accommodation
@{
    ViewData["Title"] = "Thêm chỗ ở mới";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Thêm chỗ ở mới</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Chỗ ở</a></li>
        <li class="breadcrumb-item active">Thêm mới</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i>
            Thông tin chỗ ở mới
        </div>
        <div class="card-body">
            <form asp-action="Create" enctype="multipart/form-data">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Name" class="control-label">Tên chỗ ở</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Address" class="control-label">Địa chỉ</label>
                            <input asp-for="Address" class="form-control" required />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="LocationId" class="control-label">Địa điểm</label>
                            <select asp-for="LocationId" class="form-select" asp-items="ViewBag.Locations" required>
                                <option value="">-- Chọn địa điểm --</option>
                            </select>
                            <span asp-validation-for="LocationId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="CityId" class="control-label">Thành phố</label>
                            <select asp-for="CityId" class="form-select" asp-items="ViewBag.Cities" required>
                                <option value="">-- Chọn thành phố --</option>
                            </select>
                            <span asp-validation-for="CityId" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="StarRating" class="control-label">Đánh giá (sao)</label>
                            <select asp-for="StarRating" class="form-select" required>
                                <option value="1">1 sao</option>
                                <option value="2">2 sao</option>
                                <option value="3">3 sao</option>
                                <option value="4">4 sao</option>
                                <option value="5">5 sao</option>
                            </select>
                            <span asp-validation-for="StarRating" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Hình ảnh</label>
                            <input type="file" name="imageFile" class="form-control" accept="image/*" required />
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="5" required></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>
                
                <div class="form-group mb-3">
                    <div class="form-check">
                        <input asp-for="IsFeatured" class="form-check-input" />
                        <label asp-for="IsFeatured" class="form-check-label">Nổi bật</label>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label class="control-label">Tiện nghi</label>
                    <div class="row">
                        @foreach (var amenity in ViewBag.Amenities)
                        {
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input type="checkbox" name="selectedAmenities" value="@amenity.Id" class="form-check-input" id="<EMAIL>" />
                                    <label class="form-check-label" for="<EMAIL>">@amenity.Name</label>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Hiển thị xem trước hình ảnh khi chọn file
        document.querySelector('input[name="imageFile"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    // Tạo hoặc cập nhật phần tử xem trước hình ảnh
                    let preview = document.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview mt-2';
                        e.target.parentNode.appendChild(preview);
                    }
                    preview.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
}
