﻿﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using ViVu.Models;

namespace ViVu.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using (var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>()))
            {
                // Kiểm tra xem đã có dữ liệu mẫu chưa
                if (context.Countries.Any() || context.Cities.Any() || context.Locations.Any() ||
                    context.Accommodations.Any() || context.Rooms.Any() || context.Amenities.Any())
                {
                    // Đã có dữ liệu cơ bản, không cần khởi tạo lại
                    // Nhưng vẫn kiểm tra và seed dữ liệu tour nếu cần
                    if (!context.Tours.Any())
                    {
                        // Seed dữ liệu tour
                        TourSeedData.SeedTours(context);
                    }

                    if (!context.Services.Any())
                    {
                        // Seed dữ liệu dịch vụ
                        ServiceSeedData.SeedServices(context);
                    }

                    if (!context.Vehicles.Any())
                    {
                        // Seed dữ liệu phương tiện
                        VehicleSeedData.SeedVehicles(context);
                    }

                    if (!context.Panorama360s.Any())
                    {
                        // Seed dữ liệu panorama 360
                        Panorama360SeedData.SeedPanorama360s(context);
                    }

                    // Cập nhật và thêm dữ liệu địa điểm
                    LocationSeedData.SeedLocations(context);
                    return;
                }

                // Tạo dữ liệu mẫu
                await SeedData(context);

                // Seed dữ liệu tour
                TourSeedData.SeedTours(context);

                // Seed dữ liệu dịch vụ
                ServiceSeedData.SeedServices(context);

                // Seed dữ liệu phương tiện
                VehicleSeedData.SeedVehicles(context);

                // Seed dữ liệu panorama 360
                Panorama360SeedData.SeedPanorama360s(context);

                // Cập nhật và thêm dữ liệu địa điểm
                LocationSeedData.SeedLocations(context);
            }

            // Tạo tài khoản người dùng mẫu
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            await SeedUsers(userManager, roleManager);
        }

        private static async Task SeedData(ApplicationDbContext context)
        {
            // Tạo quốc gia
            var vietnam = new Country
            {
                Name = "Việt Nam",
                Code = "VN",
                Description = "Việt Nam là một quốc gia Đông Nam Á với nhiều điểm du lịch hấp dẫn.",
                IsFeatured = true
            };
            context.Countries.Add(vietnam);
            await context.SaveChangesAsync();

            // Tạo thành phố
            var cities = new List<City>
            {
                new City
                {
                    Name = "Bến Tre",
                    Description = "Xứ dừa với những khu vườn xanh mát, sông nước hiền hòa và ẩm thực đặc sắc.",
                    CountryId = vietnam.Id,
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/bentre.jpg"
                },
                new City
                {
                    Name = "Mỹ Tho",
                    Description = "Cửa ngõ của miền Tây với cù lao, sông nước và vườn trái cây.",
                    CountryId = vietnam.Id,
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/mytho.jpg"
                },
                new City
                {
                    Name = "Trà Vinh",
                    Description = "Vùng đất của người Khmer với nhiều chùa cổ và văn hóa đặc sắc.",
                    CountryId = vietnam.Id,
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/travinh.jpg"
                },
                new City
                {
                    Name = "Vĩnh Long",
                    Description = "Vùng đất trù phú với những vườn trái cây và làng nghề truyền thống.",
                    CountryId = vietnam.Id,
                    IsFeatured = false,
                    ImageUrl = "/images/destinations/vinhlong.jpg"
                },
                new City
                {
                    Name = "Cần Thơ",
                    Description = "Thành phố sông nước với chợ nổi Cái Răng nổi tiếng và ẩm thực phong phú.",
                    CountryId = vietnam.Id,
                    IsFeatured = false,
                    ImageUrl = "/images/destinations/cantho.jpg"
                }
            };
            context.Cities.AddRange(cities);
            await context.SaveChangesAsync();

            // Tạo địa điểm
            var locations = new List<Location>
            {
                new Location
                {
                    Name = "Cồn Phụng",
                    Description = "Cồn Phụng là điểm du lịch nổi tiếng ở Bến Tre với khu di tích Đạo Dừa và nhiều hoạt động trải nghiệm văn hóa địa phương.",
                    CityId = cities[0].Id, // Bến Tre
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/conphung.jpg"
                },
                new Location
                {
                    Name = "Làng nghề dừa Bến Tre",
                    Description = "Làng nghề dừa Bến Tre là nơi du khách có thể tham quan và trải nghiệm quy trình chế biến các sản phẩm từ dừa.",
                    CityId = cities[0].Id, // Bến Tre
                    IsFeatured = false,
                    ImageUrl = "/images/destinations/langnghedua.jpg"
                },
                new Location
                {
                    Name = "Cồn Ốc",
                    Description = "Cồn Ốc là điểm du lịch sinh thái với vườn trái cây, sông nước và ẩm thực đặc sắc của miền Tây.",
                    CityId = cities[1].Id, // Mỹ Tho
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/conoc.jpg"
                },
                new Location
                {
                    Name = "Cù lao Thới Sơn",
                    Description = "Cù lao Thới Sơn là điểm du lịch nổi tiếng với vườn cây ăn trái, kênh rạch và văn hóa sông nước miền Tây.",
                    CityId = cities[1].Id, // Mỹ Tho
                    IsFeatured = false,
                    ImageUrl = "/images/destinations/culaothoison.jpg"
                },
                new Location
                {
                    Name = "Chùa Âng",
                    Description = "Chùa Âng là ngôi chùa Khmer cổ với kiến trúc độc đáo và nhiều giá trị văn hóa, lịch sử.",
                    CityId = cities[2].Id, // Trà Vinh
                    IsFeatured = true,
                    ImageUrl = "/images/destinations/chuaang.jpg"
                },
                new Location
                {
                    Name = "Biển Ba Động",
                    Description = "Biển Ba Động là bãi biển hoang sơ với cảnh quan thiên nhiên tuyệt đẹp và hải sản tươi ngon.",
                    CityId = cities[2].Id, // Trà Vinh
                    IsFeatured = false,
                    ImageUrl = "/images/destinations/bienbadong.jpg"
                }
            };
            context.Locations.AddRange(locations);
            await context.SaveChangesAsync();

            // Tạo tiện nghi
            var amenities = new List<Amenity>
            {
                new Amenity { Name = "Wi-Fi miễn phí", Icon = "bi bi-wifi", Type = AmenityType.Both },
                new Amenity { Name = "Bể bơi", Icon = "bi bi-water", Type = AmenityType.Accommodation },
                new Amenity { Name = "Phòng tập gym", Icon = "bi bi-bicycle", Type = AmenityType.Accommodation },
                new Amenity { Name = "Nhà hàng", Icon = "bi bi-cup-hot", Type = AmenityType.Accommodation },
                new Amenity { Name = "Spa", Icon = "bi bi-gem", Type = AmenityType.Accommodation },
                new Amenity { Name = "Đưa đón sân bay", Icon = "bi bi-airplane", Type = AmenityType.Accommodation },
                new Amenity { Name = "Máy lạnh", Icon = "bi bi-snow", Type = AmenityType.Room },
                new Amenity { Name = "TV màn hình phẳng", Icon = "bi bi-tv", Type = AmenityType.Room },
                new Amenity { Name = "Minibar", Icon = "bi bi-cup-straw", Type = AmenityType.Room },
                new Amenity { Name = "Két an toàn", Icon = "bi bi-safe", Type = AmenityType.Room },
                new Amenity { Name = "Bồn tắm", Icon = "bi bi-droplet", Type = AmenityType.Room },
                new Amenity { Name = "Bữa sáng miễn phí", Icon = "bi bi-egg-fried", Type = AmenityType.Both }
            };
            context.Amenities.AddRange(amenities);
            await context.SaveChangesAsync();

            // Tạo khách sạn
            var accommodations = new List<Accommodation>
            {
                new Accommodation
                {
                    Name = "Bến Tre Riverside Resort",
                    Description = "Resort sang trọng bên sông với không gian xanh mát, thiết kế hài hòa với thiên nhiên và dịch vụ đẳng cấp.",
                    Address = "123 Đường 30/4, Phường Phú Tân, TP. Bến Tre",
                    StarRating = 5,
                    IsFeatured = true,
                    LocationId = locations[0].Id, // Cồn Phụng
                    CityId = cities[0].Id, // Bến Tre
                    ImageUrl = "/images/accommodations/bentre-riverside.jpg"
                },
                new Accommodation
                {
                    Name = "Mekong Delta Lodge",
                    Description = "Khu nghỉ dưỡng sinh thái với kiến trúc đặc trưng miền Tây, nằm giữa vườn dừa xanh mát.",
                    Address = "456 Đường Đồng Khởi, Phường Phú Khương, TP. Bến Tre",
                    StarRating = 5,
                    IsFeatured = true,
                    LocationId = locations[1].Id, // Làng nghề dừa
                    CityId = cities[0].Id, // Bến Tre
                    ImageUrl = "/images/accommodations/mekong-delta-lodge.jpg"
                },
                new Accommodation
                {
                    Name = "Coconut Homestay",
                    Description = "Homestay mang đậm nét văn hóa miền Tây, nằm giữa vườn dừa với trải nghiệm cuộc sống địa phương.",
                    Address = "789 Ấp 5, Xã Tân Thạch, Huyện Châu Thành, Bến Tre",
                    StarRating = 3,
                    IsFeatured = true,
                    LocationId = locations[0].Id, // Cồn Phụng
                    CityId = cities[0].Id, // Bến Tre
                    ImageUrl = "/images/accommodations/coconut-homestay.jpg"
                },
                new Accommodation
                {
                    Name = "Mỹ Tho Riverside Hotel",
                    Description = "Khách sạn hiện đại với tầm nhìn ra sông Tiền, gần các điểm tham quan nổi tiếng của Mỹ Tho.",
                    Address = "45 Đường 30/4, Phường 1, TP. Mỹ Tho, Tiền Giang",
                    StarRating = 4,
                    IsFeatured = false,
                    LocationId = locations[3].Id, // Cù lao Thới Sơn
                    CityId = cities[1].Id, // Mỹ Tho
                    ImageUrl = "/images/accommodations/mytho-riverside.jpg"
                },
                new Accommodation
                {
                    Name = "Trà Vinh Boutique Hotel",
                    Description = "Khách sạn boutique với thiết kế kết hợp giữa văn hóa Khmer và hiện đại, gần các điểm tham quan nổi tiếng.",
                    Address = "123 Đường Nguyễn Thị Minh Khai, Phường 1, TP. Trà Vinh",
                    StarRating = 4,
                    IsFeatured = false,
                    LocationId = locations[4].Id, // Chùa Âng
                    CityId = cities[2].Id, // Trà Vinh
                    ImageUrl = "/images/accommodations/travinh-boutique.jpg"
                },
                new Accommodation
                {
                    Name = "Mekong Farmstay",
                    Description = "Khu nghỉ dưỡng nông trại với không gian xanh mát, trải nghiệm cuộc sống nông thôn và ẩm thực đặc sắc.",
                    Address = "456 Ấp Long Bình, Xã Long Đức, TP. Trà Vinh",
                    StarRating = 3,
                    IsFeatured = false,
                    LocationId = locations[5].Id, // Biển Ba Động
                    CityId = cities[2].Id, // Trà Vinh
                    ImageUrl = "/images/accommodations/mekong-farmstay.jpg"
                }
            };
            context.Accommodations.AddRange(accommodations);
            await context.SaveChangesAsync();

            // Tạo phòng
            var rooms = new List<Room>();
            foreach (var accommodation in accommodations)
            {
                // Phòng Deluxe
                rooms.Add(new Room
                {
                    Name = "Phòng Deluxe",
                    Description = "Phòng rộng rãi với thiết kế hiện đại, trang bị đầy đủ tiện nghi cao cấp.",
                    PricePerNight = accommodation.StarRating == 5 ? 2500000 : 1800000, // 2.5tr hoặc 1.8tr tùy theo sao
                    MaxOccupancy = 2,
                    AccommodationId = accommodation.Id,
                    IsAvailable = true,
                    ImageUrl = "/images/rooms/deluxe.jpg"
                });

                // Phòng Superior
                rooms.Add(new Room
                {
                    Name = "Phòng Superior",
                    Description = "Phòng tiêu chuẩn với đầy đủ tiện nghi cần thiết cho kỳ nghỉ của bạn.",
                    PricePerNight = accommodation.StarRating == 5 ? 1800000 : 1200000, // 1.8tr hoặc 1.2tr tùy theo sao
                    MaxOccupancy = 2,
                    AccommodationId = accommodation.Id,
                    IsAvailable = true,
                    ImageUrl = "/images/rooms/superior.jpg"
                });

                // Phòng Family
                rooms.Add(new Room
                {
                    Name = "Phòng Family",
                    Description = "Phòng rộng rãi phù hợp cho gia đình với 2 giường lớn và các tiện nghi đầy đủ.",
                    PricePerNight = accommodation.StarRating == 5 ? 3500000 : 2500000, // 3.5tr hoặc 2.5tr tùy theo sao
                    MaxOccupancy = 4,
                    AccommodationId = accommodation.Id,
                    IsAvailable = true,
                    ImageUrl = "/images/rooms/family.jpg"
                });

                // Suite (chỉ cho khách sạn 5 sao)
                if (accommodation.StarRating == 5)
                {
                    rooms.Add(new Room
                    {
                        Name = "Suite",
                        Description = "Phòng suite sang trọng với phòng khách riêng biệt, tầm nhìn đẹp và dịch vụ VIP.",
                        PricePerNight = 5000000, // 5tr
                        MaxOccupancy = 2,
                        AccommodationId = accommodation.Id,
                        IsAvailable = true,
                        ImageUrl = "/images/rooms/suite.jpg"
                    });
                }
            }
            context.Rooms.AddRange(rooms);
            await context.SaveChangesAsync();

            // Tạo liên kết giữa khách sạn và tiện nghi
            var accommodationAmenities = new List<AccommodationAmenity>();
            foreach (var accommodation in accommodations)
            {
                // Tất cả khách sạn đều có Wi-Fi
                accommodationAmenities.Add(new AccommodationAmenity
                {
                    AccommodationId = accommodation.Id,
                    AmenityId = amenities[0].Id // Wi-Fi
                });

                // Khách sạn 5 sao có nhiều tiện nghi hơn
                if (accommodation.StarRating == 5)
                {
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[1].Id // Bể bơi
                    });
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[2].Id // Phòng tập gym
                    });
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[3].Id // Nhà hàng
                    });
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[4].Id // Spa
                    });
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[5].Id // Đưa đón sân bay
                    });
                }
                else
                {
                    // Khách sạn 4 sao có ít tiện nghi hơn
                    accommodationAmenities.Add(new AccommodationAmenity
                    {
                        AccommodationId = accommodation.Id,
                        AmenityId = amenities[3].Id // Nhà hàng
                    });
                }

                // Tất cả khách sạn đều có bữa sáng miễn phí
                accommodationAmenities.Add(new AccommodationAmenity
                {
                    AccommodationId = accommodation.Id,
                    AmenityId = amenities[11].Id // Bữa sáng miễn phí
                });
            }
            context.AccommodationAmenities.AddRange(accommodationAmenities);
            await context.SaveChangesAsync();

            // Tạo liên kết giữa phòng và tiện nghi
            var roomAmenities = new List<RoomAmenity>();
            foreach (var room in rooms)
            {
                // Tất cả phòng đều có Wi-Fi, máy lạnh và TV
                roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[0].Id }); // Wi-Fi
                roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[6].Id }); // Máy lạnh
                roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[7].Id }); // TV

                // Phòng Deluxe và Suite có thêm minibar và két an toàn
                if (room.Name == "Phòng Deluxe" || room.Name == "Suite")
                {
                    roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[8].Id }); // Minibar
                    roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[9].Id }); // Két an toàn
                }

                // Suite có thêm bồn tắm
                if (room.Name == "Suite")
                {
                    roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[10].Id }); // Bồn tắm
                }

                // Tất cả phòng đều có bữa sáng miễn phí
                roomAmenities.Add(new RoomAmenity { RoomId = room.Id, AmenityId = amenities[11].Id }); // Bữa sáng miễn phí
            }
            context.RoomAmenities.AddRange(roomAmenities);
            await context.SaveChangesAsync();

            // Tạo hình ảnh cho khách sạn
            var accommodationImages = new List<AccommodationImage>();
            foreach (var accommodation in accommodations)
            {
                // Mỗi khách sạn có 4 hình ảnh
                for (int i = 1; i <= 4; i++)
                {
                    accommodationImages.Add(new AccommodationImage
                    {
                        Url = $"/images/accommodations/{accommodation.Name.ToLower().Replace(" ", "-")}-{i}.jpg",
                        AccommodationId = accommodation.Id,
                        IsPrimary = i == 1 // Hình đầu tiên là hình chính
                    });
                }
            }
            context.AccommodationImages.AddRange(accommodationImages);
            await context.SaveChangesAsync();

            // Tạo khuyến mãi
            var promotions = new List<Promotion>
            {
                new Promotion
                {
                    Name = "Khám phá Bến Tre",
                    Description = "Giảm giá 20% cho tất cả các đặt phòng khi tham gia tour khám phá Bến Tre.",
                    DiscountPercentage = 20,
                    StartDate = DateTime.Now,
                    EndDate = DateTime.Now.AddMonths(3),
                    PromoCode = "BENTRE2023",
                    IsActive = true
                },
                new Promotion
                {
                    Name = "Trải nghiệm miền Tây",
                    Description = "Giảm giá 15% khi đặt phòng trước 30 ngày cho các tour miền Tây.",
                    DiscountPercentage = 15,
                    StartDate = DateTime.Now,
                    EndDate = DateTime.Now.AddMonths(6),
                    PromoCode = "MEKONG2023",
                    IsActive = true
                },
                new Promotion
                {
                    Name = "Ưu đãi homestay",
                    Description = "Giảm giá 10% cho các đặt phòng homestay vào cuối tuần.",
                    DiscountPercentage = 10,
                    StartDate = DateTime.Now,
                    EndDate = DateTime.Now.AddMonths(12),
                    PromoCode = "HOMESTAY2023",
                    IsActive = true
                }
            };
            context.Promotions.AddRange(promotions);
            await context.SaveChangesAsync();

            // Tạo liên kết giữa khách sạn và khuyến mãi
            var accommodationPromotions = new List<AccommodationPromotion>();
            foreach (var accommodation in accommodations)
            {
                // Mỗi khách sạn có 1-2 khuyến mãi
                accommodationPromotions.Add(new AccommodationPromotion
                {
                    AccommodationId = accommodation.Id,
                    PromotionId = promotions[0].Id // Ưu đãi mùa hè
                });

                if (accommodation.StarRating == 5)
                {
                    accommodationPromotions.Add(new AccommodationPromotion
                    {
                        AccommodationId = accommodation.Id,
                        PromotionId = promotions[1].Id // Đặt sớm giảm giá
                    });
                }
                else
                {
                    accommodationPromotions.Add(new AccommodationPromotion
                    {
                        AccommodationId = accommodation.Id,
                        PromotionId = promotions[2].Id // Ưu đãi cuối tuần
                    });
                }
            }
            context.AccommodationPromotions.AddRange(accommodationPromotions);
            await context.SaveChangesAsync();
        }

        private static async Task SeedUsers(UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
        {
            // Tạo các vai trò nếu chưa tồn tại
            string[] roleNames = { "Admin", "User" };
            foreach (var roleName in roleNames)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }

            // Tạo tài khoản Admin nếu chưa tồn tại
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    FullName = "Admin Bến Tre",
                    Address = "Bến Tre, Việt Nam"
                };

                var result = await userManager.CreateAsync(adminUser, "Admin@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                    Console.WriteLine("Admin user created successfully");
                }
                else
                {
                    Console.WriteLine("Failed to create admin user:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"- {error.Description}");
                    }
                }
            }

            // Tạo tài khoản User nếu chưa tồn tại
            var userEmail = "<EMAIL>";
            var normalUser = await userManager.FindByEmailAsync(userEmail);
            if (normalUser == null)
            {
                normalUser = new ApplicationUser
                {
                    UserName = userEmail,
                    Email = userEmail,
                    EmailConfirmed = true,
                    FullName = "Khách Du Lịch",
                    Address = "Hồ Chí Minh, Việt Nam"
                };

                var result = await userManager.CreateAsync(normalUser, "User@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(normalUser, "User");
                    Console.WriteLine("Normal user created successfully");
                }
                else
                {
                    Console.WriteLine("Failed to create normal user:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"- {error.Description}");
                    }
                }
            }
        }
    }
}
