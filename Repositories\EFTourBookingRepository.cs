﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFTourBookingRepository : ITourBookingRepository
    {
        private readonly ApplicationDbContext _context;

        public EFTourBookingRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<TourBooking>> GetAllAsync()
        {
            return await _context.TourBookings
                .Include(tb => tb.ApplicationUser)
                .Include(tb => tb.TourBookingDetails)
                    .ThenInclude(tbd => tbd.Tour)
                .OrderByDescending(tb => tb.BookingDate)
                .ToListAsync();
        }

        public async Task<TourBooking> GetByIdAsync(int id)
        {
            return await _context.TourBookings
                .Include(tb => tb.ApplicationUser)
                .Include(tb => tb.TourBookingDetails)
                    .ThenInclude(tbd => tbd.Tour)
                .FirstOrDefaultAsync(tb => tb.Id == id);
        }

        public async Task<IEnumerable<TourBooking>> GetByUserIdAsync(string userId)
        {
            return await _context.TourBookings
                .Include(tb => tb.TourBookingDetails)
                    .ThenInclude(tbd => tbd.Tour)
                .Where(tb => tb.UserId == userId)
                .OrderByDescending(tb => tb.BookingDate)
                .ToListAsync();
        }

        public async Task AddAsync(TourBooking tourBooking)
        {
            try
            {
                _context.TourBookings.Add(tourBooking);
                await _context.SaveChangesAsync();

                // Đảm bảo ID đã được thiết lập sau khi lưu
                if (tourBooking.Id <= 0)
                {
                    throw new Exception("Không thể tạo ID cho đơn đặt tour.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thêm đơn đặt tour: {ex.Message}");
                throw; // Re-throw để controller có thể xử lý
            }
        }

        public async Task UpdateAsync(TourBooking tourBooking)
        {
            _context.TourBookings.Update(tourBooking);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var tourBooking = await _context.TourBookings.FindAsync(id);
            if (tourBooking != null)
            {
                _context.TourBookings.Remove(tourBooking);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.TourBookings.AnyAsync(tb => tb.Id == id);
        }
    }
}
