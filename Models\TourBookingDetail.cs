﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TourBookingDetail
    {
        public int Id { get; set; }
        
        public int TourBookingId { get; set; }
        
        public int TourId { get; set; }
        
        public int NumberOfAdults { get; set; } = 1;
        
        public int NumberOfChildren { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        public DateTime TourDate { get; set; }
        
        public TourBookingDetailStatus Status { get; set; } = TourBookingDetailStatus.Pending;
        
        [ForeignKey("TourBookingId")]
        public TourBooking TourBooking { get; set; }
        
        [ForeignKey("TourId")]
        public Tour Tour { get; set; }
        
        [NotMapped]
        public decimal TotalPrice => Price * (NumberOfAdults + (NumberOfChildren * 0.5m));
    }
    
    public enum TourBookingDetailStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
