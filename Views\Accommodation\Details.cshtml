@model ViVu.Models.Accommodation

@{
    ViewData["Title"] = Model.Name;
}

<div class="container mt-4">
    <nav aria-label="breadcrumb" class="modern-breadcrumb" data-aos="fade-up">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="Accommodation" asp-action="Index">Khách sạn</a></li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Name</li>
        </ol>
    </nav>

    <div class="row mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="col-md-8">
            <h1 class="mb-2">@Model.Name</h1>
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    @for (int i = 0; i < Model.StarRating; i++)
                    {
                        <i class="bi bi-star-fill text-warning"></i>
                    }
                </div>
                <span class="text-muted">
                    <i class="bi bi-geo-alt"></i> @Model.Address, @Model.Location?.Name, @Model.City?.Name
                </span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="#rooms" class="btn btn-modern btn-modern-primary">
                <i class="bi bi-calendar-check"></i> Đặt phòng ngay
            </a>
        </div>
    </div>

    <!-- Hình ảnh khách sạn -->
    <div class="row mb-4">
        <div class="col-md-8" data-aos="fade-right" data-aos-delay="150">
            <div id="hotelCarousel" class="carousel slide shadow-sm" data-bs-ride="carousel">
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#hotelCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                    <button type="button" data-bs-target="#hotelCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#hotelCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                </div>
                <div class="carousel-inner rounded">
                    <div class="carousel-item active">
                        @if (Model.Images != null && Model.Images.Any(i => i.IsPrimary))
                        {
                            var primaryImage = Model.Images.FirstOrDefault(i => i.IsPrimary);
                            <img src="@primaryImage?.Url" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="@Model.Name">
                        }
                        else
                        {
                            <img src="/images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="@Model.Name">
                        }
                    </div>
                    <div class="carousel-item">
                        <img src="/images/accommodations/amenities/hotel_ben-tre-riverside_pool.jpg" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="@Model.Name">
                    </div>
                    <div class="carousel-item">
                        <img src="/images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.jpg" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="@Model.Name">
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#hotelCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#hotelCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>
        <div class="col-md-4" data-aos="fade-left" data-aos-delay="200">
            <div class="row">
                <div class="col-6 mb-3">
                    <img src="/images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.jpg" class="img-fluid rounded shadow-sm hover-lift" style="width: 100%; height: 120px; object-fit: cover;" alt="@Model.Name">
                </div>
                <div class="col-6 mb-3">
                    <img src="/images/accommodations/rooms/hotel_ben-tre-riverside_room_deluxe_01.jpg" class="img-fluid rounded shadow-sm hover-lift" style="width: 100%; height: 120px; object-fit: cover;" alt="@Model.Name">
                </div>
                <div class="col-6 mb-3">
                    <img src="/images/accommodations/amenities/hotel_ben-tre-riverside_pool.jpg" class="img-fluid rounded shadow-sm hover-lift" style="width: 100%; height: 120px; object-fit: cover;" alt="@Model.Name">
                </div>
                <div class="col-6 mb-3">
                    <img src="/images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg" class="img-fluid rounded shadow-sm hover-lift" style="width: 100%; height: 120px; object-fit: cover;" alt="@Model.Name">
                </div>
            </div>
        </div>
    </div>

    <!-- Thông tin khách sạn -->
    <div class="row mb-5">
        <div class="col-md-8">
            <div class="modern-card mb-4" data-aos="fade-up" data-aos-delay="250">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Giới thiệu</h5>
                </div>
                <div class="card-body">
                    <p>@Model.Description</p>
                </div>
            </div>

            <div class="modern-card mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Tiện nghi</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if (Model.AccommodationAmenities != null && Model.AccommodationAmenities.Any())
                        {
                            foreach (var amenity in Model.AccommodationAmenities)
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex align-items-center">
                                        <i class="@amenity.Amenity?.Icon me-2 text-success"></i>
                                        <span>@amenity.Amenity?.Name</span>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="col-12">
                                <p class="text-muted">Không có thông tin về tiện nghi.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="modern-card mb-4" data-aos="fade-up" data-aos-delay="350">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Đánh giá</h5>
                    @if (User.Identity != null && User.Identity.IsAuthenticated)
                    {
                        <a asp-controller="Review" asp-action="Create" asp-route-type="Accommodation" asp-route-id="@Model.Id" class="btn btn-modern btn-modern-outline btn-sm">
                            <i class="bi bi-star"></i> Viết đánh giá
                        </a>
                    }
                </div>
                <div class="card-body">
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <h2 class="mb-0">@Model.Reviews.Average(r => r.Rating).ToString("0.0")</h2>
                                <div class="mb-2">
                                    @{
                                        var avgRating = Model.Reviews.Average(r => r.Rating);
                                        for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= Math.Floor(avgRating))
                                            {
                                                <i class="bi bi-star-fill text-warning"></i>
                                            }
                                            else if (i - avgRating < 1 && i - avgRating > 0)
                                            {
                                                <i class="bi bi-star-half text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="bi bi-star text-warning"></i>
                                            }
                                        }
                                    }
                                </div>
                                <p class="text-muted">@Model.Reviews.Count() đánh giá</p>
                            </div>
                            <div class="col-md-8">
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">5 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 8px;">
                                            @{
                                                var fiveStarPercent = Model.Reviews.Count(r => r.Rating == 5) * 100 / Model.Reviews.Count();
                                            }
                                            <div class="progress-bar bg-success" role="progressbar" style="width: @fiveStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">@Model.Reviews.Count(r => r.Rating == 5)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">4 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 8px;">
                                            @{
                                                var fourStarPercent = Model.Reviews.Count(r => r.Rating == 4) * 100 / Model.Reviews.Count();
                                            }
                                            <div class="progress-bar bg-success" role="progressbar" style="width: @fourStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">@Model.Reviews.Count(r => r.Rating == 4)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">3 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 8px;">
                                            @{
                                                var threeStarPercent = Model.Reviews.Count(r => r.Rating == 3) * 100 / Model.Reviews.Count();
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @threeStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">@Model.Reviews.Count(r => r.Rating == 3)</div>
                                </div>
                                <div class="row align-items-center mb-2">
                                    <div class="col-3">2 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 8px;">
                                            @{
                                                var twoStarPercent = Model.Reviews.Count(r => r.Rating == 2) * 100 / Model.Reviews.Count();
                                            }
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: @twoStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">@Model.Reviews.Count(r => r.Rating == 2)</div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="col-3">1 sao</div>
                                    <div class="col-7">
                                        <div class="progress" style="height: 8px;">
                                            @{
                                                var oneStarPercent = Model.Reviews.Count(r => r.Rating == 1) * 100 / Model.Reviews.Count();
                                            }
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: @oneStarPercent%"></div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">@Model.Reviews.Count(r => r.Rating == 1)</div>
                                </div>
                            </div>
                        </div>

                        <hr />

                        @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt).Take(5))
                        {
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <h6 class="mb-0">@review.User?.FullName</h6>
                                        <div>
                                            @for (int i = 0; i < review.Rating; i++)
                                            {
                                                <i class="bi bi-star-fill text-warning"></i>
                                            }
                                            @for (int i = review.Rating; i < 5; i++)
                                            {
                                                <i class="bi bi-star text-warning"></i>
                                            }
                                        </div>
                                    </div>
                                    <small class="text-muted">@review.CreatedAt.ToString("dd/MM/yyyy")</small>
                                </div>
                                <p>@review.Comment</p>
                            </div>
                        }

                        @if (Model.Reviews.Count() > 5)
                        {
                            <div class="text-center">
                                <button class="btn btn-outline-primary">Xem thêm đánh giá</button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <p class="text-muted mb-3">Chưa có đánh giá nào.</p>
                            @if (User.Identity != null && User.Identity.IsAuthenticated)
                            {
                                <a asp-controller="Review" asp-action="Create" asp-route-type="Accommodation" asp-route-id="@Model.Id" class="btn btn-primary">
                                    <i class="bi bi-star"></i> Viết đánh giá đầu tiên
                                </a>
                            }
                            else
                            {
                                <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right"></i> Đăng nhập để viết đánh giá
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="modern-card mb-4 sticky-top" style="top: 20px; z-index: 1;" data-aos="fade-left" data-aos-delay="250">
                <div class="card-header bg-white">
                    <h5 class="mb-0 text-success">Thông tin liên hệ</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="bi bi-geo-alt me-2 text-success"></i>Địa chỉ</h6>
                        <p>@Model.Address, @Model.Location?.Name, @Model.City?.Name</p>
                    </div>
                    <div class="mb-3">
                        <h6><i class="bi bi-telephone me-2 text-success"></i>Điện thoại</h6>
                        <p>0123 456 789</p>
                    </div>
                    <div class="mb-3">
                        <h6><i class="bi bi-envelope me-2 text-success"></i>Email</h6>
                        <p><EMAIL></p>
                    </div>
                    <div class="mb-3">
                        <h6><i class="bi bi-clock me-2 text-success"></i>Giờ nhận phòng</h6>
                        <p>Từ 14:00</p>
                    </div>
                    <div class="mb-3">
                        <h6><i class="bi bi-clock-history me-2 text-success"></i>Giờ trả phòng</h6>
                        <p>Trước 12:00</p>
                    </div>
                    <div class="d-grid">
                        <a href="#rooms" class="btn btn-modern btn-modern-primary">
                            <i class="bi bi-calendar-check me-2"></i>Đặt phòng ngay
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách phòng -->
    <div id="rooms" class="mb-5">
        <div class="section-title" data-aos="fade-up">
            <h2>Danh sách phòng</h2>
        </div>

        @if (Model.Rooms != null && Model.Rooms.Any())
        {
            <div class="row">
                @{
                    int roomIndex = 0;
                }
                @foreach (var room in Model.Rooms)
                {
                    <div class="col-md-6 mb-4" data-aos="fade-up" data-aos-delay="@(roomIndex * 100)">
                        <div class="modern-card h-100">
                            <div class="row g-0">
                                <div class="col-md-4">
                                    <img src="@(string.IsNullOrEmpty(room.ImageUrl) ? "/images/accommodations/rooms/hotel_ben-tre-riverside_room_standard_01.jpg" : room.ImageUrl)"
                                         class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="@room.Name">
                                </div>
                                <div class="col-md-8">
                                    <div class="card-body d-flex flex-column h-100">
                                        <h5 class="card-title">@room.Name</h5>
                                        <div class="mb-2">
                                            <span class="badge badge-modern badge-modern-info"><i class="bi bi-people"></i> @room.MaxOccupancy người</span>
                                            @if (room.IsAvailable)
                                            {
                                                <span class="badge badge-modern badge-modern-primary">Còn phòng</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-modern badge-modern-secondary">Hết phòng</span>
                                            }
                                        </div>
                                        <p class="card-text">@room.Description</p>

                                        @if (room.RoomAmenities != null && room.RoomAmenities.Any())
                                        {
                                            <div class="mb-3">
                                                @foreach (var amenity in room.RoomAmenities.Take(3))
                                                {
                                                    <span class="badge bg-light text-dark me-1 mb-1">
                                                        <i class="@amenity.Amenity?.Icon me-1 text-success"></i>
                                                        @amenity.Amenity?.Name
                                                    </span>
                                                }
                                                @if (room.RoomAmenities.Count() > 3)
                                                {
                                                    <span class="badge bg-light text-dark me-1 mb-1">+@(room.RoomAmenities.Count() - 3)</span>
                                                }
                                            </div>
                                        }

                                        <div class="mt-auto d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="text-success fw-bold fs-5">@room.PricePerNight.ToString("#,##0") VNĐ</span>
                                                <small class="text-muted d-block">/ đêm</small>
                                            </div>
                                            <form asp-controller="Booking" asp-action="AddToCart" method="post">
                                                <input type="hidden" name="roomId" value="@room.Id" />
                                                <input type="hidden" name="accommodationId" value="@Model.Id" />
                                                <button type="submit" class="btn btn-modern btn-modern-primary" @(room.IsAvailable ? "" : "disabled")>
                                                    <i class="bi bi-cart-plus"></i> Đặt phòng
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="alert alert-info">
                <p class="mb-0">Không có thông tin về phòng.</p>
            </div>
        }
    </div>

    <!-- Khách sạn tương tự -->
    @if (ViewBag.SimilarAccommodations != null && ((IEnumerable<ViVu.Models.Accommodation>)ViewBag.SimilarAccommodations).Any())
    {
        <div class="mb-5">
            <h2 class="mb-4">Khách sạn tương tự</h2>
            <div class="row">
                @foreach (var accommodation in (IEnumerable<ViVu.Models.Accommodation>)ViewBag.SimilarAccommodations)
                {
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/accommodations/amenities/hotel_ben-tre-riverside_garden.jpg" : accommodation.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="card-title mb-0">@accommodation.Name</h5>
                                    <div>
                                        @for (int i = 0; i < accommodation.StarRating; i++)
                                        {
                                            <i class="bi bi-star-fill text-warning"></i>
                                        }
                                    </div>
                                </div>
                                <p class="text-muted mb-2">
                                    <i class="bi bi-geo-alt"></i> @accommodation.Location?.Name, @accommodation.City?.Name
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                    <a asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        // Smooth scroll to rooms section when clicking "Book Now" button
        document.addEventListener('DOMContentLoaded', function() {
            const bookNowButtons = document.querySelectorAll('a[href="#rooms"]');
            bookNowButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const roomsSection = document.getElementById('rooms');
                    roomsSection.scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
}
