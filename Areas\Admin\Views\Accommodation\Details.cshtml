@model ViVu.Models.Accommodation
@{
    ViewData["Title"] = "Chi tiết chỗ ở";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết chỗ ở</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Chỗ ở</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-info-circle me-1"></i> Thông tin chi tiết</div>
                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit"></i> Chỉnh sửa
                    </a>
                    <a asp-action="Index" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" />
                </div>
                <div class="col-md-8">
                    <h2>@Model.Name</h2>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt"></i> @Model.Address
                    </p>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Địa điểm:</strong> @Model.Location?.Name</p>
                            <p><strong>Thành phố:</strong> @Model.City?.Name</p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <strong>Đánh giá:</strong>
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.StarRating)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-warning"></i>
                                    }
                                }
                            </p>
                            <p>
                                <strong>Nổi bật:</strong>
                                @if (Model.IsFeatured)
                                {
                                    <span class="badge bg-success">Có</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Không</span>
                                }
                            </p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h5>Mô tả</h5>
                        <p>@Model.Description</p>
                    </div>

                    <div class="mb-3">
                        <h5>Tiện nghi</h5>
                        <div class="row">
                            @if (Model.AccommodationAmenities != null && Model.AccommodationAmenities.Any())
                            {
                                foreach (var accommodationAmenity in Model.AccommodationAmenities)
                                {
                                    <div class="col-md-3 mb-2">
                                        <i class="fas fa-check text-success"></i> @accommodationAmenity.Amenity.Name
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="col-12">
                                    <p class="text-muted">Không có tiện nghi nào.</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-bed me-1"></i> Danh sách phòng</div>
                <a asp-area="Admin" asp-controller="Room" asp-action="Create" asp-route-accommodationId="@Model.Id" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> Thêm phòng
                </a>
            </div>
        </div>
        <div class="card-body">
            @if (Model.Rooms != null && Model.Rooms.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Hình ảnh</th>
                                <th>Tên phòng</th>
                                <th>Giá/đêm</th>
                                <th>Sức chứa</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var room in Model.Rooms)
                            {
                                <tr>
                                    <td>
                                        <img src="@room.ImageUrl" alt="@room.Name" style="width: 80px; height: 60px; object-fit: cover;" />
                                    </td>
                                    <td>@room.Name</td>
                                    <td>@room.PricePerNight.ToString("N0") VNĐ</td>
                                    <td>@room.MaxOccupancy người</td>
                                    <td>
                                        @if (room.IsAvailable)
                                        {
                                            <span class="badge bg-success">Có sẵn</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Không có sẵn</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a asp-area="Admin" asp-controller="Room" asp-action="Details" asp-route-id="@room.Id" class="btn btn-info btn-sm me-1">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-area="Admin" asp-controller="Room" asp-action="Edit" asp-route-id="@room.Id" class="btn btn-warning btn-sm me-1">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-area="Admin" asp-controller="Room" asp-action="Delete" asp-route-id="@room.Id" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Chưa có phòng nào cho chỗ ở này.
                </div>
            }
        </div>
    </div>
</div>
