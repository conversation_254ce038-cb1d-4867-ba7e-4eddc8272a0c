﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using ViVu.Models;
using ViVu.Models.ViewModels;

namespace ViVu.Controllers
{
    [Authorize]
    public class ReviewController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ReviewController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Review/Create
        public IActionResult Create(ReviewType type, int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            
            // Kiểm tra xem người dùng đã đánh giá chưa
            var existingReview = _context.Reviews.FirstOrDefault(r => 
                r.UserId == userId && 
                ((type == ReviewType.Accommodation && r.AccommodationId == id) ||
                 (type == ReviewType.Tour && r.TourId == id) ||
                 (type == ReviewType.Service && r.ServiceId == id)));
                
            if (existingReview != null)
            {
                return RedirectToAction("Edit", new { id = existingReview.Id });
            }
            
            var model = new ReviewViewModel
            {
                Type = type,
                ItemId = id
            };
            
            // Lấy thông tin về đối tượng được đánh giá
            switch (type)
            {
                case ReviewType.Accommodation:
                    var accommodation = _context.Accommodations.FirstOrDefault(a => a.Id == id);
                    if (accommodation == null)
                        return NotFound();
                    model.ItemName = accommodation.Name;
                    break;
                case ReviewType.Tour:
                    var tour = _context.Tours.FirstOrDefault(t => t.Id == id);
                    if (tour == null)
                        return NotFound();
                    model.ItemName = tour.Name;
                    break;
                case ReviewType.Service:
                    var service = _context.Services.FirstOrDefault(s => s.Id == id);
                    if (service == null)
                        return NotFound();
                    model.ItemName = service.Name;
                    break;
            }
            
            return View(model);
        }

        // POST: Review/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ReviewViewModel model)
        {
            if (ModelState.IsValid)
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                
                var review = new Review
                {
                    UserId = userId,
                    Rating = model.Rating,
                    Comment = model.Comment,
                    CreatedAt = DateTime.UtcNow
                };
                
                // Thiết lập ID tương ứng với loại đánh giá
                switch (model.Type)
                {
                    case ReviewType.Accommodation:
                        review.AccommodationId = model.ItemId;
                        break;
                    case ReviewType.Tour:
                        review.TourId = model.ItemId;
                        break;
                    case ReviewType.Service:
                        review.ServiceId = model.ItemId;
                        break;
                }
                
                _context.Reviews.Add(review);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = "Đánh giá của bạn đã được gửi thành công!";
                
                // Chuyển hướng về trang chi tiết tương ứng
                switch (model.Type)
                {
                    case ReviewType.Accommodation:
                        return RedirectToAction("Details", "Accommodation", new { id = model.ItemId });
                    case ReviewType.Tour:
                        return RedirectToAction("Details", "Tour", new { id = model.ItemId });
                    case ReviewType.Service:
                        return RedirectToAction("Details", "Service", new { id = model.ItemId });
                    default:
                        return RedirectToAction("Index", "Home");
                }
            }
            
            return View(model);
        }

        // GET: Review/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var review = await _context.Reviews.FindAsync(id);
            
            if (review == null)
            {
                return NotFound();
            }
            
            // Kiểm tra xem người dùng có quyền chỉnh sửa đánh giá này không
            if (review.UserId != userId)
            {
                return Forbid();
            }
            
            var model = new ReviewViewModel
            {
                Id = review.Id,
                Rating = review.Rating,
                Comment = review.Comment
            };
            
            // Xác định loại đánh giá và ID của đối tượng
            if (review.AccommodationId.HasValue)
            {
                model.Type = ReviewType.Accommodation;
                model.ItemId = review.AccommodationId.Value;
                var accommodation = await _context.Accommodations.FindAsync(review.AccommodationId.Value);
                model.ItemName = accommodation?.Name;
            }
            else if (review.TourId.HasValue)
            {
                model.Type = ReviewType.Tour;
                model.ItemId = review.TourId.Value;
                var tour = await _context.Tours.FindAsync(review.TourId.Value);
                model.ItemName = tour?.Name;
            }
            else if (review.ServiceId.HasValue)
            {
                model.Type = ReviewType.Service;
                model.ItemId = review.ServiceId.Value;
                var service = await _context.Services.FindAsync(review.ServiceId.Value);
                model.ItemName = service?.Name;
            }
            
            return View(model);
        }

        // POST: Review/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, ReviewViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }
            
            if (ModelState.IsValid)
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var review = await _context.Reviews.FindAsync(id);
                
                if (review == null)
                {
                    return NotFound();
                }
                
                // Kiểm tra xem người dùng có quyền chỉnh sửa đánh giá này không
                if (review.UserId != userId)
                {
                    return Forbid();
                }
                
                // Cập nhật thông tin đánh giá
                review.Rating = model.Rating;
                review.Comment = model.Comment;
                
                try
                {
                    _context.Update(review);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "Đánh giá của bạn đã được cập nhật thành công!";
                    
                    // Chuyển hướng về trang chi tiết tương ứng
                    switch (model.Type)
                    {
                        case ReviewType.Accommodation:
                            return RedirectToAction("Details", "Accommodation", new { id = model.ItemId });
                        case ReviewType.Tour:
                            return RedirectToAction("Details", "Tour", new { id = model.ItemId });
                        case ReviewType.Service:
                            return RedirectToAction("Details", "Service", new { id = model.ItemId });
                        default:
                            return RedirectToAction("Index", "Home");
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ReviewExists(review.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            
            return View(model);
        }

        // GET: Review/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .FirstOrDefaultAsync(r => r.Id == id);
                
            if (review == null)
            {
                return NotFound();
            }
            
            // Kiểm tra xem người dùng có quyền xóa đánh giá này không
            if (review.UserId != userId)
            {
                return Forbid();
            }
            
            return View(review);
        }

        // POST: Review/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var review = await _context.Reviews.FindAsync(id);
            
            if (review == null)
            {
                return NotFound();
            }
            
            // Kiểm tra xem người dùng có quyền xóa đánh giá này không
            if (review.UserId != userId)
            {
                return Forbid();
            }
            
            // Lưu thông tin để chuyển hướng sau khi xóa
            int? accommodationId = review.AccommodationId;
            int? tourId = review.TourId;
            int? serviceId = review.ServiceId;
            
            _context.Reviews.Remove(review);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = "Đánh giá của bạn đã được xóa thành công!";
            
            // Chuyển hướng về trang chi tiết tương ứng
            if (accommodationId.HasValue)
            {
                return RedirectToAction("Details", "Accommodation", new { id = accommodationId.Value });
            }
            else if (tourId.HasValue)
            {
                return RedirectToAction("Details", "Tour", new { id = tourId.Value });
            }
            else if (serviceId.HasValue)
            {
                return RedirectToAction("Details", "Service", new { id = serviceId.Value });
            }
            else
            {
                return RedirectToAction("Index", "Home");
            }
        }

        // GET: Review/MyReviews
        public async Task<IActionResult> MyReviews()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var reviews = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Accommodation)
                .Include(r => r.Tour)
                .Include(r => r.Service)
                .Where(r => r.UserId == userId)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
                
            return View(reviews);
        }

        private bool ReviewExists(int id)
        {
            return _context.Reviews.Any(e => e.Id == id);
        }
    }
}
