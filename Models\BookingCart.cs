﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class BookingCart
    {
        public List<BookingItem> Items { get; set; } = new List<BookingItem>();

        public void AddItem(BookingItem item)
        {
            var existingItem = Items.FirstOrDefault(i => i.RoomId == item.RoomId &&
                                                        i.CheckInDate == item.CheckInDate &&
                                                        i.CheckOutDate == item.CheckOutDate);
            if (existingItem != null)
            {
                existingItem.NumberOfRooms += item.NumberOfRooms;
                existingItem.NumberOfGuests += item.NumberOfGuests;
            }
            else
            {
                Items.Add(item);
            }
        }

        public void RemoveItem(int roomId)
        {
            Items.RemoveAll(i => i.RoomId == roomId);
        }

        // Phương thức này không còn cần thiết vì chúng ta đã có phương thức AddItem(BookingItem item)
        // Giữ lại để tương thích với mã cũ
        public void AddItem(Room room, int numberOfRooms, int numberOfGuests, DateTime checkInDate, DateTime checkOutDate)
        {
            var item = new BookingItem
            {
                RoomId = room.Id,
                RoomName = room.Name,
                AccommodationId = room.AccommodationId,
                AccommodationName = room.Accommodation?.Name,
                PricePerNight = room.PricePerNight,
                NumberOfRooms = numberOfRooms,
                NumberOfGuests = numberOfGuests,
                CheckInDate = checkInDate,
                CheckOutDate = checkOutDate,
                ImageUrl = room.ImageUrl
            };

            AddItem(item);
        }

        [NotMapped]
        public decimal TotalPrice => Items.Sum(i => i.TotalPrice);
    }
}
