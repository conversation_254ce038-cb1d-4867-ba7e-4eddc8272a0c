@model ViVu.Models.Service
@{
    ViewData["Title"] = "Xóa dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">X<PERSON><PERSON> dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Dịch vụ</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa dịch vụ
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i><PERSON>ạ<PERSON> c<PERSON> chắc chắn muốn xóa dịch vụ này?</h5>
                <p>Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến dịch vụ này sẽ bị xóa.</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">@Model.Name</h5>
                    
                    <div class="mb-3">
                        <strong>Địa điểm:</strong>
                        <span>@(Model.Location != null ? Model.Location.Name : ""), @(Model.City != null ? Model.City.Name : "")</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Thời gian:</strong>
                        <span>@Model.Duration phút</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Giá:</strong>
                        <span>@Model.Price.ToString("N0") VNĐ</span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" style="max-height: 200px; object-fit: cover;" />
                    }
                </div>
            </div>
            
            <form asp-action="Delete" method="post">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
