@model IEnumerable<ViVu.Models.TourBooking>

@{
    ViewData["Title"] = "Quản lý đặt tour";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <i class="fas fa-list me-1"></i>
                    Danh sách đặt tour
                </div>
                <div class="col-md-6">
                    <form asp-action="Index" method="get" class="d-flex justify-content-end">
                        <div class="input-group" style="max-width: 300px;">
                            <select name="status" class="form-select" onchange="this.form.submit()">
                                <option value="">Tất cả trạng thái</option>
                                @foreach (var status in ViewBag.Statuses)
                                {
                                    <option value="@status.Value" selected="@(status.Value == ViewBag.SelectedStatus)">@status.Text</option>
                                }
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="bookingsTable">
                    <thead>
                        <tr>
                            <th>Mã</th>
                            <th>Khách hàng</th>
                            <th>Ngày đặt</th>
                            <th>Ngày tour</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>#@item.Id</td>
                                <td>@item.ApplicationUser.FullName</td>
                                <td>@item.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@item.TourDate.ToString("dd/MM/yyyy")</td>
                                <td>@item.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    <span class="badge @(item.Status == TourBookingStatus.Pending ? "bg-warning" :
                                                       item.Status == TourBookingStatus.Confirmed ? "bg-primary" :
                                                       item.Status == TourBookingStatus.Completed ? "bg-success" : "bg-danger")">
                                        @(item.Status == TourBookingStatus.Pending ? "Chờ xác nhận" :
                                         item.Status == TourBookingStatus.Confirmed ? "Đã xác nhận" :
                                         item.Status == TourBookingStatus.Completed ? "Hoàn thành" : "Đã hủy")
                                    </span>
                                </td>
                                <td>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form asp-action="Delete" method="post" class="d-inline-block">
                                        <input type="hidden" name="id" value="@item.Id" />
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa đơn đặt tour này?');">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#bookingsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json'
                },
                order: [[2, 'desc']]
            });
        });
    </script>
}
