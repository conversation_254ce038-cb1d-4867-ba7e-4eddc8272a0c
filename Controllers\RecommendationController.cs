﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using ViVu.Models;
using ViVu.Repositories;
using ViVu.Services;

namespace ViVu.Controllers
{
    public class RecommendationController : Controller
    {
        private readonly IRecommendationService _recommendationService;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;
        private readonly OpenAIService _openAIService;
        private readonly OllamaService _ollamaService;

        public RecommendationController(
            IRecommendationService recommendationService,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            OpenAIService openAIService,
            OllamaService ollamaService)
        {
            _recommendationService = recommendationService;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _userManager = userManager;
            _context = context;
            _openAIService = openAIService;
            _ollamaService = ollamaService;
        }

        // GET: Recommendation
        public async Task<IActionResult> Index()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            UserPreference userPreference;

            if (!string.IsNullOrEmpty(userId))
            {
                // Get existing preferences for logged in user
                userPreference = await _recommendationService.GetUserPreferencesAsync(userId);

                // Lấy lịch sử tìm kiếm gần đây của người dùng
                var recentSearches = await _recommendationService.GetUserSearchHistoryAsync(userId, 5);

                // Filter out any null entries or entries with null LocationId
                recentSearches = recentSearches.Where(s => s != null &&
                    (s.LocationId == null || (s.LocationId.HasValue && s.Location != null))).ToList();

                ViewBag.RecentSearches = recentSearches;

                // Lấy các địa điểm phổ biến dựa trên lịch sử tìm kiếm
                var popularLocations = await _recommendationService.GetPopularLocationsAsync(5);

                // Filter out any null locations
                popularLocations = popularLocations.Where(l => l != null).ToList();

                ViewBag.PopularLocations = popularLocations;
            }
            else
            {
                // Create default preferences for anonymous user
                userPreference = new UserPreference
                {
                    PrefersTours = true,
                    PrefersIndependentTravel = true,
                    MinBudget = 500000,
                    MaxBudget = 2000000,
                    MinDuration = 2,
                    MaxDuration = 5,
                    PrefersNature = true,
                    PrefersHistory = true,
                    PrefersFood = true,
                    PrefersAdventure = false,
                    PrefersRelaxation = true
                };

                // Lấy các địa điểm phổ biến cho người dùng ẩn danh
                var popularLocations = await _recommendationService.GetPopularLocationsAsync(5);

                // Filter out any null locations
                popularLocations = popularLocations.Where(l => l != null).ToList();

                ViewBag.PopularLocations = popularLocations;
            }

            // Get locations and cities for dropdowns
            var locations = await _locationRepository.GetAllAsync();
            var cities = await _cityRepository.GetAllAsync();

            var viewModel = new RecommendationViewModel
            {
                UserPreference = userPreference,
                Locations = new SelectList(locations, "Id", "Name"),
                Cities = new SelectList(cities, "Id", "Name")
            };

            return View(viewModel);
        }

        // POST: Recommendation/GetRecommendations
        [HttpPost]
        public async Task<IActionResult> GetRecommendations(UserPreference preferences)
        {
            // Set loading message for AI processing
            ViewData["LoadingMessage"] = "AI đang tạo gợi ý du lịch cho bạn...";

            // Kiểm tra xem preferences có null không
            if (preferences == null)
            {
                preferences = new UserPreference();
            }

            // Ghi log chi tiết về form data để gỡ lỗi
            Console.WriteLine("=== Form Values ===");
            foreach (var key in Request.Form.Keys)
            {
                Console.WriteLine($"{key}: {Request.Form[key]}");
            }
            Console.WriteLine("===================");

            // Gọi phương thức debug chi tiết
            DebugFormData();

            // Cải thiện cách xử lý dữ liệu form
            // Xử lý các checkbox với cả giá trị true và false
            var formKeys = Request.Form.Keys.ToList();

            // Ghi log chi tiết về preferences trước khi xử lý
            Console.WriteLine("=== User Preferences Before Processing ===");
            Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
            Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
            Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
            Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
            Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
            Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
            Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
            Console.WriteLine("========================================");

            // Xử lý loại hình du lịch
            preferences.PrefersTours = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersTours");
            preferences.PrefersIndependentTravel = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersIndependentTravel");

            // Xử lý sở thích
            preferences.PrefersNature = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersNature");
            preferences.PrefersHistory = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersHistory");
            preferences.PrefersFood = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersFood");
            preferences.PrefersAdventure = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersAdventure");
            preferences.PrefersRelaxation = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersRelaxation");

            // Xử lý hoạt động cụ thể
            preferences.InterestedInCooking = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInCooking");
            preferences.InterestedInCrafts = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInCrafts");
            preferences.InterestedInFarming = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInFarming");
            preferences.InterestedInBoating = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInBoating");
            preferences.InterestedInCycling = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInCycling");
            preferences.InterestedInFishing = ProcessBooleanFormValue(formKeys, "UserPreference.InterestedInFishing");

            // Xử lý đi cùng ai
            preferences.TravelingAlone = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingAlone");
            preferences.TravelingAsCouple = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingAsCouple");
            preferences.TravelingWithFriends = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithFriends");
            preferences.TravelingWithChildren = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithChildren");
            preferences.TravelingWithElders = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithElders");

            // Xử lý kinh nghiệm du lịch
            preferences.HasVisitedBenTreBefore = ProcessBooleanFormValue(formKeys, "UserPreference.HasVisitedBenTreBefore");
            preferences.HasVisitedMekongDeltaBefore = ProcessBooleanFormValue(formKeys, "UserPreference.HasVisitedMekongDeltaBefore");

            // Log preferences for debugging
            Console.WriteLine("=== Processed User Preferences ===");
            Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
            Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
            Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
            Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
            Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
            Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
            Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
            Console.WriteLine($"InterestedInCooking: {preferences.InterestedInCooking}");
            Console.WriteLine($"InterestedInCrafts: {preferences.InterestedInCrafts}");
            Console.WriteLine($"InterestedInFarming: {preferences.InterestedInFarming}");
            Console.WriteLine($"InterestedInBoating: {preferences.InterestedInBoating}");
            Console.WriteLine($"InterestedInCycling: {preferences.InterestedInCycling}");
            Console.WriteLine($"InterestedInFishing: {preferences.InterestedInFishing}");
            Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
            Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
            Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
            Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
            Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");
            Console.WriteLine("================================");

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Save user preferences if logged in
            if (!string.IsNullOrEmpty(userId))
            {
                preferences.UserId = userId;
                await _recommendationService.SaveUserPreferencesAsync(preferences);

                // Tạo lịch sử tìm kiếm dựa trên sở thích
                var searchHistory = new SearchHistory
                {
                    UserId = userId,
                    SearchDate = DateTime.UtcNow,
                    SearchType = "Recommendation",
                    RelatedToNature = preferences.PrefersNature,
                    RelatedToHistory = preferences.PrefersHistory,
                    RelatedToFood = preferences.PrefersFood,
                    RelatedToAdventure = preferences.PrefersAdventure,
                    RelatedToRelaxation = preferences.PrefersRelaxation,
                    InterestLevel = 5 // Mức độ quan tâm cao nhất
                };

                // Lưu lịch sử tìm kiếm
                await _recommendationService.TrackSearchHistoryAsync(searchHistory);
            }

            // Log preferences before generating recommendations
            Console.WriteLine("=== User Preferences Before Generating Recommendations ===");
            Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
            Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
            Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
            Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
            Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
            Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
            Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
            Console.WriteLine($"InterestedInCooking: {preferences.InterestedInCooking}");
            Console.WriteLine($"InterestedInCrafts: {preferences.InterestedInCrafts}");
            Console.WriteLine($"InterestedInFarming: {preferences.InterestedInFarming}");
            Console.WriteLine($"InterestedInBoating: {preferences.InterestedInBoating}");
            Console.WriteLine($"InterestedInCycling: {preferences.InterestedInCycling}");
            Console.WriteLine($"InterestedInFishing: {preferences.InterestedInFishing}");
            Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
            Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
            Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
            Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
            Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");
            Console.WriteLine("=================================================");

            // Generate recommendations
            var recommendations = await _recommendationService.GenerateRecommendationsAsync(preferences, userId);

            // Sắp xếp lại các gợi ý theo mức độ ưu tiên
            recommendations = recommendations.OrderByDescending(r => r.Priority).ToList();

            // Get related items for recommendations
            var relatedTours = new Dictionary<int, Tour>();
            var relatedAccommodations = new Dictionary<int, Accommodation>();
            var relatedServices = new Dictionary<int, Service>();
            var relatedLocations = new Dictionary<int, Location>();
            var relatedVehicles = new Dictionary<int, Vehicle>();

            foreach (var recommendation in recommendations)
            {
                if (!string.IsNullOrEmpty(recommendation.RelatedTourIds))
                {
                    var tours = await _recommendationService.GetRelatedToursAsync(recommendation.RelatedTourIds);
                    foreach (var tour in tours)
                    {
                        if (!relatedTours.ContainsKey(tour.Key))
                        {
                            relatedTours.Add(tour.Key, tour.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedAccommodationIds))
                {
                    var accommodations = await _recommendationService.GetRelatedAccommodationsAsync(recommendation.RelatedAccommodationIds);
                    foreach (var accommodation in accommodations)
                    {
                        if (!relatedAccommodations.ContainsKey(accommodation.Key))
                        {
                            relatedAccommodations.Add(accommodation.Key, accommodation.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedServiceIds))
                {
                    var services = await _recommendationService.GetRelatedServicesAsync(recommendation.RelatedServiceIds);
                    foreach (var service in services)
                    {
                        if (!relatedServices.ContainsKey(service.Key))
                        {
                            relatedServices.Add(service.Key, service.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedLocationIds))
                {
                    var locations = await _recommendationService.GetRelatedLocationsAsync(recommendation.RelatedLocationIds);
                    foreach (var location in locations)
                    {
                        if (!relatedLocations.ContainsKey(location.Key))
                        {
                            relatedLocations.Add(location.Key, location.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds))
                {
                    // Lấy thông tin phương tiện liên quan
                    var vehicleIds = recommendation.RelatedVehicleIds.Split(',').Select(id => int.Parse(id)).ToList();
                    var vehicles = await _context.Vehicles
                        .Include(v => v.Location)
                        .Include(v => v.City)
                        .Where(v => vehicleIds.Contains(v.Id))
                        .ToListAsync();

                    foreach (var vehicle in vehicles)
                    {
                        if (!relatedVehicles.ContainsKey(vehicle.Id))
                        {
                            relatedVehicles.Add(vehicle.Id, vehicle);
                        }
                    }
                }
            }

            // Create view model
            var viewModel = new RecommendationViewModel
            {
                UserPreference = preferences,
                Recommendations = recommendations,
                RelatedTours = relatedTours,
                RelatedAccommodations = relatedAccommodations,
                RelatedServices = relatedServices,
                RelatedLocations = relatedLocations,
                RelatedVehicles = relatedVehicles
            };

            return View("Results", viewModel);
        }

        // GET: Recommendation/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var recommendation = await _context.TravelRecommendations.FindAsync(id);
            if (recommendation == null)
            {
                return NotFound();
            }

            // Mark as viewed
            recommendation.IsViewed = true;
            recommendation.ViewCount++;
            recommendation.LastViewedDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            // Get related items
            var relatedTours = await _recommendationService.GetRelatedToursAsync(recommendation.RelatedTourIds);
            var relatedAccommodations = await _recommendationService.GetRelatedAccommodationsAsync(recommendation.RelatedAccommodationIds);
            var relatedServices = await _recommendationService.GetRelatedServicesAsync(recommendation.RelatedServiceIds);
            var relatedLocations = await _recommendationService.GetRelatedLocationsAsync(recommendation.RelatedLocationIds);

            // Lấy thông tin phương tiện liên quan
            var relatedVehicles = new Dictionary<int, Vehicle>();
            if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds))
            {
                var vehicleIds = recommendation.RelatedVehicleIds.Split(',').Select(id => int.Parse(id)).ToList();
                var vehicles = await _context.Vehicles
                    .Include(v => v.Location)
                    .Include(v => v.City)
                    .Where(v => vehicleIds.Contains(v.Id))
                    .ToListAsync();

                foreach (var vehicle in vehicles)
                {
                    relatedVehicles.Add(vehicle.Id, vehicle);
                }
            }

            // Lấy các gợi ý tương tự
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            List<TravelRecommendation> similarRecommendations = new List<TravelRecommendation>();

            if (!string.IsNullOrEmpty(userId))
            {
                // Lấy các gợi ý tương tự dựa trên sở thích người dùng
                similarRecommendations = _context.TravelRecommendations
                    .Where(r => r.Id != id && r.UserId == userId)
                    .OrderByDescending(r => r.CreatedDate)
                    .Take(3)
                    .ToList();
            }

            if (similarRecommendations.Count < 3)
            {
                // Bổ sung thêm các gợi ý tương tự dựa trên loại gợi ý
                var additionalRecommendations = _context.TravelRecommendations
                    .Where(r => r.Id != id &&
                           (r.RecommendationType == recommendation.RecommendationType ||
                            r.IncludesNature == recommendation.IncludesNature ||
                            r.IncludesHistory == recommendation.IncludesHistory ||
                            r.IncludesFood == recommendation.IncludesFood ||
                            r.IncludesAdventure == recommendation.IncludesAdventure ||
                            r.IncludesRelaxation == recommendation.IncludesRelaxation))
                    .OrderByDescending(r => r.CreatedDate)
                    .Take(3 - similarRecommendations.Count)
                    .ToList();

                similarRecommendations.AddRange(additionalRecommendations);
            }

            // Lưu lịch sử xem chi tiết
            if (!string.IsNullOrEmpty(userId))
            {
                var searchHistory = new SearchHistory
                {
                    UserId = userId,
                    SearchDate = DateTime.UtcNow,
                    SearchType = "RecommendationDetails",
                    RelatedToNature = recommendation.IncludesNature,
                    RelatedToHistory = recommendation.IncludesHistory,
                    RelatedToFood = recommendation.IncludesFood,
                    RelatedToAdventure = recommendation.IncludesAdventure,
                    RelatedToRelaxation = recommendation.IncludesRelaxation,
                    InterestLevel = 5, // Mức độ quan tâm cao nhất
                    ResultClicked = true
                };

                await _recommendationService.TrackSearchHistoryAsync(searchHistory);
            }

            // Create view model
            var viewModel = new RecommendationViewModel
            {
                Recommendations = new List<TravelRecommendation> { recommendation },
                SimilarRecommendations = similarRecommendations,
                RelatedTours = relatedTours,
                RelatedAccommodations = relatedAccommodations,
                RelatedServices = relatedServices,
                RelatedLocations = relatedLocations,
                RelatedVehicles = relatedVehicles
            };

            return View(viewModel);
        }

        // POST: Recommendation/SaveRecommendation/5
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> SaveRecommendation(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var recommendation = await _context.TravelRecommendations.FindAsync(id);

            if (recommendation == null)
            {
                return NotFound();
            }

            // Save recommendation for user
            recommendation.IsSaved = true;
            recommendation.UserId = userId;
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Lịch trình đã được lưu thành công!";
            return RedirectToAction(nameof(Details), new { id = recommendation.Id });
        }

        // GET: Recommendation/SavedRecommendations
        [Authorize]
        public async Task<IActionResult> SavedRecommendations()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var savedRecommendations = _context.TravelRecommendations
                .Where(r => r.UserId == userId && r.IsSaved)
                .OrderByDescending(r => r.CreatedDate)
                .ToList();

            return View(savedRecommendations);
        }

        // POST: Recommendation/RemoveSavedRecommendation/5
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> RemoveSavedRecommendation(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var recommendation = await _context.TravelRecommendations.FindAsync(id);

            if (recommendation == null)
            {
                return NotFound();
            }

            // Check if the recommendation belongs to the current user
            if (recommendation.UserId != userId)
            {
                return Forbid();
            }

            // Mark as not saved
            recommendation.IsSaved = false;
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Lịch trình đã được xóa khỏi danh sách đã lưu!";
            return RedirectToAction(nameof(SavedRecommendations));
        }

        // GET: Recommendation/AISettings
        [Authorize]
        public async Task<IActionResult> AISettings()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userPreference = await _recommendationService.GetUserPreferencesAsync(userId);

            return View(userPreference);
        }

        // POST: Recommendation/AISettings
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> AISettings(UserPreference preferences)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Lấy preferences hiện tại
            var currentPreferences = await _recommendationService.GetUserPreferencesAsync(userId);

            // Cập nhật chỉ các thuộc tính liên quan đến AI
            currentPreferences.UseAI = preferences.UseAI;
            currentPreferences.AIPrompt = preferences.AIPrompt;

            // Lưu lại
            await _recommendationService.SaveUserPreferencesAsync(currentPreferences);

            TempData["Message"] = "Cài đặt AI đã được lưu thành công!";
            return RedirectToAction(nameof(Index));
        }

        // POST: Recommendation/GenerateAIInsight
        [HttpPost]
        public async Task<IActionResult> GenerateAIInsight()
        {
            // Đọc dữ liệu từ form
            string destination = Request.Form["destination"];
            string interests = Request.Form["interests"];

            // Log to both Console and Debug for maximum visibility
            Console.WriteLine($"=== GenerateAIInsight called with destination: {destination}, interests: {interests} ===");
            System.Diagnostics.Debug.WriteLine($"=== GenerateAIInsight called with destination: {destination}, interests: {interests} ===");

            // Log request details
            Console.WriteLine($"Request Time: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}");
            Console.WriteLine($"Thread ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}");
            Console.WriteLine($"Route: {HttpContext.Request.Path}");
            Console.WriteLine($"Method: {HttpContext.Request.Method}");
            Console.WriteLine($"Content Type: {HttpContext.Request.ContentType}");

            // Log query string
            Console.WriteLine("Query string parameters:");
            foreach (var key in HttpContext.Request.Query.Keys)
            {
                Console.WriteLine($"  {key}: {HttpContext.Request.Query[key]}");
            }

            // Log form data
            Console.WriteLine("Form data:");
            foreach (var key in HttpContext.Request.Form.Keys)
            {
                Console.WriteLine($"  {key}: {HttpContext.Request.Form[key]}");
            }

            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(destination))
                {
                    Console.WriteLine("ERROR: destination is null or empty");
                    return Json(new { success = false, error = "Vui lòng chọn điểm đến" });
                }

                if (string.IsNullOrWhiteSpace(interests))
                {
                    Console.WriteLine("ERROR: interests is null or empty");
                    return Json(new { success = false, error = "Vui lòng chọn sở thích" });
                }

                string insight;
                Console.WriteLine("About to call Ollama service...");

                try
                {
                    // Thử sử dụng Ollama trước
                    Console.WriteLine("Calling _ollamaService.GenerateTravelInsights...");
                    insight = await _ollamaService.GenerateTravelInsights(destination, interests);
                    Console.WriteLine($"Received response from Ollama, length: {insight?.Length ?? 0}");

                    // Kiểm tra xem phản hồi từ Ollama có hợp lệ không
                    if (string.IsNullOrWhiteSpace(insight) || insight.Contains("Rất tiếc"))
                    {
                        Console.WriteLine("Ollama response was empty or contained an error. Falling back to OpenAI.");
                        throw new Exception("Ollama response was invalid");
                    }

                    Console.WriteLine("Ollama response is valid.");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error using Ollama: {ex.Message}. Falling back to OpenAI.");
                    Console.WriteLine($"Exception type: {ex.GetType().Name}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");

                    // Fallback to OpenAI if Ollama fails
                    Console.WriteLine("Calling _openAIService.GenerateTravelInsights...");
                    insight = await _openAIService.GenerateTravelInsights(destination, interests);
                    Console.WriteLine($"Received response from OpenAI, length: {insight?.Length ?? 0}");
                }

                Console.WriteLine("Returning successful JSON response");
                var response = new { success = true, insight = insight };
                Console.WriteLine($"Response: {System.Text.Json.JsonSerializer.Serialize(response)}");
                return Json(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GenerateAIInsight: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return Json(new { success = false, error = ex.Message });
            }
        }

        // POST: Recommendation/GetRecommendationsFromSimpleForm
        [HttpPost]
        public async Task<IActionResult> GetRecommendationsFromSimpleForm(UserPreference preferences)
        {
            // Set loading message for AI processing
            ViewData["LoadingMessage"] = "AI đang tạo gợi ý du lịch cho bạn...";

            // Log form data for debugging
            Console.WriteLine("=== Simple Form Values ===");
            foreach (var key in Request.Form.Keys)
            {
                Console.WriteLine($"{key}: {Request.Form[key]}");
            }
            Console.WriteLine("=========================");

            // Ensure preferences is not null
            if (preferences == null)
            {
                preferences = new UserPreference();
            }

            // Log preferences before processing
            Console.WriteLine("=== User Preferences Before Processing (Simple Form) ===");
            Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
            Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
            Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
            Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
            Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
            Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
            Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
            Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
            Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
            Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
            Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
            Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");
            Console.WriteLine($"MinBudget: {preferences.MinBudget}");
            Console.WriteLine($"MaxBudget: {preferences.MaxBudget}");
            Console.WriteLine("=======================================================");

            // Process form data manually to ensure correct values
            var formKeys = Request.Form.Keys.ToList();

            // Process travel type
            preferences.PrefersTours = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersTours");
            preferences.PrefersIndependentTravel = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersIndependentTravel");

            // Process interests
            preferences.PrefersNature = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersNature");
            preferences.PrefersHistory = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersHistory");
            preferences.PrefersFood = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersFood");
            preferences.PrefersAdventure = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersAdventure");
            preferences.PrefersRelaxation = ProcessBooleanFormValue(formKeys, "UserPreference.PrefersRelaxation");

            // Process travel companions
            preferences.TravelingAlone = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingAlone");
            preferences.TravelingAsCouple = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingAsCouple");
            preferences.TravelingWithFriends = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithFriends");
            preferences.TravelingWithChildren = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithChildren");
            preferences.TravelingWithElders = ProcessBooleanFormValue(formKeys, "UserPreference.TravelingWithElders");

            // Process budget
            if (formKeys.Contains("UserPreference.MinBudget"))
            {
                if (int.TryParse(Request.Form["UserPreference.MinBudget"], out int minBudget))
                {
                    preferences.MinBudget = minBudget;
                }
            }

            if (formKeys.Contains("UserPreference.MaxBudget"))
            {
                if (int.TryParse(Request.Form["UserPreference.MaxBudget"], out int maxBudget))
                {
                    preferences.MaxBudget = maxBudget;
                }
            }

            // Set default duration
            preferences.MinDuration = 2;
            preferences.MaxDuration = 5;

            // Enable AI for recommendations
            preferences.UseAI = true;

            // Log preferences after processing
            Console.WriteLine("=== User Preferences After Processing (Simple Form) ===");
            Console.WriteLine($"PrefersTours: {preferences.PrefersTours}");
            Console.WriteLine($"PrefersIndependentTravel: {preferences.PrefersIndependentTravel}");
            Console.WriteLine($"PrefersNature: {preferences.PrefersNature}");
            Console.WriteLine($"PrefersHistory: {preferences.PrefersHistory}");
            Console.WriteLine($"PrefersFood: {preferences.PrefersFood}");
            Console.WriteLine($"PrefersAdventure: {preferences.PrefersAdventure}");
            Console.WriteLine($"PrefersRelaxation: {preferences.PrefersRelaxation}");
            Console.WriteLine($"TravelingAlone: {preferences.TravelingAlone}");
            Console.WriteLine($"TravelingAsCouple: {preferences.TravelingAsCouple}");
            Console.WriteLine($"TravelingWithFriends: {preferences.TravelingWithFriends}");
            Console.WriteLine($"TravelingWithChildren: {preferences.TravelingWithChildren}");
            Console.WriteLine($"TravelingWithElders: {preferences.TravelingWithElders}");
            Console.WriteLine($"MinBudget: {preferences.MinBudget}");
            Console.WriteLine($"MaxBudget: {preferences.MaxBudget}");
            Console.WriteLine("======================================================");

            // Save user preferences if logged in
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (!string.IsNullOrEmpty(userId))
            {
                preferences.UserId = userId;
                await _recommendationService.SaveUserPreferencesAsync(preferences);

                // Track search history
                var searchHistory = new SearchHistory
                {
                    UserId = userId,
                    SearchDate = DateTime.UtcNow,
                    SearchType = "SimpleRecommendation",
                    RelatedToNature = preferences.PrefersNature,
                    RelatedToHistory = preferences.PrefersHistory,
                    RelatedToFood = preferences.PrefersFood,
                    RelatedToAdventure = preferences.PrefersAdventure,
                    RelatedToRelaxation = preferences.PrefersRelaxation,
                    InterestLevel = 5 // High interest level
                };

                await _recommendationService.TrackSearchHistoryAsync(searchHistory);
            }

            // Generate recommendations
            var recommendations = await _recommendationService.GenerateRecommendationsAsync(preferences, userId);

            // Sort recommendations by priority
            recommendations = recommendations.OrderByDescending(r => r.Priority).ToList();

            // Get related items for recommendations
            var relatedTours = new Dictionary<int, Tour>();
            var relatedAccommodations = new Dictionary<int, Accommodation>();
            var relatedServices = new Dictionary<int, Service>();
            var relatedLocations = new Dictionary<int, Location>();
            var relatedVehicles = new Dictionary<int, Vehicle>();

            foreach (var recommendation in recommendations)
            {
                if (!string.IsNullOrEmpty(recommendation.RelatedTourIds))
                {
                    var tours = await _recommendationService.GetRelatedToursAsync(recommendation.RelatedTourIds);
                    foreach (var tour in tours)
                    {
                        if (!relatedTours.ContainsKey(tour.Key))
                        {
                            relatedTours.Add(tour.Key, tour.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedAccommodationIds))
                {
                    var accommodations = await _recommendationService.GetRelatedAccommodationsAsync(recommendation.RelatedAccommodationIds);
                    foreach (var accommodation in accommodations)
                    {
                        if (!relatedAccommodations.ContainsKey(accommodation.Key))
                        {
                            relatedAccommodations.Add(accommodation.Key, accommodation.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedServiceIds))
                {
                    var services = await _recommendationService.GetRelatedServicesAsync(recommendation.RelatedServiceIds);
                    foreach (var service in services)
                    {
                        if (!relatedServices.ContainsKey(service.Key))
                        {
                            relatedServices.Add(service.Key, service.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedLocationIds))
                {
                    var locations = await _recommendationService.GetRelatedLocationsAsync(recommendation.RelatedLocationIds);
                    foreach (var location in locations)
                    {
                        if (!relatedLocations.ContainsKey(location.Key))
                        {
                            relatedLocations.Add(location.Key, location.Value);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(recommendation.RelatedVehicleIds))
                {
                    var vehicleIds = recommendation.RelatedVehicleIds.Split(',').Select(id => int.Parse(id)).ToList();
                    var vehicles = await _context.Vehicles
                        .Include(v => v.Location)
                        .Include(v => v.City)
                        .Where(v => vehicleIds.Contains(v.Id))
                        .ToListAsync();

                    foreach (var vehicle in vehicles)
                    {
                        if (!relatedVehicles.ContainsKey(vehicle.Id))
                        {
                            relatedVehicles.Add(vehicle.Id, vehicle);
                        }
                    }
                }
            }

            // Create view model
            var viewModel = new RecommendationViewModel
            {
                UserPreference = preferences,
                Recommendations = recommendations,
                RelatedTours = relatedTours,
                RelatedAccommodations = relatedAccommodations,
                RelatedServices = relatedServices,
                RelatedLocations = relatedLocations,
                RelatedVehicles = relatedVehicles
            };

            return View("Results", viewModel);
        }

        // POST: Recommendation/GenerateCustomTour
        [HttpPost]
        public async Task<IActionResult> GenerateCustomTour(string preferences, string duration, string budget)
        {
            try
            {
                string tourSuggestion;

                try
                {
                    // Thử sử dụng Ollama trước
                    tourSuggestion = await _ollamaService.GenerateCustomTourSuggestion(preferences, duration, budget);

                    // Kiểm tra xem phản hồi từ Ollama có hợp lệ không
                    if (string.IsNullOrWhiteSpace(tourSuggestion) || tourSuggestion.Contains("Rất tiếc"))
                    {
                        Console.WriteLine("Ollama response was empty or contained an error. Falling back to OpenAI.");
                        throw new Exception("Ollama response was invalid");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error using Ollama: {ex.Message}. Falling back to OpenAI.");

                    // Fallback to OpenAI if Ollama fails
                    tourSuggestion = await _openAIService.GenerateCustomTourSuggestion(preferences, duration, budget);
                }

                return Json(new { success = true, suggestion = tourSuggestion });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        // GET: Recommendation/AIFeatures
        public IActionResult AIFeatures()
        {
            return View();
        }

        // GET: Recommendation/SimpleForm
        public async Task<IActionResult> SimpleForm()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            UserPreference userPreference;

            if (!string.IsNullOrEmpty(userId))
            {
                // Get existing preferences for logged in user
                userPreference = await _recommendationService.GetUserPreferencesAsync(userId);
            }
            else
            {
                // Create default preferences for anonymous user
                userPreference = new UserPreference
                {
                    PrefersTours = false,
                    PrefersIndependentTravel = false,
                    MinBudget = 1000000,
                    MaxBudget = 1500000,
                    MinDuration = 2,
                    MaxDuration = 5,
                    PrefersNature = false,
                    PrefersHistory = false,
                    PrefersFood = false,
                    PrefersAdventure = false,
                    PrefersRelaxation = false,
                    TravelingAlone = false,
                    TravelingAsCouple = false,
                    TravelingWithFriends = false,
                    TravelingWithChildren = false,
                    TravelingWithElders = false
                };
            }

            var viewModel = new RecommendationViewModel
            {
                UserPreference = userPreference
            };

            return View(viewModel);
        }

        // Helper method to process boolean form values
        private bool ProcessBooleanFormValue(List<string> formKeys, string key)
        {
            // Check if the key exists in the form
            if (!formKeys.Contains(key))
            {
                Console.WriteLine($"WARNING: Key '{key}' not found in form");
                return false;
            }

            // Get the value from the form
            var value = Request.Form[key].ToString();

            // Log the value for debugging
            Console.WriteLine($"Processing form value: {key} = {value}");

            // Return true if the value is "true", otherwise false
            bool result = value.Equals("true", StringComparison.OrdinalIgnoreCase);
            Console.WriteLine($"Result for {key}: {result}");
            return result;
        }

        // Helper method to debug form data
        private void DebugFormData()
        {
            Console.WriteLine("\n=== DETAILED FORM DATA DEBUG ===");
            Console.WriteLine($"Form count: {Request.Form.Count}");
            Console.WriteLine("Form keys:");
            foreach (var key in Request.Form.Keys)
            {
                Console.WriteLine($"  Key: '{key}'");
                var values = Request.Form[key];
                Console.WriteLine($"  Values count: {values.Count}");
                for (int i = 0; i < values.Count; i++)
                {
                    Console.WriteLine($"    Value[{i}]: '{values[i]}'");
                }
            }
            Console.WriteLine("=================================\n");
        }
    }
}
