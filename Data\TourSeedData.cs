﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Data
{
    public static class TourSeedData
    {
        public static void SeedTours(ApplicationDbContext context)
        {
            // Kiểm tra xem đã có tour nào trong cơ sở dữ liệu chưa
            if (context.Tours.Any())
            {
                return; // Đã có dữ liệu, không cần seed
            }

            // Lấy các địa điểm và thành phố hiện có
            var benTreLocation = context.Locations.FirstOrDefault(l => l.Name.Contains("Bến Tre"));
            var benTreCity = context.Cities.FirstOrDefault(c => c.Name.Contains("Bến Tre"));

            // Nếu không có địa điểm hoặc thành phố Bến Tre, tạo mới
            if (benTreLocation == null)
            {
                benTreLocation = new Location
                {
                    Name = "Bến Tre",
                    Description = "Tỉnh Bến Tre nằm ở khu vực Đồng bằng sông <PERSON>, Việt Nam."
                };
                context.Locations.Add(benTreLocation);
            }

            if (benTreCity == null)
            {
                benTreCity = new City
                {
                    Name = "Bến Tre",
                    Description = "Thành phố Bến Tre là trung tâm kinh tế, chính trị của tỉnh Bến Tre."
                };
                context.Cities.Add(benTreCity);
            }

            context.SaveChanges();

            // Tạo các tiện ích cho tour
            var amenities = context.Amenities.ToList();
            if (!amenities.Any())
            {
                amenities = new List<Amenity>
                {
                    new Amenity { Name = "Hướng dẫn viên", Description = "Hướng dẫn viên du lịch chuyên nghiệp", Icon = "bi bi-person-fill" },
                    new Amenity { Name = "Xe đưa đón", Description = "Xe đưa đón tận nơi", Icon = "bi bi-car-front-fill" },
                    new Amenity { Name = "Bữa ăn", Description = "Bao gồm các bữa ăn", Icon = "bi bi-cup-hot-fill" },
                    new Amenity { Name = "Vé tham quan", Description = "Bao gồm vé tham quan các điểm du lịch", Icon = "bi bi-ticket-perforated-fill" },
                    new Amenity { Name = "Bảo hiểm", Description = "Bảo hiểm du lịch", Icon = "bi bi-shield-fill-check" },
                    new Amenity { Name = "Wifi", Description = "Wifi miễn phí", Icon = "bi bi-wifi" }
                };

                context.Amenities.AddRange(amenities);
                context.SaveChanges();
            }

            // Tạo danh sách tour mẫu
            var tours = new List<Tour>
            {
                new Tour
                {
                    Name = "Tour khám phá miệt vườn Bến Tre 1 ngày",
                    Description = "Khám phá vẻ đẹp miệt vườn Bến Tre với tour 1 ngày đầy thú vị. Tham quan các làng nghề truyền thống, thưởng thức đặc sản địa phương và trải nghiệm cuộc sống sông nước miền Tây.",
                    Itinerary = "07:30 - 08:00: Đón khách tại TP.HCM\n" +
                               "10:00: Đến Bến Tre, tham quan làng nghề kẹo dừa\n" +
                               "11:30: Đi thuyền trên sông, tham quan vườn trái cây\n" +
                               "12:30: Ăn trưa tại nhà hàng địa phương\n" +
                               "14:00: Tham quan làng nghề đan lát\n" +
                               "15:30: Thưởng thức trà mật ong và trái cây theo mùa\n" +
                               "16:30: Khởi hành về TP.HCM\n" +
                               "18:30: Kết thúc tour",
                    Address = "Điểm đón: 123 Nguyễn Huệ, Quận 1, TP.HCM",
                    ImageUrl = "/images/tours/ben-tre-1-day.jpg",
                    Duration = 1,
                    Price = 850000,
                    MaxGroupSize = 20,
                    IsFeatured = true,
                    LocationId = benTreLocation.Id,
                    CityId = benTreCity.Id
                },
                new Tour
                {
                    Name = "Tour Bến Tre - Cồn Phụng 2 ngày 1 đêm",
                    Description = "Tour 2 ngày 1 đêm khám phá Bến Tre và Cồn Phụng, nơi có đạo dừa nổi tiếng. Trải nghiệm homestay miệt vườn, thưởng thức ẩm thực đặc sắc và tìm hiểu văn hóa địa phương.",
                    Itinerary = "Ngày 1:\n" +
                               "07:30: Đón khách tại TP.HCM\n" +
                               "10:00: Đến Bến Tre, tham quan làng nghề truyền thống\n" +
                               "12:00: Ăn trưa tại nhà hàng địa phương\n" +
                               "14:00: Đi thuyền tham quan Cồn Phụng, tìm hiểu về đạo dừa\n" +
                               "16:00: Nhận phòng homestay, nghỉ ngơi\n" +
                               "18:30: Ăn tối, giao lưu văn nghệ cùng người dân địa phương\n\n" +
                               "Ngày 2:\n" +
                               "07:00: Ăn sáng\n" +
                               "08:30: Tham quan vườn trái cây, hái trái cây theo mùa\n" +
                               "10:30: Tham gia lớp học nấu ăn món ăn đặc sản\n" +
                               "12:00: Ăn trưa\n" +
                               "14:00: Tham quan chợ nổi Cái Bè\n" +
                               "16:00: Khởi hành về TP.HCM\n" +
                               "18:30: Kết thúc tour",
                    Address = "Điểm đón: 123 Nguyễn Huệ, Quận 1, TP.HCM",
                    ImageUrl = "/images/tours/ben-tre-2-days.jpg",
                    Duration = 2,
                    Price = 1850000,
                    MaxGroupSize = 15,
                    IsFeatured = true,
                    LocationId = benTreLocation.Id,
                    CityId = benTreCity.Id
                },
                new Tour
                {
                    Name = "Tour khám phá ẩm thực Bến Tre 1 ngày",
                    Description = "Tour ẩm thực 1 ngày tại Bến Tre, thưởng thức các món ăn đặc sản như bánh tráng, kẹo dừa, rượu dừa và nhiều món ngon khác. Tham gia vào quá trình chế biến và tìm hiểu văn hóa ẩm thực địa phương.",
                    Itinerary = "07:30 - 08:00: Đón khách tại TP.HCM\n" +
                               "10:00: Đến Bến Tre, tham quan lò bánh tráng\n" +
                               "11:00: Tham quan cơ sở sản xuất kẹo dừa, thưởng thức kẹo dừa\n" +
                               "12:30: Ăn trưa với các món đặc sản địa phương\n" +
                               "14:00: Tham quan vườn dừa, thưởng thức các món chế biến từ dừa\n" +
                               "15:30: Học cách làm bánh xèo miền Tây\n" +
                               "16:30: Khởi hành về TP.HCM\n" +
                               "18:30: Kết thúc tour",
                    Address = "Điểm đón: 123 Nguyễn Huệ, Quận 1, TP.HCM",
                    ImageUrl = "/images/tours/ben-tre-food.jpg",
                    Duration = 1,
                    Price = 950000,
                    MaxGroupSize = 12,
                    IsFeatured = false,
                    LocationId = benTreLocation.Id,
                    CityId = benTreCity.Id
                },
                new Tour
                {
                    Name = "Tour sinh thái Bến Tre 3 ngày 2 đêm",
                    Description = "Tour sinh thái 3 ngày 2 đêm tại Bến Tre, khám phá hệ sinh thái đa dạng của vùng sông nước. Tham quan các khu bảo tồn, trải nghiệm cuộc sống của người dân địa phương và tham gia các hoạt động bảo vệ môi trường.",
                    Itinerary = "Ngày 1:\n" +
                               "07:30: Đón khách tại TP.HCM\n" +
                               "10:30: Đến Bến Tre, nhận phòng homestay\n" +
                               "12:00: Ăn trưa\n" +
                               "14:00: Tham quan khu bảo tồn sinh thái rừng ngập mặn\n" +
                               "16:00: Tham gia hoạt động trồng cây\n" +
                               "18:30: Ăn tối, giao lưu văn nghệ\n\n" +
                               "Ngày 2:\n" +
                               "07:00: Ăn sáng\n" +
                               "08:30: Đạp xe tham quan làng nghề địa phương\n" +
                               "12:00: Ăn trưa\n" +
                               "14:00: Chèo thuyền kayak trên sông\n" +
                               "16:00: Tham quan vườn trái cây\n" +
                               "18:30: Ăn tối, tham gia lớp học nấu ăn\n\n" +
                               "Ngày 3:\n" +
                               "07:00: Ăn sáng\n" +
                               "08:30: Tham quan chợ nổi\n" +
                               "11:00: Trả phòng\n" +
                               "12:00: Ăn trưa\n" +
                               "14:00: Tham quan làng nghề đan lát\n" +
                               "16:00: Khởi hành về TP.HCM\n" +
                               "18:30: Kết thúc tour",
                    Address = "Điểm đón: 123 Nguyễn Huệ, Quận 1, TP.HCM",
                    ImageUrl = "/images/tours/ben-tre-eco.jpg",
                    Duration = 3,
                    Price = 2850000,
                    MaxGroupSize = 10,
                    IsFeatured = true,
                    LocationId = benTreLocation.Id,
                    CityId = benTreCity.Id
                },
                new Tour
                {
                    Name = "Tour làng nghề truyền thống Bến Tre 1 ngày",
                    Description = "Tour 1 ngày khám phá các làng nghề truyền thống tại Bến Tre như làm kẹo dừa, đan lát, làm gốm và nhiều nghề thủ công khác. Tìm hiểu quy trình sản xuất và trực tiếp tham gia vào quá trình làm ra sản phẩm.",
                    Itinerary = "07:30 - 08:00: Đón khách tại TP.HCM\n" +
                               "10:00: Đến Bến Tre, tham quan làng nghề kẹo dừa\n" +
                               "11:30: Tham quan làng nghề đan lát\n" +
                               "12:30: Ăn trưa tại nhà hàng địa phương\n" +
                               "14:00: Tham quan làng gốm\n" +
                               "15:30: Tham quan cơ sở sản xuất rượu dừa\n" +
                               "16:30: Khởi hành về TP.HCM\n" +
                               "18:30: Kết thúc tour",
                    Address = "Điểm đón: 123 Nguyễn Huệ, Quận 1, TP.HCM",
                    ImageUrl = "/images/tours/ben-tre-craft.jpg",
                    Duration = 1,
                    Price = 900000,
                    MaxGroupSize = 15,
                    IsFeatured = false,
                    LocationId = benTreLocation.Id,
                    CityId = benTreCity.Id
                }
            };

            context.Tours.AddRange(tours);
            context.SaveChanges();

            // Thêm tiện ích cho các tour
            var tourAmenities = new List<TourAmenity>();
            foreach (var tour in tours)
            {
                // Mỗi tour sẽ có 3-5 tiện ích ngẫu nhiên
                var randomAmenities = amenities.OrderBy(a => Guid.NewGuid()).Take(new Random().Next(3, 6)).ToList();
                foreach (var amenity in randomAmenities)
                {
                    tourAmenities.Add(new TourAmenity
                    {
                        TourId = tour.Id,
                        AmenityId = amenity.Id
                    });
                }
            }

            context.TourAmenities.AddRange(tourAmenities);
            context.SaveChanges();

            // Thêm hình ảnh cho các tour
            var tourImages = new List<TourImage>();
            foreach (var tour in tours)
            {
                // Mỗi tour sẽ có 3-5 hình ảnh
                for (int i = 1; i <= new Random().Next(3, 6); i++)
                {
                    tourImages.Add(new TourImage
                    {
                        TourId = tour.Id,
                        ImageUrl = $"/images/tours/tour-{tour.Id}-image-{i}.jpg",
                        IsMain = i == 1, // Hình đầu tiên là hình chính
                        Caption = $"Hình ảnh {i} của tour {tour.Name}"
                    });
                }
            }

            context.TourImages.AddRange(tourImages);
            context.SaveChanges();
        }
    }
}
