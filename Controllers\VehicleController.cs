﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Security.Claims;
using ViVu.Extensions;
using ViVu.Models;
using ViVu.Models.ViewModels;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class VehicleController : Controller
    {
        private readonly IVehicleRepository _vehicleRepository;
        private readonly IVehicleBookingRepository _vehicleBookingRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public VehicleController(
            IVehicleRepository vehicleRepository,
            IVehicleBookingRepository vehicleBookingRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            UserManager<ApplicationUser> userManager,
            IHttpContextAccessor httpContextAccessor)
        {
            _vehicleRepository = vehicleRepository;
            _vehicleBookingRepository = vehicleBookingRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: Vehicle
        public async Task<IActionResult> Index(string searchTerm, int? locationId, int? cityId,
            DateTime? startDate, DateTime? endDate, decimal? minPrice, decimal? maxPrice, VehicleType? vehicleType)
        {
            ViewBag.SearchTerm = searchTerm;
            ViewBag.LocationId = locationId;
            ViewBag.CityId = cityId;
            ViewBag.StartDate = startDate ?? DateTime.Today.AddDays(1);
            ViewBag.EndDate = endDate ?? DateTime.Today.AddDays(3);
            ViewBag.MinPrice = minPrice;
            ViewBag.MaxPrice = maxPrice;
            ViewBag.VehicleType = vehicleType;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            ViewBag.VehicleTypes = Enum.GetValues(typeof(VehicleType))
                .Cast<VehicleType>()
                .Select(v => new SelectListItem
                {
                    Text = v.ToString(),
                    Value = ((int)v).ToString()
                });

            var vehicles = await _vehicleRepository.SearchAsync(
                searchTerm, locationId, cityId, startDate, endDate, minPrice, maxPrice, vehicleType);

            return View(vehicles);
        }

        // GET: Vehicle/Details/5
        public async Task<IActionResult> Details(int id, DateTime? startDate, DateTime? endDate)
        {
            var vehicle = await _vehicleRepository.GetByIdWithDetailsAsync(id);
            if (vehicle == null)
            {
                return NotFound();
            }

            ViewBag.StartDate = startDate ?? DateTime.Today.AddDays(1);
            ViewBag.EndDate = endDate ?? DateTime.Today.AddDays(3);

            // Kiểm tra xem người dùng đã đặt phương tiện này chưa
            if (User.Identity != null && User.Identity.IsAuthenticated)
            {
                var userId = _userManager.GetUserId(User);
                var userBookings = await _vehicleBookingRepository.GetByUserIdAsync(userId);

                ViewBag.HasBooked = userBookings.Any(b =>
                    b.VehicleBookingDetails.Any(d => d.VehicleId == id));

                // Kiểm tra xem người dùng đã đánh giá phương tiện này chưa
                ViewBag.HasReviewed = vehicle.Reviews != null &&
                                     vehicle.Reviews.Any(r => r.UserId == userId);

                ViewBag.CanReview = ViewBag.HasBooked && !ViewBag.HasReviewed;
            }

            return View(vehicle);
        }

        // POST: Vehicle/AddToCart
        [HttpPost]
        public async Task<IActionResult> AddToCart(int vehicleId, DateTime startDate, DateTime endDate, int quantity = 1)
        {
            var vehicle = await _vehicleRepository.GetByIdAsync(vehicleId);
            if (vehicle == null)
            {
                return NotFound();
            }

            // Kiểm tra xem phương tiện có sẵn trong khoảng thời gian này không
            var availableVehicles = await _vehicleRepository.SearchAsync(
                null, null, null, startDate, endDate, null, null, null);

            if (!availableVehicles.Any(v => v.Id == vehicleId))
            {
                TempData["Error"] = "Phương tiện này không có sẵn trong khoảng thời gian bạn chọn.";
                return RedirectToAction(nameof(Details), new { id = vehicleId, startDate, endDate });
            }

            // Lấy giỏ hàng từ session hoặc tạo mới
            var cart = GetVehicleCartFromSession() ?? new VehicleCart();

            // Thêm phương tiện vào giỏ hàng
            cart.AddItem(new VehicleCartItem
            {
                VehicleId = vehicle.Id,
                VehicleName = vehicle.Name,
                PricePerDay = vehicle.PricePerDay,
                StartDate = startDate,
                EndDate = endDate,
                Quantity = quantity,
                ImageUrl = vehicle.ImageUrl,
                VehicleType = vehicle.Type
            });

            // Lưu giỏ hàng vào session
            SaveVehicleCartToSession(cart);

            TempData["Success"] = "Đã thêm phương tiện vào giỏ hàng.";
            return RedirectToAction(nameof(Cart));
        }

        // GET: Vehicle/Cart
        public IActionResult Cart()
        {
            var cart = GetVehicleCartFromSession() ?? new VehicleCart();
            return View(cart);
        }

        // POST: Vehicle/RemoveFromCart
        [HttpPost]
        public IActionResult RemoveFromCart(int vehicleId)
        {
            var cart = GetVehicleCartFromSession();
            if (cart != null)
            {
                cart.RemoveItem(vehicleId);
                SaveVehicleCartToSession(cart);
            }

            return RedirectToAction(nameof(Cart));
        }

        // POST: Vehicle/ClearCart
        [HttpPost]
        public IActionResult ClearCart()
        {
            var cart = GetVehicleCartFromSession();
            if (cart != null)
            {
                cart.Clear();
                SaveVehicleCartToSession(cart);
            }

            return RedirectToAction(nameof(Cart));
        }

        // GET: Vehicle/Checkout
        [Authorize]
        public IActionResult Checkout()
        {
            var cart = GetVehicleCartFromSession();
            if (cart == null || cart.Items.Count == 0)
            {
                TempData["Error"] = "Giỏ hàng của bạn đang trống.";
                return RedirectToAction(nameof(Cart));
            }

            return View(cart);
        }

        // POST: Vehicle/Checkout
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Checkout(string specialRequests)
        {
            var cart = GetVehicleCartFromSession();
            if (cart == null || cart.Items.Count == 0)
            {
                TempData["Error"] = "Giỏ hàng của bạn đang trống.";
                return RedirectToAction(nameof(Cart));
            }

            // Tạo đơn đặt phương tiện mới
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var booking = new VehicleBooking
            {
                UserId = userId,
                BookingDate = DateTime.UtcNow,
                StartDate = cart.Items.Min(i => i.StartDate),
                EndDate = cart.Items.Max(i => i.EndDate),
                TotalPrice = cart.TotalPrice,
                SpecialRequests = specialRequests,
                Status = VehicleBookingStatus.Pending,
                VehicleBookingDetails = new List<VehicleBookingDetail>()
            };

            // Thêm chi tiết đặt phương tiện
            foreach (var item in cart.Items)
            {
                booking.VehicleBookingDetails.Add(new VehicleBookingDetail
                {
                    VehicleId = item.VehicleId,
                    StartDate = item.StartDate,
                    EndDate = item.EndDate,
                    PricePerDay = item.PricePerDay,
                    NumberOfDays = item.NumberOfDays,
                    TotalPrice = item.TotalPrice
                });
            }

            // Lưu đơn đặt phương tiện vào cơ sở dữ liệu
            await _vehicleBookingRepository.AddAsync(booking);

            // Xóa giỏ hàng
            cart.Clear();
            SaveVehicleCartToSession(cart);

            TempData["Success"] = "Đặt phương tiện thành công!";
            return RedirectToAction(nameof(BookingConfirmation), new { id = booking.Id });
        }

        // GET: Vehicle/BookingConfirmation/5
        [Authorize]
        public async Task<IActionResult> BookingConfirmation(int id)
        {
            var booking = await _vehicleBookingRepository.GetByIdWithDetailsAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            // Kiểm tra xem đơn đặt phương tiện có thuộc về người dùng hiện tại không
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (booking.UserId != userId)
            {
                return Forbid();
            }

            return View(booking);
        }

        // GET: Vehicle/MyBookings
        [Authorize]
        public async Task<IActionResult> MyBookings()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var bookings = await _vehicleBookingRepository.GetByUserIdAsync(userId);
            return View(bookings);
        }

        // Phương thức lấy giỏ hàng từ session
        private VehicleCart? GetVehicleCartFromSession()
        {
            return HttpContext.Session.GetObjectFromJson<VehicleCart>("VehicleCart");
        }

        // Phương thức lưu giỏ hàng vào session
        private void SaveVehicleCartToSession(VehicleCart cart)
        {
            HttpContext.Session.SetObjectAsJson("VehicleCart", cart);
        }
    }
}
