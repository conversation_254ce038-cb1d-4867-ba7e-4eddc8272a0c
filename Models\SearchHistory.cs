﻿﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class SearchHistory
    {
        public int Id { get; set; }

        public string? UserId { get; set; }

        [Required]
        public DateTime SearchDate { get; set; } = DateTime.UtcNow;

        public string? SearchTerm { get; set; }

        public int? LocationId { get; set; }

        public int? CityId { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public decimal? MinPrice { get; set; }

        public decimal? MaxPrice { get; set; }

        public int? StarRating { get; set; }

        public string SearchType { get; set; } = "Accommodation"; // Accommodation, Tour, Service, Vehicle

        // Thêm các trường để theo dõi sở thích
        public bool? RelatedToNature { get; set; }
        public bool? RelatedToHistory { get; set; }
        public bool? RelatedToFood { get; set; }
        public bool? RelatedToAdventure { get; set; }
        public bool? RelatedToRelaxation { get; set; }

        // Thêm các trường để theo dõi hoạt động cụ thể
        public bool? RelatedToCooking { get; set; }
        public bool? RelatedToCrafts { get; set; }
        public bool? RelatedToFarming { get; set; }
        public bool? RelatedToBoating { get; set; }
        public bool? RelatedToCycling { get; set; }
        public bool? RelatedToFishing { get; set; }

        // Thêm các trường để theo dõi đối tượng du lịch
        public bool? ForSoloTravelers { get; set; }
        public bool? ForCouples { get; set; }
        public bool? ForFriends { get; set; }
        public bool? ForFamiliesWithChildren { get; set; }
        public bool? ForElderlyTravelers { get; set; }

        // Thêm trường để theo dõi mức độ quan tâm (1-5)
        public int? InterestLevel { get; set; }

        // Thêm trường để theo dõi kết quả tìm kiếm có được click hay không
        public bool ResultClicked { get; set; } = false;

        // Thêm trường để theo dõi thời gian người dùng xem kết quả tìm kiếm (giây)
        public int? ViewDuration { get; set; }

        [ForeignKey("UserId")]
        public ApplicationUser? User { get; set; }

        [ForeignKey("LocationId")]
        public Location? Location { get; set; }

        [ForeignKey("CityId")]
        public City? City { get; set; }
    }
}
