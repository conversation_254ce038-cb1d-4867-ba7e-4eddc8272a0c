using Microsoft.AspNetCore.Mvc;
using ViVu.Models;
using ViVu.Repositories;
using System.Threading.Tasks;

namespace ViVu.Controllers
{
    public class StreetViewController : Controller
    {
        private readonly ILocationRepository _locationRepository;

        public StreetViewController(ILocationRepository locationRepository)
        {
            _locationRepository = locationRepository;
        }

        // GET: StreetView/View/5
        public async Task<IActionResult> View(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            // Nếu location không có tọa độ, sử dụng tọa độ mặc định của Bến Tre
            if (location.Latitude == 0 && location.Longitude == 0)
            {
                // Tọa độ mặc định của Bến Tre
                ViewBag.DefaultLatitude = 10.2433;
                ViewBag.DefaultLongitude = 106.3756;
            }

            return View(location);
        }

        // GET: StreetView/Embed/5
        public async Task<IActionResult> Embed(int id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            // Nếu location không có tọa độ, sử dụng tọa độ mặc định của Bến Tre
            if (location.Latitude == 0 && location.Longitude == 0)
            {
                // Tọa độ mặc định của Bến Tre
                ViewBag.DefaultLatitude = 10.2433;
                ViewBag.DefaultLongitude = 106.3756;
            }

            return View(location);
        }

        // GET: StreetView/ViewByCoordinates
        public IActionResult ViewByCoordinates(double lat, double lng, string name = "Địa điểm")
        {
            ViewBag.Latitude = lat;
            ViewBag.Longitude = lng;
            ViewBag.LocationName = name;

            return View();
        }
    }
}
