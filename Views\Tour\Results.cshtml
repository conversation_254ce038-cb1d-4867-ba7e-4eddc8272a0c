@model IEnumerable<ViVu.Models.Tour>

@{
    ViewData["Title"] = "Kết quả tìm kiếm tour";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb" class="modern-breadcrumb" data-aos="fade-up">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Tour" asp-action="Index">Tour du lịch</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Kết quả tìm kiếm</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm" data-aos="fade-up" data-aos-delay="100">
                <div class="card-body">
                    <h4 class="card-title mb-3">Bộ lọc tìm kiếm</h4>
                    <form asp-controller="Tour" asp-action="Search" method="post" class="modern-form">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Tất cả địa điểm --</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Ngày bắt đầu</label>
                                <input type="date" name="startDate" class="form-control" value="@(ViewBag.StartDate?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(1).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Số người</label>
                                <select name="groupSize" class="form-select">
                                    <option value="">-- Tất cả --</option>
                                    @if (ViewBag.GroupSize == 1)
                                    {
                                        <option value="1" selected>1 người</option>
                                    }
                                    else
                                    {
                                        <option value="1">1 người</option>
                                    }

                                    @if (ViewBag.GroupSize == 2)
                                    {
                                        <option value="2" selected>2 người</option>
                                    }
                                    else
                                    {
                                        <option value="2">2 người</option>
                                    }

                                    @if (ViewBag.GroupSize == 3)
                                    {
                                        <option value="3" selected>3 người</option>
                                    }
                                    else
                                    {
                                        <option value="3">3 người</option>
                                    }

                                    @if (ViewBag.GroupSize == 4)
                                    {
                                        <option value="4" selected>4 người</option>
                                    }
                                    else
                                    {
                                        <option value="4">4 người</option>
                                    }

                                    @if (ViewBag.GroupSize == 5)
                                    {
                                        <option value="5" selected>5+ người</option>
                                    }
                                    else
                                    {
                                        <option value="5">5+ người</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                    <i class="bi bi-search me-1"></i> Lọc kết quả
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12" data-aos="fade-up" data-aos-delay="150">
            <div class="section-title">
                <h2>Kết quả tìm kiếm tour</h2>
                <p>Tìm thấy @Model.Count() tour phù hợp với tiêu chí của bạn</p>
            </div>
        </div>
    </div>

    <div class="row">
        @if (Model.Any())
        {
            int index = 0;
            foreach (var tour in Model)
            {
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="@(index * 100)">
                    <div class="modern-card h-100 tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/default/default-tour.jpg" : tour.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@tour.Name">
                            <div class="position-absolute top-0 end-0 m-2">
                                @if (tour.IsFeatured)
                                {
                                    <span class="badge badge-modern badge-modern-warning">Tour nổi bật</span>
                                }
                                else
                                {
                                    <span class="badge badge-modern badge-modern-primary">Tour mới</span>
                                }
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@tour.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @tour.Location?.Name, @tour.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @tour.Duration ngày
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @tour.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(tour.Description?.Length > 100 ? tour.Description.Substring(0, 100) + "..." : tour.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@tour.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-modern btn-modern-outline">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
                index++;
            }
        }
        else
        {
            <div class="col-12 text-center py-5" data-aos="fade-up">
                <div class="alert alert-info">
                    <h4 class="alert-heading">Không tìm thấy tour nào!</h4>
                    <p>Không có tour nào phù hợp với tiêu chí tìm kiếm của bạn. Vui lòng thử lại với các tiêu chí khác.</p>
                </div>
                <a asp-controller="Tour" asp-action="Index" class="btn btn-modern btn-modern-primary mt-3">Xem tất cả tour</a>
            </div>
        }
    </div>
</div>
