using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;
using ViVu.Services;
using ViVu.Data;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllersWithViews();

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddIdentity<ApplicationUser, IdentityRole>()
    .AddDefaultTokenProviders()
    .AddDefaultUI()
    .AddEntityFrameworkStores<ApplicationDbContext>();

builder.Services.AddRazorPages();

builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = $"/Identity/Account/Login";
    options.LogoutPath = $"/Identity/Account/Logout";
    options.AccessDeniedPath = $"/Identity/Account/AccessDenied";
});

builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add services to the container.

// Các repository cũ (sẽ được thay thế sau khi migration)
builder.Services.AddScoped<IProductRepository, EFProductRepository>();
builder.Services.AddScoped<ICategoryRepository, EFCategoryRepository>();

// Các repository mới cho trang web du lịch
builder.Services.AddScoped<IAccommodationRepository, EFAccommodationRepository>();
builder.Services.AddScoped<IRoomRepository, EFRoomRepository>();
builder.Services.AddScoped<ILocationRepository, EFLocationRepository>();
builder.Services.AddScoped<ICityRepository, EFCityRepository>();
builder.Services.AddScoped<IBookingRepository, EFBookingRepository>();
builder.Services.AddScoped<ITourRepository, EFTourRepository>();
builder.Services.AddScoped<ITourBookingRepository, EFTourBookingRepository>();
builder.Services.AddScoped<IServiceRepository, EFServiceRepository>();
builder.Services.AddScoped<IServiceBookingRepository, EFServiceBookingRepository>();
builder.Services.AddScoped<IVehicleRepository, EFVehicleRepository>();
builder.Services.AddScoped<IVehicleBookingRepository, EFVehicleBookingRepository>();
builder.Services.AddScoped<IPanorama360Repository, EFPanorama360Repository>();

// AI Services
builder.Services.AddScoped<OpenAIService>();
builder.Services.AddScoped<OllamaService>();
builder.Services.AddScoped<IRecommendationService, RecommendationService>();

Console.WriteLine("=== TESTING LOG OUTPUT ===");
Console.WriteLine("OllamaService and OpenAIService registered");

var app = builder.Build();

using (var scope = app.Services.CreateScope())
{
    // Create roles
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
    var roles = new[] { "Admin", "User" }; // Add your desired roles

    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new IdentityRole(role));
        }
    }

    // Tạo tài khoản Admin nếu chưa tồn tại
    var adminEmail = "<EMAIL>";
    var adminUser = await userManager.FindByEmailAsync(adminEmail);
    if (adminUser == null)
    {
        adminUser = new ApplicationUser
        {
            UserName = adminEmail,
            Email = adminEmail,
            EmailConfirmed = true,
            FullName = "Admin Bến Tre",
            Address = "Bến Tre, Việt Nam"
        };

        var result = await userManager.CreateAsync(adminUser, "Admin@123");
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
            Console.WriteLine("Admin user created successfully");
        }
        else
        {
            Console.WriteLine("Failed to create admin user:");
            foreach (var error in result.Errors)
            {
                Console.WriteLine($"- {error.Description}");
            }
        }
    }

    // Tạo tài khoản User nếu chưa tồn tại
    var userEmail = "<EMAIL>";
    var normalUser = await userManager.FindByEmailAsync(userEmail);
    if (normalUser == null)
    {
        normalUser = new ApplicationUser
        {
            UserName = userEmail,
            Email = userEmail,
            EmailConfirmed = true,
            FullName = "Khách Du Lịch",
            Address = "Hồ Chí Minh, Việt Nam"
        };

        var result = await userManager.CreateAsync(normalUser, "User@123");
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(normalUser, "User");
            Console.WriteLine("Normal user created successfully");
        }
        else
        {
            Console.WriteLine("Failed to create normal user:");
            foreach (var error in result.Errors)
            {
                Console.WriteLine($"- {error.Description}");
            }
        }
    }

    // Khởi tạo dữ liệu mẫu
    await ViVu.Data.DbInitializer.Initialize(scope.ServiceProvider);
}
// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
app.UseSession();
app.UseRouting();
app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseAuthentication();
app.UseAuthorization();

app.MapRazorPages();

app.MapControllerRoute(
    name: "areas",
    pattern: "{area:exists}/{controller=Admin}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "admin",
    pattern: "Admin",
    defaults: new { area = "Admin", controller = "Admin", action = "Index" });

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
