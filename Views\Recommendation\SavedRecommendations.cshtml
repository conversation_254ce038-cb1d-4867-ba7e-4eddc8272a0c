@model List<ViVu.Models.TravelRecommendation>

@{
    ViewData["Title"] = "Lịch Trình Đã Lưu";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Recommendation" asp-action="Index">AI Gợi Ý Lịch Trình</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Lịch Trình Đã Lưu</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary text-white p-4 rounded-3">
                <h1 class="display-5 fw-bold">Lịch Trình Du Lịch Đã Lưu</h1>
                <p class="fs-5">Quản lý các gợi ý lịch trình du lịch bạn đã lưu</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (Model != null && Model.Any())
            {
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    @foreach (var recommendation in Model)
                    {
                        <div class="col">
                            <div class="card h-100 shadow-sm">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">@recommendation.Title</h5>
                                    <span class="badge bg-primary">@recommendation.RecommendationType</span>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">@recommendation.Description</p>
                                    <div class="d-flex flex-wrap mb-3">
                                        @if (recommendation.IncludesNature)
                                        {
                                            <span class="badge bg-success me-2 mb-2">Thiên nhiên</span>
                                        }
                                        @if (recommendation.IncludesHistory)
                                        {
                                            <span class="badge bg-info me-2 mb-2">Lịch sử</span>
                                        }
                                        @if (recommendation.IncludesFood)
                                        {
                                            <span class="badge bg-warning me-2 mb-2">Ẩm thực</span>
                                        }
                                        @if (recommendation.IncludesAdventure)
                                        {
                                            <span class="badge bg-danger me-2 mb-2">Mạo hiểm</span>
                                        }
                                        @if (recommendation.IncludesRelaxation)
                                        {
                                            <span class="badge bg-secondary me-2 mb-2">Thư giãn</span>
                                        }
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt me-1"></i> @recommendation.RecommendedDuration ngày
                                            </small>
                                        </div>
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-money-bill-wave me-1"></i>
                                                @(recommendation.EstimatedMinBudget?.ToString("N0") ?? "0") -
                                                @(recommendation.EstimatedMaxBudget?.ToString("N0") ?? "0") VNĐ
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-white border-top-0">
                                    <div class="d-flex justify-content-between">
                                        <a asp-action="Details" asp-route-id="@recommendation.Id" class="btn btn-outline-primary">
                                            <i class="fas fa-info-circle me-2"></i>Xem chi tiết
                                        </a>
                                        <form asp-action="RemoveSavedRecommendation" asp-route-id="@recommendation.Id" method="post"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa lịch trình này khỏi danh sách đã lưu?');">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="fas fa-trash-alt me-2"></i>Xóa
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <h4 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Chưa có lịch trình nào được lưu!</h4>
                    <p>Bạn chưa lưu lịch trình du lịch nào. Hãy sử dụng tính năng AI Gợi Ý Lịch Trình để nhận các gợi ý phù hợp với sở thích của bạn.</p>
                    <hr>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                        <a asp-action="Index" class="btn btn-primary">
                            <i class="fas fa-robot me-2"></i>Nhận gợi ý lịch trình
                        </a>
                        <a asp-action="SimpleForm" class="btn btn-outline-primary">
                            <i class="fas fa-sliders-h me-2"></i>Sử dụng form đơn giản
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
