@model IEnumerable<ViVu.Models.Tour>

@{
    ViewData["Title"] = "Hoạt động du lịch";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Hoạt động du lịch</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="position-relative rounded overflow-hidden">
                <img src="/images/banners/banner_activity.jpg" class="img-fluid w-100" style="height: 350px; object-fit: cover;" alt="Hoạt động du lịch">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);">
                    <div class="container-fluid px-4 px-md-5">
                        <div class="row">
                            <div class="col-md-7">
                                <h1 class="text-white mb-3 text-shadow-strong">Hoạt động du lịch Bến Tre</h1>
                                <p class="text-white mb-3 fs-5">Khám phá các hoạt động thú vị và trải nghiệm văn hóa độc đáo tại Bến Tre</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h4 class="card-title mb-3">Tìm kiếm hoạt động</h4>
                    <form asp-controller="Activity" asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-5">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn địa điểm --</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <label class="form-label">Ngày tham gia</label>
                                <input type="date" name="activityDate" class="form-control" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities List -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Hoạt động nổi bật</h2>
        </div>
    </div>

    <div class="row">
        @if (Model.Any())
        {
            foreach (var activity in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(activity.ImageUrl) ? "/images/default/default-activity.jpg" : activity.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@activity.Name">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-info">Hoạt động</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@activity.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @activity.Location?.Name, @activity.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @activity.Duration giờ
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @activity.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(activity.Description?.Length > 100 ? activity.Description.Substring(0, 100) + "..." : activity.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@activity.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@activity.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-5">
                <div class="alert alert-info">
                    <h4 class="alert-heading">Chưa có hoạt động nào!</h4>
                    <p>Hiện tại chưa có hoạt động nào được đăng ký. Vui lòng quay lại sau.</p>
                </div>
            </div>
        }
    </div>

    <!-- Activity Categories -->
    <div class="row mt-5 mb-4">
        <div class="col-12">
            <h2 class="mb-3">Khám phá theo loại hoạt động</h2>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-3 mb-4">
            <div class="card h-100 text-center p-4 hover-lift">
                <div class="mb-3">
                    <i class="bi bi-water text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5>Du lịch sông nước</h5>
                <p>Trải nghiệm đi thuyền trên sông, khám phá các cồn, lách và các làng nghề truyền thống.</p>
                <a href="#" class="btn btn-outline-primary mt-auto">Xem thêm</a>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card h-100 text-center p-4 hover-lift">
                <div class="mb-3">
                    <i class="bi bi-tree text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5>Khám phá vườn trái cây</h5>
                <p>Tham quan các vườn trái cây nhiệt đới, thưởng thức trái cây tươi ngon.</p>
                <a href="#" class="btn btn-outline-primary mt-auto">Xem thêm</a>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card h-100 text-center p-4 hover-lift">
                <div class="mb-3">
                    <i class="bi bi-cup-hot text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5>Trải nghiệm ẩm thực</h5>
                <p>Thưởng thức các món ăn đặc sản của Bến Tre và học cách chế biến món ăn địa phương.</p>
                <a href="#" class="btn btn-outline-primary mt-auto">Xem thêm</a>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card h-100 text-center p-4 hover-lift">
                <div class="mb-3">
                    <i class="bi bi-bicycle text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5>Đạp xe khám phá</h5>
                <p>Đạp xe dọc theo những con đường làng yên bình, khám phá cuộc sống địa phương.</p>
                <a href="#" class="btn btn-outline-primary mt-auto">Xem thêm</a>
            </div>
        </div>
    </div>
</div>
