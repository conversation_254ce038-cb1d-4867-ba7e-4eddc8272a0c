@model ViVu.Models.TourCart

@{
    ViewData["Title"] = "Giỏ đặt tour";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    @if (Model.Items.Count == 0)
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Giỏ đặt tour của bạn đang trống.
        </div>
        
        <div class="text-center mt-4">
            <a asp-controller="Tour" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Tìm tour ngay
            </a>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-body p-4">
                        <h5 class="mb-3">Tour đã chọn (@Model.Items.Count)</h5>
                        
                        @foreach (var item in Model.Items)
                        {
                            <div class="card mb-3 border-0 shadow-sm">
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <img src="@(string.IsNullOrEmpty(item.ImageUrl) ? "/images/no-image.jpg" : item.ImageUrl)" 
                                             class="img-fluid rounded-start" alt="@item.TourName" style="height: 100%; object-fit: cover;">
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h5 class="card-title">@item.TourName</h5>
                                                <form asp-action="RemoveFromCart" method="post">
                                                    <input type="hidden" name="tourId" value="@item.TourId" />
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                            
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-calendar-alt me-1"></i>Ngày tour: @item.TourDate.ToString("dd/MM/yyyy")
                                                    </p>
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-clock me-1"></i>Thời gian: @item.Duration ngày
                                                    </p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-user me-1"></i>Người lớn: @item.NumberOfAdults
                                                    </p>
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-child me-1"></i>Trẻ em: @item.NumberOfChildren
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <div>
                                                    <span class="text-muted">Đơn giá: @item.Price.ToString("N0") VNĐ</span>
                                                </div>
                                                <div>
                                                    <span class="fw-bold text-primary">Thành tiền: @item.TotalPrice.ToString("N0") VNĐ</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tổng đơn hàng</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between mb-3">
                            <span>Tổng tiền:</span>
                            <span class="fw-bold">@Model.TotalPrice.ToString("N0") VNĐ</span>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between mb-3">
                            <span>Thành tiền:</span>
                            <span class="fw-bold text-primary fs-5">@Model.TotalPrice.ToString("N0") VNĐ</span>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <a asp-controller="Tour" asp-action="Checkout" class="btn btn-primary">
                                <i class="fas fa-credit-card me-2"></i>Tiến hành đặt tour
                            </a>
                            <a asp-controller="Tour" asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-search me-2"></i>Tiếp tục tìm tour
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
