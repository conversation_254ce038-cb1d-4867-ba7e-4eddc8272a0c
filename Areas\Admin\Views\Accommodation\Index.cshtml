@model IEnumerable<ViVu.Models.Accommodation>
@{
    ViewData["Title"] = "Quản lý chỗ ở";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý chỗ ở</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Chỗ ở</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-hotel me-1"></i> Danh sách chỗ ở</div>
                <a asp-action="Create" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> Thêm mới
                </a>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>Tên</th>
                        <th>Địa chỉ</th>
                        <th>Địa điểm</th>
                        <th>Thành phố</th>
                        <th>Đánh giá</th>
                        <th>Giá từ</th>
                        <th>Nổi bật</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <img src="@item.ImageUrl" alt="@item.Name" style="width: 80px; height: 60px; object-fit: cover;" />
                            </td>
                            <td>@item.Name</td>
                            <td>@item.Address</td>
                            <td>@item.Location?.Name</td>
                            <td>@item.City?.Name</td>
                            <td>
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= item.StarRating)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-warning"></i>
                                    }
                                }
                            </td>
                            <td>@(item.MinPrice > 0 ? item.MinPrice.ToString("N0") + " VNĐ" : "Chưa có phòng")</td>
                            <td>
                                @if (item.IsFeatured)
                                {
                                    <span class="badge bg-success">Có</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Không</span>
                                }
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-warning btn-sm me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
