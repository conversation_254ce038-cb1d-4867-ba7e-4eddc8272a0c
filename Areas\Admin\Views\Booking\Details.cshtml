@model ViVu.Models.Booking
@{
    ViewData["Title"] = "Chi tiết đặt phòng";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết đặt phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Đặt phòng</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>

    <div class="row">
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-user me-1"></i>
                    Thông tin khách hàng
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Họ tên:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.FullName</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.Email</dd>

                        <dt class="col-sm-4">Địa chỉ:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.Address</dd>

                        <dt class="col-sm-4">Điện thoại:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.PhoneNumber</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Thông tin đặt phòng
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Mã đơn:</dt>
                                <dd class="col-sm-7">#@Model.Id</dd>

                                <dt class="col-sm-5">Ngày đặt:</dt>
                                <dd class="col-sm-7">@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-5">Ngày nhận phòng:</dt>
                                <dd class="col-sm-7">@Model.CheckInDate.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-5">Ngày trả phòng:</dt>
                                <dd class="col-sm-7">@Model.CheckOutDate.ToString("dd/MM/yyyy")</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-5">Tổng tiền:</dt>
                                <dd class="col-sm-7">@Model.TotalPrice.ToString("N0") VNĐ</dd>

                                <dt class="col-sm-5">Trạng thái:</dt>
                                <dd class="col-sm-7">
                                    @if (Model.Status == BookingStatus.Pending)
                                    {
                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                    }
                                    else if (Model.Status == BookingStatus.Confirmed)
                                    {
                                        <span class="badge bg-primary">Đã xác nhận</span>
                                    }
                                    else if (Model.Status == BookingStatus.Completed)
                                    {
                                        <span class="badge bg-success">Hoàn thành</span>
                                    }
                                    else if (Model.Status == BookingStatus.Cancelled)
                                    {
                                        <span class="badge bg-danger">Đã hủy</span>
                                    }
                                </dd>

                                <dt class="col-sm-5">Ghi chú:</dt>
                                <dd class="col-sm-7">@(string.IsNullOrEmpty(Model.SpecialRequests) ? "Không có" : Model.SpecialRequests)</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-3">
                        <form asp-action="UpdateStatus" method="post" class="d-flex align-items-center">
                            <input type="hidden" name="id" value="@Model.Id" />
                            <select name="status" class="form-select me-2">
                                <option value="0" selected="@(Model.Status == BookingStatus.Pending)">Chờ xác nhận</option>
                                <option value="1" selected="@(Model.Status == BookingStatus.Confirmed)">Đã xác nhận</option>
                                <option value="2" selected="@(Model.Status == BookingStatus.Completed)">Hoàn thành</option>
                                <option value="3" selected="@(Model.Status == BookingStatus.Cancelled)">Đã hủy</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Cập nhật trạng thái</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-list me-1"></i>
            Chi tiết phòng đã đặt
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Phòng</th>
                            <th>Chỗ ở</th>
                            <th>Giá/đêm</th>
                            <th>Số đêm</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.BookingDetails)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@detail.Room.ImageUrl" alt="@detail.Room.Name" style="width: 60px; height: 45px; object-fit: cover;" class="me-2" />
                                        <div>
                                            <div>@detail.Room.Name</div>
                                            <small class="text-muted">Sức chứa: @detail.Room.MaxOccupancy người</small>
                                        </div>
                                    </div>
                                </td>
                                <td>@detail.Room.Accommodation?.Name</td>
                                <td>@detail.PricePerNight.ToString("N0") VNĐ</td>
                                <td>@detail.NumberOfNights</td>
                                <td>@detail.TotalPrice.ToString("N0") VNĐ</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">Tổng cộng:</th>
                            <th>@Model.TotalPrice.ToString("N0") VNĐ</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="mb-4">
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </a>
        @if (Model.Status == BookingStatus.Cancelled || Model.Status == BookingStatus.Completed)
        {
            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                <i class="fas fa-trash"></i> Xóa đơn đặt phòng
            </a>
        }
    </div>
</div>
