/**
 * Recommendation Results Page JavaScript
 * Handles the interactive elements and animations for the recommendation results page
 */

$(document).ready(function() {
    // Initialize tabs
    initTabs();
    
    // Initialize animations
    initAnimations();
    
    // Initialize interactive elements
    initInteractiveElements();
    
    // Initialize sticky navigation
    initStickyNavigation();
});

/**
 * Initialize the tab navigation system
 */
function initTabs() {
    // Show the first tab by default
    $('.recommendation-tab-content').hide();
    $('.recommendation-tab-content:first').show();
    $('.recommendation-tab:first').addClass('active');
    
    // Handle tab clicks
    $('.recommendation-tab').on('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        $('.recommendation-tab').removeClass('active');
        
        // Add active class to clicked tab
        $(this).addClass('active');
        
        // Hide all tab content
        $('.recommendation-tab-content').hide();
        
        // Show the selected tab content
        var target = $(this).data('target');
        $(target).fadeIn(300);
        
        // Update URL hash
        window.location.hash = target;
    });
    
    // Check for hash in URL
    if (window.location.hash) {
        var hash = window.location.hash;
        $('.recommendation-tab[data-target="' + hash + '"]').click();
    }
}

/**
 * Initialize animations for the recommendation cards
 */
function initAnimations() {
    // Animate recommendation cards on page load
    $('.recommendation-card').each(function(index) {
        $(this).css({
            'opacity': 0,
            'transform': 'translateY(20px)'
        });
        
        setTimeout(function(card) {
            card.css({
                'opacity': 1,
                'transform': 'translateY(0)',
                'transition': 'all 0.5s ease'
            });
        }, index * 100, $(this));
    });
    
    // Animate sections when they come into view
    $(window).on('scroll', function() {
        $('.animate-on-scroll').each(function() {
            if (isElementInViewport(this) && !$(this).hasClass('animated')) {
                $(this).addClass('animated');
                $(this).css({
                    'opacity': 1,
                    'transform': 'translateY(0)',
                    'transition': 'all 0.5s ease'
                });
            }
        });
    });
}

/**
 * Initialize interactive elements
 */
function initInteractiveElements() {
    // Add hover effects to recommendation cards
    $('.recommendation-card').hover(
        function() {
            $(this).addClass('recommendation-card-hover');
        },
        function() {
            $(this).removeClass('recommendation-card-hover');
        }
    );
    
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Handle "Show More" buttons
    $('.show-more-btn').on('click', function() {
        var target = $(this).data('target');
        $(target).slideToggle(300);
        
        if ($(this).text() === 'Xem thêm') {
            $(this).text('Thu gọn');
        } else {
            $(this).text('Xem thêm');
        }
    });
}

/**
 * Initialize sticky navigation
 */
function initStickyNavigation() {
    // Make the tab navigation sticky when scrolling
    var tabNav = $('.recommendation-tabs');
    var tabNavTop = tabNav.offset().top;
    
    $(window).on('scroll', function() {
        if ($(window).scrollTop() > tabNavTop) {
            tabNav.addClass('sticky-tabs');
            $('.recommendation-content').css('padding-top', tabNav.outerHeight() + 'px');
        } else {
            tabNav.removeClass('sticky-tabs');
            $('.recommendation-content').css('padding-top', 0);
        }
    });
    
    // Smooth scroll to sections
    $('.recommendation-nav-item').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        $('html, body').animate({
            scrollTop: $(target).offset().top - tabNav.outerHeight() - 20
        }, 500);
    });
}

/**
 * Check if an element is in the viewport
 */
function isElementInViewport(el) {
    var rect = el.getBoundingClientRect();
    
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}
