﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFServiceBookingRepository : IServiceBookingRepository
    {
        private readonly ApplicationDbContext _context;

        public EFServiceBookingRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ServiceBooking>> GetAllAsync()
        {
            return await _context.ServiceBookings
                .Include(sb => sb.ApplicationUser)
                .Include(sb => sb.ServiceBookingDetails)
                    .ThenInclude(sbd => sbd.Service)
                .OrderByDescending(sb => sb.BookingDate)
                .ToListAsync();
        }

        public async Task<ServiceBooking> GetByIdAsync(int id)
        {
            return await _context.ServiceBookings
                .Include(sb => sb.ApplicationUser)
                .FirstOrDefaultAsync(sb => sb.Id == id);
        }

        public async Task<IEnumerable<ServiceBooking>> GetByUserIdAsync(string userId)
        {
            return await _context.ServiceBookings
                .Include(sb => sb.ServiceBookingDetails)
                    .ThenInclude(sbd => sbd.Service)
                .Where(sb => sb.UserId == userId)
                .OrderByDescending(sb => sb.BookingDate)
                .ToListAsync();
        }

        public async Task<ServiceBooking> GetByIdWithDetailsAsync(int id)
        {
            return await _context.ServiceBookings
                .Include(sb => sb.ApplicationUser)
                .Include(sb => sb.ServiceBookingDetails)
                    .ThenInclude(sbd => sbd.Service)
                        .ThenInclude(s => s.Location)
                .Include(sb => sb.ServiceBookingDetails)
                    .ThenInclude(sbd => sbd.Service)
                        .ThenInclude(s => s.City)
                .FirstOrDefaultAsync(sb => sb.Id == id);
        }

        public async Task AddAsync(ServiceBooking serviceBooking)
        {
            await _context.ServiceBookings.AddAsync(serviceBooking);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(ServiceBooking serviceBooking)
        {
            _context.ServiceBookings.Update(serviceBooking);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var serviceBooking = await _context.ServiceBookings.FindAsync(id);
            if (serviceBooking != null)
            {
                _context.ServiceBookings.Remove(serviceBooking);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<ServiceBooking>> GetRecentBookingsAsync(int count = 5)
        {
            return await _context.ServiceBookings
                .Include(sb => sb.ApplicationUser)
                .OrderByDescending(sb => sb.BookingDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<int> GetTotalBookingsAsync()
        {
            return await _context.ServiceBookings.CountAsync();
        }
    }
}
