﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class VehicleBookingController : Controller
    {
        private readonly IVehicleBookingRepository _vehicleBookingRepository;
        private readonly ApplicationDbContext _context;

        public VehicleBookingController(
            IVehicleBookingRepository vehicleBookingRepository,
            ApplicationDbContext context)
        {
            _vehicleBookingRepository = vehicleBookingRepository;
            _context = context;
        }

        // GET: Admin/VehicleBooking
        public async Task<IActionResult> Index(string status = null)
        {
            IQueryable<VehicleBooking> bookingsQuery = _context.VehicleBookings
                .Include(b => b.ApplicationUser)
                .OrderByDescending(b => b.BookingDate);

            // Lọc theo trạng thái nếu có
            if (!string.IsNullOrEmpty(status) && Enum.TryParse<VehicleBookingStatus>(status, out var bookingStatus))
            {
                bookingsQuery = bookingsQuery.Where(b => b.Status == bookingStatus);
            }

            var bookings = await bookingsQuery.ToListAsync();

            // Truyền danh sách trạng thái để hiển thị bộ lọc
            ViewBag.Statuses = Enum.GetValues(typeof(VehicleBookingStatus))
                .Cast<VehicleBookingStatus>()
                .Select(s => new
                {
                    Value = s.ToString(),
                    Text = s.ToString()
                }).ToList();

            ViewBag.SelectedStatus = status;

            return View(bookings);
        }

        // GET: Admin/VehicleBooking/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var booking = await _vehicleBookingRepository.GetByIdWithDetailsAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            return View(booking);
        }

        // POST: Admin/VehicleBooking/UpdateStatus/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, VehicleBookingStatus status)
        {
            var booking = await _vehicleBookingRepository.GetByIdAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            booking.Status = status;
            await _vehicleBookingRepository.UpdateAsync(booking);

            return RedirectToAction(nameof(Details), new { id });
        }

        // POST: Admin/VehicleBooking/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _vehicleBookingRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }
    }
}
