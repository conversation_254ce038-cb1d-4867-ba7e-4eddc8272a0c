﻿﻿﻿using Microsoft.AspNetCore.Mvc.Rendering;

namespace ViVu.Models
{
    public class RecommendationViewModel
    {
        // User preferences for the recommendation form
        public UserPreference UserPreference { get; set; }

        // For the form dropdowns
        public SelectList Locations { get; set; }
        public SelectList Cities { get; set; }

        // For displaying recommendations
        public List<TravelRecommendation> Recommendations { get; set; } = new List<TravelRecommendation>();

        // For displaying similar recommendations
        public List<TravelRecommendation> SimilarRecommendations { get; set; } = new List<TravelRecommendation>();

        // For displaying related items
        public Dictionary<int, Tour> RelatedTours { get; set; } = new Dictionary<int, Tour>();
        public Dictionary<int, Accommodation> RelatedAccommodations { get; set; } = new Dictionary<int, Accommodation>();
        public Dictionary<int, Service> RelatedServices { get; set; } = new Dictionary<int, Service>();
        public Dictionary<int, Location> RelatedLocations { get; set; } = new Dictionary<int, Location>();
        public Dictionary<int, Vehicle> RelatedVehicles { get; set; } = new Dictionary<int, Vehicle>();

        // For displaying user search history
        public List<SearchHistory> RecentSearches { get; set; } = new List<SearchHistory>();

        // For displaying popular locations
        public List<Location> PopularLocations { get; set; } = new List<Location>();

        // For displaying recommendation statistics
        public int TotalRecommendations { get; set; } = 0;
        public int SavedRecommendations { get; set; } = 0;
        public int ViewedRecommendations { get; set; } = 0;
    }
}
