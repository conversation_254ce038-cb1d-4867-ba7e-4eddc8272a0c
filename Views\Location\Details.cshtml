@model ViVu.Models.Location
@using System.Linq

@{
    ViewData["Title"] = Model.Name;
}

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="mb-4">
                <h1 class="mb-2">@Model.Name</h1>
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                        <span>@(Model.City != null ? Model.City.Name : "")</span>
                    </div>
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div>
                            <span class="text-warning">
                                @Model.AverageRating.ToString("0.0")
                                <i class="fas fa-star"></i>
                            </span>
                            <span class="text-muted">(@Model.Reviews.Count đánh giá)</span>
                        </div>
                    }
                </div>
            </div>

            <div class="mb-4">
                <div id="locationCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-indicators">
                        <button type="button" data-bs-target="#locationCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                        <button type="button" data-bs-target="#locationCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                        <button type="button" data-bs-target="#locationCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                    </div>
                    <div class="carousel-inner rounded">
                        <div class="carousel-item active">
                            <img src="@(string.IsNullOrEmpty(Model.ImageUrl) ? "/images/destinations/default.jpg" : Model.ImageUrl)"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Name">
                        </div>
                        <div class="carousel-item">
                            <img src="/images/destinations/details/location_detail_01.jpg"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Name">
                        </div>
                        <div class="carousel-item">
                            <img src="/images/destinations/details/location_detail_02.jpg"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Name">
                        </div>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#locationCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#locationCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Thông tin địa điểm</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                <strong>Thành phố:</strong>
                                <span>@(Model.City != null ? Model.City.Name : "")</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="fas fa-flag me-2 text-primary"></i>
                                <strong>Nổi bật:</strong>
                                <span>@(Model.IsFeatured ? "Có" : "Không")</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">Mô tả</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Description.Replace("\n", "<br>"))
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">Du lịch 360°</h5>
                        <div class="p-3 bg-light rounded">
                            <p>Trải nghiệm du lịch ảo 360° tại @Model.Name để khám phá trước khi đến thăm thực tế.</p>
                            <a href="@Url.Action("LocationView", "Panorama360", new { id = Model.Id })" class="btn btn-primary">
                                <i class="fas fa-vr-cardboard me-2"></i>Xem du lịch 360°
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Đánh giá (@(Model.Reviews != null ? Model.Reviews.Count : 0))</h5>
                </div>
                <div class="card-body">
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <span class="display-4 fw-bold text-warning">@Model.AverageRating.ToString("0.0")</span>
                                </div>
                                <div>
                                    <div class="text-warning mb-1">
                                        @{
                                            var avgRating = Model.AverageRating;
                                            for (int i = 1; i <= 5; i++)
                                            {
                                                if (i <= Math.Floor(avgRating))
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                else if (i - avgRating < 1 && i - avgRating > 0)
                                                {
                                                    <i class="fas fa-star-half-alt"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star"></i>
                                                }
                                            }
                                        }
                                    </div>
                                    <div class="text-muted">Dựa trên @Model.Reviews.Count đánh giá</div>
                                </div>
                            </div>
                        </div>

                        @foreach (var review in Model.Reviews?.OrderByDescending(r => r.CreatedAt) ?? Enumerable.Empty<ViVu.Models.Review>())
                        {
                            <div class="border-bottom mb-3 pb-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>@review.User.FullName</strong>
                                    </div>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="@(i <= review.Rating ? "fas" : "far") fa-star"></i>
                                        }
                                    </div>
                                </div>
                                <div class="text-muted small">@review.CreatedAt.ToString("dd/MM/yyyy")</div>
                                <div>@review.Comment</div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Chưa có đánh giá nào cho địa điểm này.
                        </div>
                    }

                    @if (User.Identity != null && User.Identity.IsAuthenticated)
                    {
                        <div class="mt-4">
                            <a asp-controller="Review" asp-action="Create" asp-route-type="Location" asp-route-id="@Model.Id" class="btn btn-primary">
                                <i class="fas fa-star me-2"></i>Viết đánh giá
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm border-0 mb-4 sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Khám phá @Model.Name</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="mb-3">Các khách sạn gần đây</h6>
                        @if (Model.Accommodations != null && Model.Accommodations.Any())
                        {
                            <ul class="list-group">
                                @foreach (var accommodation in Model.Accommodations.Take(5))
                                {
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="text-decoration-none">
                                            @accommodation.Name
                                        </a>
                                        <span class="badge bg-primary rounded-pill">
                                            @for (int i = 0; i < accommodation.StarRating; i++)
                                            {
                                                <i class="fas fa-star"></i>
                                            }
                                        </span>
                                    </li>
                                }
                            </ul>
                            <div class="mt-2">
                                <a asp-controller="Search" asp-action="Results" asp-route-locationId="@Model.Id" class="btn btn-outline-primary btn-sm w-100">
                                    <i class="fas fa-search me-1"></i>Tìm thêm khách sạn
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Chưa có khách sạn nào tại địa điểm này.
                            </div>
                        }
                    </div>

                    <div class="mb-4">
                        <h6 class="mb-3">Các tour tại @Model.Name</h6>
                        @if (Model.Tours != null && Model.Tours.Any())
                        {
                            <ul class="list-group">
                                @foreach (var tour in Model.Tours?.Take(5) ?? Enumerable.Empty<ViVu.Models.Tour>())
                                {
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="text-decoration-none">
                                            @tour.Name
                                        </a>
                                        <span class="badge bg-success rounded-pill">@tour.Duration ngày</span>
                                    </li>
                                }
                            </ul>
                            <div class="mt-2">
                                <a asp-controller="TourSearch" asp-action="Index" asp-route-locationId="@Model.Id" class="btn btn-outline-primary btn-sm w-100">
                                    <i class="fas fa-search me-1"></i>Tìm thêm tour
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Chưa có tour nào tại địa điểm này.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }

        .rating:not(:checked) > input {
            position: absolute;
            clip: rect(0,0,0,0);
        }

        .rating:not(:checked) > label {
            float: right;
            width: 1em;
            padding: 0 .1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 2rem;
            line-height: 1.2;
            color: #ddd;
        }

        .rating:not(:checked) > label:before {
            content: '★ ';
        }

        .rating > input:checked ~ label {
            color: #ffb700;
        }

        .rating:not(:checked) > label:hover,
        .rating:not(:checked) > label:hover ~ label {
            color: #ffb700;
        }

        .rating > input:checked + label:hover,
        .rating > input:checked + label:hover ~ label,
        .rating > input:checked ~ label:hover,
        .rating > input:checked ~ label:hover ~ label,
        .rating > label:hover ~ input:checked ~ label {
            color: #ffb700;
        }
    </style>
}
