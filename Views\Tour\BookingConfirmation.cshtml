@model ViVu.Models.TourBooking

@{
    ViewData["Title"] = "Xác nhận đặt tour";
}

<div class="container py-5">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger mb-4">
            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
        </div>
    }

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <h2 class="mt-3">Đặt tour thành công!</h2>
                        <p class="text-muted">Cảm ơn bạn đã đặt tour. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.</p>
                    </div>

                    <div class="border-top border-bottom py-4 mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Mã đặt tour:</strong> #@Model.Id</p>
                                <p class="mb-1"><strong>Ngày đặt:</strong> @Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                                <p class="mb-1"><strong>Ngày tour:</strong> @Model.TourDate.ToString("dd/MM/yyyy")</p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <p class="mb-1"><strong>Trạng thái:</strong> <span class="badge bg-warning">Chờ xác nhận</span></p>
                                <p class="mb-1"><strong>Tổng tiền:</strong> <span class="text-primary fw-bold">@Model.TotalPrice.ToString("N0") VNĐ</span></p>
                            </div>
                        </div>
                    </div>

                    <h5 class="mb-3">Chi tiết tour</h5>
                    <div class="table-responsive mb-4">
                        <table class="table">
                            <thead class="table-light">
                                <tr>
                                    <th>Tour</th>
                                    <th>Ngày</th>
                                    <th>Số người lớn</th>
                                    <th>Số trẻ em</th>
                                    <th class="text-end">Giá</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var detail in Model.TourBookingDetails)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(detail.Tour.ImageUrl))
                                                {
                                                    <img src="@detail.Tour.ImageUrl" alt="@detail.Tour.Name" class="me-2" style="width: 50px; height: 50px; object-fit: cover;">
                                                }
                                                <div>
                                                    <h6 class="mb-0">@detail.Tour.Name</h6>
                                                    <small class="text-muted">@detail.Tour.Duration ngày</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@detail.TourDate.ToString("dd/MM/yyyy")</td>
                                        <td>@detail.NumberOfAdults</td>
                                        <td>@detail.NumberOfChildren</td>
                                        <td class="text-end">@detail.Price.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Tổng cộng:</strong></td>
                                    <td class="text-end"><strong>@Model.TotalPrice.ToString("N0") VNĐ</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="mb-4">
                            <h5 class="mb-3">Yêu cầu đặc biệt</h5>
                            <div class="p-3 bg-light rounded">
                                @Model.SpecialRequests
                            </div>
                        </div>
                    }

                    <div class="text-center mt-4">
                        <a asp-controller="Tour" asp-action="MyBookings" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>Xem đơn đặt tour của tôi
                        </a>
                        <a asp-controller="Tour" asp-action="Index" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-search me-2"></i>Tìm thêm tour
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
