﻿﻿using Microsoft.AspNetCore.Mvc;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class LocationController : Controller
    {
        private readonly ILocationRepository _locationRepository;

        public LocationController(ILocationRepository locationRepository)
        {
            _locationRepository = locationRepository;
        }

        // GET: Location/Index
        public async Task<IActionResult> Index()
        {
            var locations = await _locationRepository.GetAllAsync();
            return View(locations);
        }

        // GET: Location/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var location = await _locationRepository.GetByIdWithDetailsAsync(id);
            if (location == null)
            {
                return NotFound();
            }

            return View(location);
        }
    }
}
