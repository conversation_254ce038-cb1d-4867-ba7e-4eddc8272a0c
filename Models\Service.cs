﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class Service
    {
        public int Id { get; set; }

        [Required, StringLength(100)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string Details { get; set; }

        public string? ImageUrl { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        [Required]
        [Range(1, 1000)]
        public int Duration { get; set; } // Duration in minutes

        public bool IsFeatured { get; set; } = false;

        public int LocationId { get; set; }

        public int CityId { get; set; }

        [ForeignKey("LocationId")]
        public Location? Location { get; set; }

        [ForeignKey("CityId")]
        public City? City { get; set; }

        public List<ServiceImage>? Images { get; set; }

        public List<Review>? Reviews { get; set; }

        public List<ServiceBookingDetail>? ServiceBookingDetails { get; set; }

        [NotMapped]
        public double AverageRating => Reviews?.Any() == true ? Reviews.Average(r => r.Rating) : 0;
    }
}
