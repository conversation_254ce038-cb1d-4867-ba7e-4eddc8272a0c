@model ViVu.Models.Booking
@{
    ViewData["Title"] = "Xóa đặt phòng";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Xóa đặt phòng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Đặt phòng</a></li>
        <li class="breadcrumb-item active">Xóa</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Xác nhận xóa đặt phòng
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> <PERSON><PERSON><PERSON> báo!</h5>
                <p><PERSON>ạn có chắc chắn muốn xóa đơn đặt phòng này không? Hành động này không thể hoàn tác.</p>
            </div>

            <div asp-validation-summary="All" class="text-danger"></div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Mã đơn:</dt>
                        <dd class="col-sm-8">#@Model.Id</dd>

                        <dt class="col-sm-4">Khách hàng:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.FullName</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.ApplicationUser?.Email</dd>

                        <dt class="col-sm-4">Ngày đặt:</dt>
                        <dd class="col-sm-8">@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">Ngày nhận phòng:</dt>
                        <dd class="col-sm-8">@Model.CheckInDate.ToString("dd/MM/yyyy")</dd>

                        <dt class="col-sm-4">Ngày trả phòng:</dt>
                        <dd class="col-sm-8">@Model.CheckOutDate.ToString("dd/MM/yyyy")</dd>

                        <dt class="col-sm-4">Tổng tiền:</dt>
                        <dd class="col-sm-8">@Model.TotalPrice.ToString("N0") VNĐ</dd>

                        <dt class="col-sm-4">Trạng thái:</dt>
                        <dd class="col-sm-8">
                            @if (Model.Status == BookingStatus.Pending)
                            {
                                <span class="badge bg-warning">Chờ xác nhận</span>
                            }
                            else if (Model.Status == BookingStatus.Confirmed)
                            {
                                <span class="badge bg-primary">Đã xác nhận</span>
                            }
                            else if (Model.Status == BookingStatus.Completed)
                            {
                                <span class="badge bg-success">Hoàn thành</span>
                            }
                            else if (Model.Status == BookingStatus.Cancelled)
                            {
                                <span class="badge bg-danger">Đã hủy</span>
                            }
                        </dd>
                    </dl>
                </div>
            </div>

            <div class="table-responsive mb-4">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Phòng</th>
                            <th>Chỗ ở</th>
                            <th>Giá/đêm</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.BookingDetails)
                        {
                            <tr>
                                <td>@detail.Room.Name</td>
                                <td>@detail.Room.Accommodation?.Name</td>
                                <td>@detail.PricePerNight.ToString("N0") VNĐ</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <form asp-action="DeleteConfirmed" method="post">
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </form>
        </div>
    </div>
</div>
