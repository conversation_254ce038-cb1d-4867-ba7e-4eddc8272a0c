﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFPanorama360Repository : IPanorama360Repository
    {
        private readonly ApplicationDbContext _context;

        public EFPanorama360Repository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Panorama360>> GetAllAsync()
        {
            return await _context.Panorama360s
                .Include(p => p.Tour)
                .Include(p => p.Location)
                .ToListAsync();
        }

        public async Task<Panorama360> GetByIdAsync(int id)
        {
            return await _context.Panorama360s
                .Include(p => p.Tour)
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Panorama360>> GetByTourIdAsync(int tourId)
        {
            return await _context.Panorama360s
                .Where(p => p.TourId == tourId)
                .OrderBy(p => p.DisplayOrder)
                .ToListAsync();
        }

        public async Task<IEnumerable<Panorama360>> GetByLocationIdAsync(int locationId)
        {
            return await _context.Panorama360s
                .Where(p => p.LocationId == locationId)
                .OrderBy(p => p.DisplayOrder)
                .ToListAsync();
        }

        public async Task AddAsync(Panorama360 panorama)
        {
            await _context.Panorama360s.AddAsync(panorama);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Panorama360 panorama)
        {
            _context.Panorama360s.Update(panorama);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var panorama = await _context.Panorama360s.FindAsync(id);
            if (panorama != null)
            {
                _context.Panorama360s.Remove(panorama);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Panorama360s.AnyAsync(p => p.Id == id);
        }
    }
}
