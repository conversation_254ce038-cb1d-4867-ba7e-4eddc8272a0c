@model ViVu.Models.Review
@{
    ViewData["Title"] = "Xóa đánh giá";
    
    string itemName = "";
    string itemType = "";
    int itemId = 0;
    
    if (Model.AccommodationId.HasValue)
    {
        itemName = Model.Accommodation?.Name;
        itemType = "Accommodation";
        itemId = Model.AccommodationId.Value;
    }
    else if (Model.TourId.HasValue)
    {
        itemName = Model.Tour?.Name;
        itemType = "Tour";
        itemId = Model.TourId.Value;
    }
    else if (Model.ServiceId.HasValue)
    {
        itemName = Model.Service?.Name;
        itemType = "Service";
        itemId = Model.ServiceId.Value;
    }
}

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">Xác nhận xóa đánh giá</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Bạn có chắc chắn muốn xóa đánh giá này không? Hành động này không thể hoàn tác.
                    </div>
                    
                    <div class="mb-4">
                        <h5>Thông tin đánh giá</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th>Đối tượng đánh giá:</th>
                                <td>@itemName</td>
                            </tr>
                            <tr>
                                <th>Đánh giá:</th>
                                <td>
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        if (i <= Model.Rating)
                                        {
                                            <i class="bi bi-star-fill text-warning"></i>
                                        }
                                        else
                                        {
                                            <i class="bi bi-star text-warning"></i>
                                        }
                                    }
                                </td>
                            </tr>
                            <tr>
                                <th>Nhận xét:</th>
                                <td>@Model.Comment</td>
                            </tr>
                            <tr>
                                <th>Ngày đánh giá:</th>
                                <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                            </tr>
                        </table>
                    </div>
                    
                    <form asp-action="Delete" method="post">
                        <input type="hidden" asp-for="Id" />
                        <div class="d-flex justify-content-between">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> Xác nhận xóa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
