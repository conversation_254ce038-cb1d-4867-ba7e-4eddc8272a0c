﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace ViVu.Models
{
    public class TourBooking
    {
        public int Id { get; set; }
        
        public string UserId { get; set; }
        
        [Required]
        public DateTime BookingDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        public DateTime TourDate { get; set; }
        
        [Required]
        public decimal TotalPrice { get; set; }
        
        public string? SpecialRequests { get; set; }
        
        public TourBookingStatus Status { get; set; } = TourBookingStatus.Pending;
        
        [ForeignKey("UserId")]
        [ValidateNever]
        public ApplicationUser ApplicationUser { get; set; }
        
        public List<TourBookingDetail> TourBookingDetails { get; set; }
    }
    
    public enum TourBookingStatus
    {
        Pending,
        Confirmed,
        Completed,
        Cancelled
    }
}
