﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Models.ViewModels;
using ViVu.Repositories;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;

namespace ViVu.Controllers
{
    public class ExploreController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IPanorama360Repository _panorama360Repository;
        private readonly ILocationRepository _locationRepository;

        public ExploreController(
            ApplicationDbContext context,
            IPanorama360Repository panorama360Repository,
            ILocationRepository locationRepository)
        {
            _context = context;
            _panorama360Repository = panorama360Repository;
            _locationRepository = locationRepository;
        }

        // GET: Explore/BenTre
        public async Task<IActionResult> BenTre()
        {
            // Lấy các tour liên quan đến Bến Tre
            var benTreTours = await _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .Where(t => (t.Location != null && t.Location.Name.Contains("Bến Tre")) ||
                           (t.City != null && t.City.Name.Contains("Bến Tre")) ||
                           t.Name.Contains("Bến Tre") ||
                           t.Description.Contains("Bến Tre"))
                .ToListAsync();

            // Lấy các khách sạn ở Bến Tre
            var benTreAccommodations = await _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Rooms)
                .Include(a => a.Images)
                .Where(a => (a.Location != null && a.Location.Name.Contains("Bến Tre")) ||
                           (a.City != null && a.City.Name.Contains("Bến Tre")))
                .ToListAsync();

            // Tính giá thấp nhất cho mỗi khách sạn
            foreach (var accommodation in benTreAccommodations)
            {
                accommodation.MinPrice = accommodation.Rooms.Any()
                    ? accommodation.Rooms.Min(r => r.PricePerNight)
                    : 0;
            }

            // Thông tin tổng quan về Bến Tre
            var benTreInfo = new
            {
                Name = "Bến Tre",
                Description = "Bến Tre được mệnh danh là \"Xứ Dừa\" với hơn 72.000 hecta dừa, chiếm 1/3 diện tích dừa cả nước. Đến với Bến Tre, du khách sẽ được đắm mình trong không gian xanh mát của những vườn dừa bạt ngàn, thưởng thức hương vị ngọt ngào của nước dừa tươi và khám phá các sản phẩm thủ công mỹ nghệ được làm từ dừa.",
                Area = "2,360 km²",
                Population = "1,288,463 người (2019)",
                Districts = "1 thành phố, 1 thị xã và 7 huyện",
                Climate = "Nhiệt đới gió mùa, nóng ẩm quanh năm",
                BestTimeToVisit = "Từ tháng 12 đến tháng 4 năm sau",
                FamousProducts = "Kẹo dừa, bánh tráng, rượu dừa, đồ thủ công mỹ nghệ từ dừa",
                ImageUrl = "/images/banners/banner_home_01.jpg",
                Latitude = 10.2433,
                Longitude = 106.3756
            };

            // Các điểm du lịch nổi bật ở Bến Tre
            var tourismHighlights = new List<TourismHighlight>
            {
                new TourismHighlight
                {
                    Name = "Cồn Phụng",
                    Description = "Cồn Phụng là một trong những điểm du lịch nổi tiếng nhất của Bến Tre, nằm giữa dòng sông Tiền. Nơi đây nổi tiếng với khu di tích Đạo Dừa - một tôn giáo độc đáo do ông Nguyễn Thành Nam (Đạo Dừa) sáng lập vào những năm 1960.",
                    Activities = "Tham quan khu di tích, thưởng thức ẩm thực địa phương, xem biểu diễn xiếc dừa, nghe đờn ca tài tử",
                    ImageUrl = "/images/destinations/conphung.jpg",
                    Latitude = 10.2589,
                    Longitude = 106.3982
                },
                new TourismHighlight
                {
                    Name = "Làng nghề dừa",
                    Description = "Bến Tre có nhiều làng nghề truyền thống chế biến các sản phẩm từ dừa như kẹo dừa, đồ thủ công mỹ nghệ, các sản phẩm gia dụng... Tham quan các làng nghề này, du khách sẽ được tìm hiểu quy trình sản xuất và trực tiếp tham gia vào một số công đoạn.",
                    Activities = "Tham quan quy trình sản xuất, mua sắm đồ lưu niệm, thưởng thức các món ăn từ dừa",
                    ImageUrl = "/images/destinations/langnghedua.jpg",
                    Latitude = 10.2401,
                    Longitude = 106.3645
                },
                new TourismHighlight
                {
                    Name = "Cù lao Thới Sơn",
                    Description = "Cù lao Thới Sơn là một hòn đảo xinh đẹp nằm giữa sông Tiền, thuộc địa phận tỉnh Bến Tre. Đây là điểm du lịch sinh thái nổi tiếng với những vườn cây ăn trái sum suê, những con đường làng yên bình và những kênh rạch nhỏ uốn lượn.",
                    Activities = "Đi xuồng ba lá, thưởng thức trái cây tươi, nghe đờn ca tài tử, tìm hiểu cuộc sống người dân địa phương",
                    ImageUrl = "/images/destinations/culaothoison.jpg",
                    Latitude = 10.2765,
                    Longitude = 106.3654
                }
            };

            // Văn hóa và ẩm thực Bến Tre
            var cultureAndCuisine = new
            {
                Culture = new
                {
                    Description = "Bến Tre có nền văn hóa đặc sắc với nhiều loại hình nghệ thuật dân gian như đờn ca tài tử, hát bội, hò Bến Tre... Đặc biệt, đờn ca tài tử Nam Bộ đã được UNESCO công nhận là di sản văn hóa phi vật thể của nhân loại.",
                    Festivals = "Lễ hội Dừa, Lễ hội Nghinh Ông, Lễ hội Làm Chay",
                    TraditionalArts = "Đờn ca tài tử, hát bội, hò Bến Tre",
                    ImageUrl = "/images/culture/doncataitur.jpg"
                },
                Cuisine = new
                {
                    Description = "Ẩm thực Bến Tre mang đậm hương vị miền sông nước với nhiều món ăn đặc sản như cá lóc nướng trui, chuột đồng nướng, lẩu mắm, bánh tráng nướng mỡ hành... Đặc biệt, các món ăn chế biến từ dừa như cơm dừa, chè dừa, ốc len xào dừa... là những món ăn không thể bỏ qua khi đến Bến Tre.",
                    FamousDishes = "Cá lóc nướng trui, chuột đồng nướng, lẩu mắm, các món từ dừa",
                    TraditionalCakes = "Bánh tét, bánh ít trần, bánh phồng sữa dừa",
                    Fruits = "Sầu riêng, chôm chôm, măng cụt, dừa xiêm",
                    ImageUrl = "/images/cuisine/amthucbentre.jpg"
                }
            };

            // Lịch sử Bến Tre
            var history = new
            {
                Formation = "Bến Tre là một tỉnh thuộc vùng đồng bằng sông Cửu Long, có lịch sử hình thành và phát triển gắn liền với quá trình khai phá vùng đất Nam Bộ của người Việt từ thế kỷ XVII. Vào năm 1732, vùng đất Bến Tre được chính thức thành lập dưới thời chúa Nguyễn Phúc Chu.",
                RevolutionaryTradition = "Bến Tre nổi tiếng với truyền thống cách mạng kiên cường. Trong kháng chiến chống Pháp và chống Mỹ, Bến Tre là một trong những địa phương có phong trào cách mạng mạnh mẽ nhất ở miền Nam. Đặc biệt, Bến Tre là quê hương của phong trào \"Đồng khởi\" nổi tiếng bắt đầu từ năm 1960.",
                EconomicDevelopment = "Từ sau năm 1975, Bến Tre tập trung phát triển kinh tế, đặc biệt là nông nghiệp với cây dừa là cây trồng chủ lực. Đến nay, Bến Tre đã trở thành \"thủ phủ\" của cây dừa Việt Nam với diện tích trồng dừa lớn nhất cả nước."
            };

            // Lấy dữ liệu panorama 360° cho các địa điểm ở Bến Tre
            var benTrePanoramas = new List<Panorama360>();

            // Lấy panorama cho các địa điểm nổi bật
            foreach (var highlight in tourismHighlights)
            {
                // Tìm location tương ứng với highlight
                var location = await _context.Locations
                    .FirstOrDefaultAsync(l => l.Name.Contains(highlight.Name));

                if (location != null)
                {
                    // Lấy panorama cho location này
                    var locationPanoramas = await _panorama360Repository.GetByLocationIdAsync(location.Id);
                    benTrePanoramas.AddRange(locationPanoramas);
                }
            }

            // Lấy panorama cho các tour Bến Tre
            foreach (var tour in benTreTours.Take(3))
            {
                var tourPanoramas = await _panorama360Repository.GetByTourIdAsync(tour.Id);
                benTrePanoramas.AddRange(tourPanoramas);
            }

            // Lọc các panorama trùng lặp
            benTrePanoramas = benTrePanoramas.GroupBy(p => p.Id).Select(g => g.First()).ToList();

            // Truyền dữ liệu vào ViewBag
            ViewBag.BenTreTours = benTreTours;
            ViewBag.BenTreAccommodations = benTreAccommodations;
            ViewBag.BenTreInfo = benTreInfo;
            ViewBag.TourismHighlights = tourismHighlights;
            ViewBag.CultureAndCuisine = cultureAndCuisine;
            ViewBag.History = history;
            ViewBag.BenTrePanoramas = benTrePanoramas;

            return View();
        }
    }
}
