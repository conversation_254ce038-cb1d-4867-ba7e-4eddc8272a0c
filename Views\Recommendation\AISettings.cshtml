@model ViVu.Models.UserPreference

@{
    ViewData["Title"] = "Cài đặt AI";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h2 class="mb-0">Cài đặt AI cho Gợi ý Du lịch</h2>
                </div>
                <div class="card-body">
                    <form asp-action="AISettings" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="form-group mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" asp-for="UseAI" id="useAI">
                                <label class="form-check-label" for="useAI">Sử dụng AI để tạo gợi ý du lịch</label>
                            </div>
                            <small class="form-text text-muted">
                                <PERSON><PERSON> b<PERSON><PERSON> t<PERSON><PERSON> nă<PERSON> nà<PERSON>, hệ thống sẽ sử dụng AI để tạo gợi ý du lịch cá nhân hóa dựa trên sở thích và lịch sử du lịch của bạn.
                            </small>
                        </div>
                        
                        <div class="form-group mb-4" id="aiPromptGroup">
                            <label asp-for="AIPrompt" class="control-label">Yêu cầu cụ thể cho AI</label>
                            <textarea asp-for="AIPrompt" class="form-control" rows="4" placeholder="Ví dụ: Tôi muốn một lịch trình tập trung vào ẩm thực địa phương, có nhiều hoạt động ngoài trời và phù hợp với gia đình có trẻ nhỏ."></textarea>
                            <small class="form-text text-muted">
                                Nhập yêu cầu cụ thể để AI tạo gợi ý phù hợp hơn với nhu cầu của bạn. Nếu để trống, AI sẽ tạo gợi ý dựa trên sở thích chung của bạn.
                            </small>
                        </div>
                        
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> Thông tin về tính năng AI</h5>
                            <p>Tính năng AI sử dụng công nghệ OpenAI để tạo gợi ý du lịch cá nhân hóa. Dữ liệu của bạn sẽ được xử lý an toàn và bảo mật.</p>
                            <p>Lưu ý: Gợi ý từ AI chỉ mang tính tham khảo và có thể không phản ánh đầy đủ tất cả các điều kiện thực tế.</p>
                        </div>
                        
                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg px-5">Lưu cài đặt</button>
                            <a asp-action="Index" class="btn btn-outline-secondary btn-lg px-5 ms-2">Quay lại</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Hiển thị/ẩn phần yêu cầu AI dựa trên trạng thái checkbox
            function toggleAIPrompt() {
                if ($("#useAI").is(":checked")) {
                    $("#aiPromptGroup").show();
                } else {
                    $("#aiPromptGroup").hide();
                }
            }
            
            // Kiểm tra trạng thái ban đầu
            toggleAIPrompt();
            
            // Xử lý sự kiện khi checkbox thay đổi
            $("#useAI").change(function() {
                toggleAIPrompt();
            });
        });
    </script>
}
