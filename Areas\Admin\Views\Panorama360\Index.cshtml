@model IEnumerable<ViVu.Models.Panorama360>

@{
    ViewData["Title"] = "Quản lý Du lịch 360°";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý Du lịch 360°</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">Dashboard</a></li>
        <li class="breadcrumb-item active">Quản lý Du lịch 360°</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-vr-cardboard me-1"></i>
                Danh sách Panorama 360°
            </div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm mới
            </a>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tên</th>
                        <th>Loại</th>
                        <th>Tour/Địa điểm</th>
                        <th>Hình ảnh</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Id</td>
                            <td>@item.Name</td>
                            <td>@item.Type</td>
                            <td>
                                @if (item.Type == "Tour" && item.Tour != null)
                                {
                                    @item.Tour.Name
                                }
                                else if (item.Type == "Location" && item.Location != null)
                                {
                                    @item.Location.Name
                                }
                            </td>
                            <td>
                                <img src="@item.ImageUrl" alt="@item.Name" style="width: 100px; height: 60px; object-fit: cover;" />
                            </td>
                            <td>
                                @if (item.IsActive)
                                {
                                    <span class="badge bg-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không hoạt động</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="@Url.Action("View", "Panorama360", new { id = item.Id, area = "" })" class="btn btn-info btn-sm" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
