@model ViVu.Models.VehicleBooking

@{
    ViewData["Title"] = "Xác nhận đặt phương tiện";
}

<div class="container py-5">
    <div class="text-center mb-5">
        <div class="mb-4">
            <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
        </div>
        <h1 class="mb-3">Đặt phương tiện thành công!</h1>
        <p class="lead">Cảm ơn bạn đã đặt phương tiện. Mã đơn hàng của bạn là <strong>#@Model.Id</strong>.</p>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin đặt phương tiện</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p><strong>Mã đơn hàng:</strong> #@Model.Id</p>
                            <p><strong>Ngày đặt:</strong> @Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                            <p><strong>Trạng thái:</strong> <span class="badge bg-warning">@Model.Status.ToString()</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Ngày bắt đầu:</strong> @Model.StartDate.ToString("dd/MM/yyyy")</p>
                            <p><strong>Ngày kết thúc:</strong> @Model.EndDate.ToString("dd/MM/yyyy")</p>
                            <p><strong>Tổng tiền:</strong> @Model.TotalPrice.ToString("#,##0") VNĐ</p>
                        </div>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="mb-4">
                            <h6>Yêu cầu đặc biệt:</h6>
                            <p>@Model.SpecialRequests</p>
                        </div>
                    }
                    
                    <h6 class="mb-3">Chi tiết phương tiện:</h6>
                    
                    @foreach (var detail in Model.VehicleBookingDetails)
                    {
                        <div class="card mb-3 border-0 shadow-sm">
                            <div class="row g-0">
                                <div class="col-md-3">
                                    <img src="@(string.IsNullOrEmpty(detail.Vehicle?.ImageUrl) ? "/images/no-image.jpg" : detail.Vehicle.ImageUrl)" 
                                         class="img-fluid rounded-start" alt="@detail.Vehicle?.Name" style="height: 100%; object-fit: cover;">
                                </div>
                                <div class="col-md-9">
                                    <div class="card-body">
                                        <h5 class="card-title">@detail.Vehicle?.Name</h5>
                                        <p class="card-text">
                                            <span class="badge bg-primary">@detail.Vehicle?.Type.ToString()</span>
                                        </p>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Ngày bắt đầu:</strong> @detail.StartDate.ToString("dd/MM/yyyy")</p>
                                                <p class="mb-1"><strong>Ngày kết thúc:</strong> @detail.EndDate.ToString("dd/MM/yyyy")</p>
                                                <p class="mb-1"><strong>Số ngày thuê:</strong> @detail.NumberOfDays ngày</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Giá thuê/ngày:</strong> @detail.PricePerDay.ToString("#,##0") VNĐ</p>
                                                <p class="mb-1"><strong>Tổng tiền:</strong> @detail.TotalPrice.ToString("#,##0") VNĐ</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>Chúng tôi sẽ liên hệ với bạn để xác nhận đơn đặt phương tiện của bạn. Vui lòng kiểm tra email và điện thoại của bạn.
            </div>
            
            <div class="d-flex justify-content-between">
                <a asp-action="Index" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>Tiếp tục thuê phương tiện
                </a>
                <a asp-action="MyBookings" class="btn btn-primary">
                    <i class="fas fa-list me-2"></i>Xem đơn đặt phương tiện của tôi
                </a>
            </div>
        </div>
    </div>
</div>
