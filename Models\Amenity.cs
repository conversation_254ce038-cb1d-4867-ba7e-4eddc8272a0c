﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models
{
    public class Amenity
    {
        public int Id { get; set; }
        
        [Required, StringLength(100)]
        public string Name { get; set; }
        
        public string? Description { get; set; }
        
        public string? Icon { get; set; }
        
        public AmenityType Type { get; set; }
        
        public List<AccommodationAmenity>? AccommodationAmenities { get; set; }
        
        public List<RoomAmenity>? RoomAmenities { get; set; }
    }
    
    public enum AmenityType
    {
        Accommodation,
        Room,
        Both
    }
}
