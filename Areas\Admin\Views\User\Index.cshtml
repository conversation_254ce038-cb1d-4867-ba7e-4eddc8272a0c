@model IEnumerable<ViVu.Areas.Admin.Controllers.UserViewModel>
@{
    ViewData["Title"] = "Quản lý người dùng";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý người dùng</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Người dùng</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div><i class="fas fa-users me-1"></i> Danh sách người dùng</div>
                <a asp-action="Create" class="btn btn-success btn-sm">
                    <i class="fas fa-user-plus"></i> Thêm mới
                </a>
            </div>
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Họ tên</th>
                        <th>Email</th>
                        <th>Điện thoại</th>
                        <th>Địa chỉ</th>
                        <th>Vai trò</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.User.FullName</td>
                            <td>@item.User.Email</td>
                            <td>@item.User.PhoneNumber</td>
                            <td>@item.User.Address</td>
                            <td>
                                @foreach (var role in item.Roles)
                                {
                                    <span class="badge bg-primary me-1">@role</span>
                                }
                            </td>
                            <td>@item.User.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>
                                <div class="d-flex">
                                    <a asp-action="Details" asp-route-id="@item.User.Id" class="btn btn-info btn-sm me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.User.Id" class="btn btn-warning btn-sm me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.User.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
