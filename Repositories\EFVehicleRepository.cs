﻿﻿using Microsoft.EntityFrameworkCore;
using ViVu.Models;

namespace ViVu.Repositories
{
    public class EFVehicleRepository : IVehicleRepository
    {
        private readonly ApplicationDbContext _context;

        public EFVehicleRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Vehicle>> GetAllAsync()
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .ToListAsync();
        }

        public async Task<Vehicle> GetByIdAsync(int id)
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .FirstOrDefaultAsync(v => v.Id == id);
        }

        public async Task<Vehicle> GetByIdWithDetailsAsync(int id)
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Include(v => v.Images)
                .Include(v => v.Reviews)
                    .ThenInclude(r => r.User)
                .FirstOrDefaultAsync(v => v.Id == id);
        }

        public async Task<IEnumerable<Vehicle>> GetByLocationIdAsync(int locationId)
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Where(v => v.LocationId == locationId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Vehicle>> GetByCityIdAsync(int cityId)
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Where(v => v.CityId == cityId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Vehicle>> GetFeaturedVehiclesAsync(int count = 6)
        {
            return await _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Include(v => v.Reviews)
                .Where(v => v.IsFeatured)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<Vehicle>> SearchAsync(string searchTerm = null, int? locationId = null, int? cityId = null, 
            DateTime? startDate = null, DateTime? endDate = null, decimal? minPrice = null, decimal? maxPrice = null, VehicleType? vehicleType = null)
        {
            var query = _context.Vehicles
                .Include(v => v.Location)
                .Include(v => v.City)
                .Include(v => v.Reviews)
                .AsQueryable();

            // Tìm kiếm theo tên hoặc mô tả
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(v => v.Name.Contains(searchTerm) || 
                                        v.Description.Contains(searchTerm));
            }

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                query = query.Where(v => v.LocationId == locationId.Value);
            }

            // Lọc theo thành phố
            if (cityId.HasValue)
            {
                query = query.Where(v => v.CityId == cityId.Value);
            }

            // Lọc theo giá
            if (minPrice.HasValue)
            {
                query = query.Where(v => v.PricePerDay >= minPrice.Value);
            }

            if (maxPrice.HasValue)
            {
                query = query.Where(v => v.PricePerDay <= maxPrice.Value);
            }

            // Lọc theo loại phương tiện
            if (vehicleType.HasValue)
            {
                query = query.Where(v => v.Type == vehicleType.Value);
            }

            // Lọc theo ngày có sẵn (không có đặt chỗ trong khoảng thời gian)
            if (startDate.HasValue && endDate.HasValue)
            {
                // Lấy danh sách ID của các phương tiện đã được đặt trong khoảng thời gian
                var bookedVehicleIds = await _context.VehicleBookingDetails
                    .Where(vbd => 
                        (vbd.StartDate <= endDate.Value && vbd.EndDate >= startDate.Value) &&
                        vbd.VehicleBooking.Status != VehicleBookingStatus.Cancelled)
                    .Select(vbd => vbd.VehicleId)
                    .Distinct()
                    .ToListAsync();

                // Loại bỏ các phương tiện đã đặt
                query = query.Where(v => !bookedVehicleIds.Contains(v.Id));
            }

            return await query.ToListAsync();
        }

        public async Task AddAsync(Vehicle vehicle)
        {
            await _context.Vehicles.AddAsync(vehicle);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Vehicle vehicle)
        {
            _context.Vehicles.Update(vehicle);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var vehicle = await _context.Vehicles.FindAsync(id);
            if (vehicle != null)
            {
                _context.Vehicles.Remove(vehicle);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Vehicles.AnyAsync(v => v.Id == id);
        }
    }
}
