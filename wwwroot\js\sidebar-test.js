// Test file for sidebar dropdown functionality
console.log('Sidebar test script loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, testing sidebar dropdowns...');
    
    // Test if Bootstrap is loaded
    if (typeof bootstrap !== 'undefined') {
        console.log('✓ Bootstrap is loaded');
    } else {
        console.error('✗ Bootstrap is not loaded');
    }
    
    // Test if dropdown buttons exist
    const dropdownButtons = document.querySelectorAll('.sidebar-dropdown .dropdown-toggle');
    console.log(`Found ${dropdownButtons.length} dropdown buttons`);
    
    dropdownButtons.forEach((button, index) => {
        console.log(`Button ${index + 1}:`, button);
        
        const menu = button.nextElementSibling;
        if (menu && menu.classList.contains('sidebar-dropdown-menu')) {
            console.log(`✓ Menu found for button ${index + 1}`);
        } else {
            console.error(`✗ Menu not found for button ${index + 1}`);
        }
        
        // Test click event
        button.addEventListener('click', function(e) {
            console.log(`Button ${index + 1} clicked`);
        });
    });
    
    // Test CSS classes
    const sidebarNav = document.querySelector('.sidebar-nav');
    if (sidebarNav) {
        console.log('✓ Sidebar nav found');
        console.log('Sidebar nav styles:', window.getComputedStyle(sidebarNav));
    } else {
        console.error('✗ Sidebar nav not found');
    }
});

// Add global test function
window.testSidebarDropdown = function() {
    console.log('Testing sidebar dropdown manually...');
    
    const firstDropdown = document.querySelector('.sidebar-dropdown .dropdown-toggle');
    if (firstDropdown) {
        console.log('Clicking first dropdown...');
        firstDropdown.click();
        
        setTimeout(() => {
            const menu = firstDropdown.nextElementSibling;
            if (menu && menu.classList.contains('show')) {
                console.log('✓ Dropdown opened successfully');
            } else {
                console.error('✗ Dropdown failed to open');
            }
        }, 500);
    }
};
