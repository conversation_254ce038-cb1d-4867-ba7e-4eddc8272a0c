@model ViVu.Models.Vehicle

@{
    ViewData["Title"] = Model.Name;
    var startDate = ViewBag.StartDate;
    var endDate = ViewBag.EndDate;
    int numberOfDays = (endDate - startDate).Days;
}

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="mb-4">
                <h1 class="mb-2">@Model.Name</h1>
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                        <span>@(Model.Location != null ? Model.Location.Name : ""), @(Model.City != null ? Model.City.Name : "")</span>
                    </div>
                    <div class="me-3">
                        <span class="badge bg-primary">@Model.Type.ToString()</span>
                    </div>
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div>
                            <span class="text-warning">
                                @Model.AverageRating.ToString("0.0")
                                <i class="fas fa-star"></i>
                            </span>
                            <span class="text-muted">(@Model.Reviews.Count đánh giá)</span>
                        </div>
                    }
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3">Thông tin phương tiện</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Biển số:</strong> @Model.LicensePlate</p>
                            <p><strong>Sức chứa:</strong> @Model.Capacity người</p>
                            <p><strong>Giá thuê:</strong> @Model.PricePerDay.ToString("#,##0") VNĐ/ngày</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Trạng thái:</strong> @(Model.IsAvailable ? "Có sẵn" : "Không có sẵn")</p>
                            <p><strong>Loại phương tiện:</strong> @Model.Type.ToString()</p>
                        </div>
                    </div>
                    <h5 class="mb-3">Mô tả</h5>
                    <p>@Model.Description</p>
                    <h5 class="mb-3">Chi tiết</h5>
                    <p>@Model.Details</p>
                </div>
            </div>

            @if (Model.Reviews != null && Model.Reviews.Any())
            {
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Đánh giá (@Model.Reviews.Count)</h5>
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <div class="me-2">
                                    <span class="display-4 fw-bold text-warning">@Model.AverageRating.ToString("0.0")</span>
                                </div>
                                <div>
                                    <div class="mb-1">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= Math.Round(Model.AverageRating))
                                            {
                                                <i class="fas fa-star text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star text-warning"></i>
                                            }
                                        }
                                    </div>
                                    <span class="text-muted">Dựa trên @Model.Reviews.Count đánh giá</span>
                                </div>
                            </div>
                        </div>
                        
                        @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt))
                        {
                            <div class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <div>
                                        <strong>@review.User.FullName</strong>
                                        <div>
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                if (i <= review.Rating)
                                                {
                                                    <i class="fas fa-star text-warning"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star text-warning"></i>
                                                }
                                            }
                                        </div>
                                    </div>
                                    <div class="text-muted">
                                        @review.CreatedAt.ToString("dd/MM/yyyy")
                                    </div>
                                </div>
                                <p class="mb-0">@review.Comment</p>
                            </div>
                        }
                    </div>
                </div>
            }

            @if (ViewBag.CanReview == true)
            {
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Viết đánh giá</h5>
                        <form asp-controller="Review" asp-action="Create" method="post">
                            <input type="hidden" name="Type" value="Vehicle" />
                            <input type="hidden" name="ItemId" value="@Model.Id" />
                            <input type="hidden" name="ItemName" value="@Model.Name" />
                            
                            <div class="mb-3">
                                <label class="form-label">Đánh giá của bạn</label>
                                <div class="rating">
                                    <input type="radio" id="star5" name="Rating" value="5" /><label for="star5"></label>
                                    <input type="radio" id="star4" name="Rating" value="4" /><label for="star4"></label>
                                    <input type="radio" id="star3" name="Rating" value="3" /><label for="star3"></label>
                                    <input type="radio" id="star2" name="Rating" value="2" /><label for="star2"></label>
                                    <input type="radio" id="star1" name="Rating" value="1" /><label for="star1"></label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Nhận xét của bạn</label>
                                <textarea name="Comment" class="form-control" rows="3" required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Gửi đánh giá</button>
                        </form>
                    </div>
                </div>
            }
            
            @if (User.Identity != null && User.Identity.IsAuthenticated)
            {
                <div class="mt-4">
                    <a asp-controller="Review" asp-action="Create" asp-route-type="Vehicle" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-star me-2"></i>Viết đánh giá
                    </a>
                </div>
            }
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm border-0 mb-4 sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Đặt phương tiện</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h3 class="text-primary mb-3">@Model.PricePerDay.ToString("N0") VNĐ<small class="text-muted">/ngày</small></h3>
                    </div>
                    
                    <form asp-action="AddToCart" method="post">
                        <input type="hidden" name="vehicleId" value="@Model.Id" />
                        
                        <div class="mb-3">
                            <label class="form-label">Ngày bắt đầu</label>
                            <input type="date" name="startDate" class="form-control" value="@startDate.ToString("yyyy-MM-dd")" min="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")" required />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Ngày kết thúc</label>
                            <input type="date" name="endDate" class="form-control" value="@endDate.ToString("yyyy-MM-dd")" min="@DateTime.Today.AddDays(2).ToString("yyyy-MM-dd")" required />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Số lượng</label>
                            <input type="number" name="quantity" class="form-control" value="1" min="1" max="5" required />
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Giá thuê/ngày:</span>
                                <span>@Model.PricePerDay.ToString("N0") VNĐ</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Số ngày thuê:</span>
                                <span>@numberOfDays ngày</span>
                            </div>
                            <hr />
                            <div class="d-flex justify-content-between fw-bold">
                                <span>Tổng tiền:</span>
                                <span>@((Model.PricePerDay * numberOfDays).ToString("N0")) VNĐ</span>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }
        
        .rating:not(:checked) > input {
            position: absolute;
            clip: rect(0, 0, 0, 0);
        }
        
        .rating:not(:checked) > label {
            float: right;
            width: 1em;
            padding: 0 .1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 2rem;
            line-height: 1.2;
            color: #ddd;
        }
        
        .rating:not(:checked) > label:before {
            content: '★ ';
        }
        
        .rating > input:checked ~ label {
            color: #f90;
        }
        
        .rating:not(:checked) > label:hover,
        .rating:not(:checked) > label:hover ~ label {
            color: #fc0;
        }
        
        .rating > input:checked + label:hover,
        .rating > input:checked + label:hover ~ label,
        .rating > input:checked ~ label:hover,
        .rating > input:checked ~ label:hover ~ label,
        .rating > label:hover ~ input:checked ~ label {
            color: #ea0;
        }
    </style>
}
