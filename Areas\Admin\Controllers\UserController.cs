using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using ViVu.Data;
using ViVu.Models;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class UserController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ApplicationDbContext _context;

        public UserController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
        }

        // GET: Admin/User
        public async Task<IActionResult> Index()
        {
            var users = await _userManager.Users.ToListAsync();

            // L<PERSON>y thông tin vai trò cho mỗi người dùng
            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);

                userViewModels.Add(new UserViewModel
                {
                    User = user,
                    Roles = roles.ToList()
                });
            }

            return View(userViewModels);
        }

        // GET: Admin/User/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Lấy danh sách vai trò của người dùng
            var roles = await _userManager.GetRolesAsync(user);

            // Lấy danh sách đơn đặt phòng của người dùng
            var bookings = await _context.Bookings
                .Where(b => b.UserId == id)
                .OrderByDescending(b => b.BookingDate)
                .ToListAsync();

            var viewModel = new UserDetailsViewModel
            {
                User = user,
                Roles = roles.ToList(),
                Bookings = bookings
            };

            return View(viewModel);
        }

        // GET: Admin/User/Create
        public async Task<IActionResult> Create()
        {
            // Lấy danh sách vai trò
            var roles = await _roleManager.Roles.ToListAsync();
            ViewBag.Roles = roles;

            return View();
        }

        // POST: Admin/User/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FullName = model.FullName,
                    Address = model.Address,
                    PhoneNumber = model.PhoneNumber,
                    EmailConfirmed = true
                };

                var result = await _userManager.CreateAsync(user, model.Password);

                if (result.Succeeded)
                {
                    // Thêm vai trò cho người dùng
                    if (model.SelectedRoles != null && model.SelectedRoles.Any())
                    {
                        await _userManager.AddToRolesAsync(user, model.SelectedRoles);
                    }

                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError("", error.Description);
                }
            }

            // Lấy lại danh sách vai trò nếu có lỗi
            var roles = await _roleManager.Roles.ToListAsync();
            ViewBag.Roles = roles;

            return View(model);
        }

        // GET: Admin/User/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Lấy danh sách vai trò của người dùng
            var userRoles = await _userManager.GetRolesAsync(user);

            // Lấy tất cả vai trò
            var allRoles = await _roleManager.Roles.ToListAsync();

            var model = new EditUserViewModel
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Address = user.Address,
                PhoneNumber = user.PhoneNumber,
                SelectedRoles = userRoles.ToList()
            };

            ViewBag.Roles = allRoles;

            return View(model);
        }

        // POST: Admin/User/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, EditUserViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                // Cập nhật thông tin người dùng
                user.Email = model.Email;
                user.UserName = model.Email;
                user.FullName = model.FullName;
                user.Address = model.Address;
                user.PhoneNumber = model.PhoneNumber;

                var result = await _userManager.UpdateAsync(user);

                if (result.Succeeded)
                {
                    // Cập nhật vai trò
                    var userRoles = await _userManager.GetRolesAsync(user);

                    // Xóa tất cả vai trò hiện tại
                    await _userManager.RemoveFromRolesAsync(user, userRoles);

                    // Thêm vai trò mới
                    if (model.SelectedRoles != null && model.SelectedRoles.Any())
                    {
                        await _userManager.AddToRolesAsync(user, model.SelectedRoles);
                    }

                    // Cập nhật mật khẩu nếu có
                    if (!string.IsNullOrEmpty(model.Password))
                    {
                        // Xóa mật khẩu hiện tại
                        await _userManager.RemovePasswordAsync(user);

                        // Thêm mật khẩu mới
                        var passwordResult = await _userManager.AddPasswordAsync(user, model.Password);

                        if (!passwordResult.Succeeded)
                        {
                            foreach (var error in passwordResult.Errors)
                            {
                                ModelState.AddModelError("", error.Description);
                            }

                            // Lấy lại danh sách vai trò nếu có lỗi
                            var allRoles = await _roleManager.Roles.ToListAsync();
                            ViewBag.Roles = allRoles;

                            return View(model);
                        }
                    }

                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError("", error.Description);
                }
            }

            // Lấy lại danh sách vai trò nếu có lỗi
            var roles = await _roleManager.Roles.ToListAsync();
            ViewBag.Roles = roles;

            return View(model);
        }

        // GET: Admin/User/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Lấy danh sách vai trò của người dùng
            var roles = await _userManager.GetRolesAsync(user);

            var viewModel = new UserViewModel
            {
                User = user,
                Roles = roles.ToList()
            };

            return View(viewModel);
        }

        // POST: Admin/User/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Kiểm tra xem người dùng có phải là admin duy nhất không
            var adminUsers = await _userManager.GetUsersInRoleAsync("Admin");
            if (adminUsers.Count == 1 && adminUsers.First().Id == id && await _userManager.IsInRoleAsync(user, "Admin"))
            {
                ModelState.AddModelError("", "Không thể xóa người dùng Admin duy nhất trong hệ thống.");

                // Lấy lại thông tin người dùng để hiển thị
                var roles = await _userManager.GetRolesAsync(user);

                var viewModel = new UserViewModel
                {
                    User = user,
                    Roles = roles.ToList()
                };

                return View(viewModel);
            }

            // Kiểm tra xem người dùng có đơn đặt phòng không
            var hasBookings = await _context.Bookings.AnyAsync(b => b.UserId == id);
            if (hasBookings)
            {
                ModelState.AddModelError("", "Không thể xóa người dùng này vì có đơn đặt phòng liên quan.");

                // Lấy lại thông tin người dùng để hiển thị
                var roles = await _userManager.GetRolesAsync(user);

                var viewModel = new UserViewModel
                {
                    User = user,
                    Roles = roles.ToList()
                };

                return View(viewModel);
            }

            // Xóa người dùng
            var result = await _userManager.DeleteAsync(user);
            if (result.Succeeded)
            {
                return RedirectToAction(nameof(Index));
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError("", error.Description);
            }

            // Lấy lại thông tin người dùng để hiển thị nếu có lỗi
            var userRoles = await _userManager.GetRolesAsync(user);

            var userViewModel = new UserViewModel
            {
                User = user,
                Roles = userRoles.ToList()
            };

            return View(userViewModel);
        }
    }

    // View Models
    public class UserViewModel
    {
        public ApplicationUser User { get; set; }
        public List<string> Roles { get; set; }
    }

    public class UserDetailsViewModel
    {
        public ApplicationUser User { get; set; }
        public List<string> Roles { get; set; }
        public List<Booking> Bookings { get; set; }
    }

    public class CreateUserViewModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        [Required]
        public string FullName { get; set; }

        public string Address { get; set; }

        [Phone]
        public string PhoneNumber { get; set; }

        public List<string> SelectedRoles { get; set; }
    }

    public class EditUserViewModel
    {
        public string Id { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [StringLength(100, MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        [Required]
        public string FullName { get; set; }

        public string Address { get; set; }

        [Phone]
        public string PhoneNumber { get; set; }

        public List<string> SelectedRoles { get; set; }
    }
}
