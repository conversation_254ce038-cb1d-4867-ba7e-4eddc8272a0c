﻿﻿using ViVu.Models;

namespace ViVu.Data
{
    public static class ServiceSeedData
    {
        public static void SeedServices(ApplicationDbContext context)
        {
            if (context.Services.Any())
            {
                return; // Đã có dữ liệu dịch vụ
            }

            // Lấy các địa điểm và thành phố có sẵn
            var benTreCity = context.Cities.FirstOrDefault(c => c.Name.Contains("Bến Tre"));
            var benTreLocations = context.Locations.Where(l => l.CityId == benTreCity.Id).ToList();

            if (benTreCity == null || !benTreLocations.Any())
            {
                return; // Không tìm thấy dữ liệu cần thiết
            }

            var coconutVillageLocation = benTreLocations.FirstOrDefault(l => l.Name.Contains("Làng dừa"));
            var cityLocation = benTreLocations.FirstOrDefault(l => l.Name.Contains("Trung tâm"));
            var riverLocation = benTreLocations.FirstOrDefault(l => l.Name.Contains("Sông"));

            if (coconutVillageLocation == null)
            {
                coconutVillageLocation = benTreLocations.First();
            }

            if (cityLocation == null)
            {
                cityLocation = benTreLocations.Skip(1).FirstOrDefault() ?? benTreLocations.First();
            }

            if (riverLocation == null)
            {
                riverLocation = benTreLocations.Skip(2).FirstOrDefault() ?? benTreLocations.First();
            }

            var services = new List<Service>
            {
                new Service
                {
                    Name = "Trải nghiệm làm kẹo dừa truyền thống",
                    Description = "Tham gia vào quá trình làm kẹo dừa truyền thống của Bến Tre, từ khâu chọn nguyên liệu đến thành phẩm cuối cùng.",
                    Details = "Kẹo dừa là một trong những đặc sản nổi tiếng của Bến Tre. Trong dịch vụ này, bạn sẽ được:\n\n" +
                             "- Tham quan cơ sở sản xuất kẹo dừa truyền thống\n" +
                             "- Tìm hiểu về quy trình làm kẹo dừa từ A-Z\n" +
                             "- Trực tiếp tham gia vào quá trình làm kẹo\n" +
                             "- Thưởng thức các loại kẹo dừa với nhiều hương vị khác nhau\n" +
                             "- Nhận một hộp kẹo dừa làm quà lưu niệm\n\n" +
                             "Đây là trải nghiệm thú vị và ý nghĩa cho cả gia đình, đặc biệt là trẻ em sẽ rất thích thú với quá trình làm kẹo.",
                    ImageUrl = "/images/services/coconut-candy-making.jpg",
                    Price = 150000, // 150.000 VNĐ
                    Duration = 90, // 90 phút
                    IsFeatured = true,
                    LocationId = coconutVillageLocation.Id,
                    CityId = benTreCity.Id
                },
                new Service
                {
                    Name = "Chèo thuyền trên sông Hàm Luông",
                    Description = "Trải nghiệm chèo thuyền trên dòng sông Hàm Luông thơ mộng, khám phá vẻ đẹp của miền sông nước Bến Tre.",
                    Details = "Sông Hàm Luông là một trong những nhánh chính của sông Mekong chảy qua Bến Tre. Dịch vụ chèo thuyền của chúng tôi bao gồm:\n\n" +
                             "- Hướng dẫn cách chèo thuyền an toàn\n" +
                             "- Thuyền kayak chất lượng cao cho 1-2 người\n" +
                             "- Áo phao và thiết bị an toàn\n" +
                             "- Hướng dẫn viên đi cùng\n" +
                             "- Nước uống và khăn lạnh\n\n" +
                             "Bạn sẽ được chèo thuyền dọc theo những con rạch nhỏ, ngắm nhìn cuộc sống của người dân địa phương và khám phá hệ sinh thái đa dạng của vùng sông nước.",
                    ImageUrl = "/images/services/kayaking.jpg",
                    Price = 200000, // 200.000 VNĐ
                    Duration = 120, // 120 phút
                    IsFeatured = true,
                    LocationId = riverLocation.Id,
                    CityId = benTreCity.Id
                },
                new Service
                {
                    Name = "Học làm món ăn đặc sản Bến Tre",
                    Description = "Khám phá nghệ thuật ẩm thực Bến Tre thông qua lớp học nấu ăn với các đầu bếp địa phương.",
                    Details = "Ẩm thực Bến Tre nổi tiếng với những món ăn đặc sắc từ dừa và hải sản. Trong lớp học nấu ăn này, bạn sẽ:\n\n" +
                             "- Học cách chế biến 3-4 món ăn đặc trưng của Bến Tre\n" +
                             "- Được hướng dẫn bởi đầu bếp chuyên nghiệp\n" +
                             "- Tham gia vào toàn bộ quá trình từ sơ chế đến thành phẩm\n" +
                             "- Thưởng thức thành quả của mình\n" +
                             "- Nhận công thức để có thể tự làm tại nhà\n\n" +
                             "Các món ăn tiêu biểu: Cá kho tộ kiểu Bến Tre, gỏi củ hủ dừa, canh chua cá lóc, bánh tét lá dứa...",
                    ImageUrl = "/images/services/cooking-class.jpg",
                    Price = 350000, // 350.000 VNĐ
                    Duration = 180, // 180 phút
                    IsFeatured = false,
                    LocationId = cityLocation.Id,
                    CityId = benTreCity.Id
                },
                new Service
                {
                    Name = "Spa dừa - Liệu pháp thư giãn",
                    Description = "Thư giãn với liệu pháp spa từ dừa, sản phẩm đặc trưng của Bến Tre, giúp làn da mịn màng và tinh thần sảng khoái.",
                    Details = "Spa dừa là liệu pháp thư giãn độc đáo chỉ có tại Bến Tre, sử dụng các sản phẩm từ dừa để chăm sóc cơ thể. Dịch vụ bao gồm:\n\n" +
                             "- Tẩy tế bào chết toàn thân bằng cơm dừa\n" +
                             "- Mát-xa thư giãn với dầu dừa nguyên chất\n" +
                             "- Đắp mặt nạ dừa dưỡng da\n" +
                             "- Tắm sữa dừa\n" +
                             "- Thưởng thức nước dừa tươi sau liệu trình\n\n" +
                             "Liệu pháp này không chỉ giúp làn da mịn màng, trắng sáng mà còn mang lại cảm giác thư giãn tuyệt đối cho cơ thể và tinh thần.",
                    ImageUrl = "/images/services/coconut-spa.jpg",
                    Price = 450000, // 450.000 VNĐ
                    Duration = 120, // 120 phút
                    IsFeatured = true,
                    LocationId = cityLocation.Id,
                    CityId = benTreCity.Id
                },
                new Service
                {
                    Name = "Tham quan vườn trái cây theo mùa",
                    Description = "Khám phá vườn trái cây theo mùa của Bến Tre, thưởng thức trái cây tươi ngon ngay tại vườn.",
                    Details = "Bến Tre nổi tiếng với nhiều loại trái cây nhiệt đới thơm ngon. Dịch vụ tham quan vườn trái cây bao gồm:\n\n" +
                             "- Tham quan vườn trái cây đa dạng (sầu riêng, chôm chôm, măng cụt, vú sữa... tùy theo mùa)\n" +
                             "- Tìm hiểu về quy trình trồng và chăm sóc cây ăn trái\n" +
                             "- Trực tiếp hái và thưởng thức trái cây tại vườn\n" +
                             "- Thưởng thức bữa trưa nhẹ với các món ăn từ trái cây\n" +
                             "- Mua trái cây tươi với giá ưu đãi\n\n" +
                             "Đây là hoạt động lý tưởng cho gia đình và những người yêu thích trái cây tự nhiên.",
                    ImageUrl = "/images/services/fruit-garden.jpg",
                    Price = 200000, // 200.000 VNĐ
                    Duration = 150, // 150 phút
                    IsFeatured = false,
                    LocationId = coconutVillageLocation.Id,
                    CityId = benTreCity.Id
                }
            };

            context.Services.AddRange(services);
            context.SaveChanges();
        }
    }
}
