@model ViVu.Models.ViewModels.TourReviewViewModel

@{
    ViewData["Title"] = "Đánh giá tour";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">@ViewData["Title"] - @Model.Tour.Name</h5>
                </div>
                <div class="card-body p-4">
                    <div class="mb-4">
                        <div class="d-flex align-items-center">
                            @if (!string.IsNullOrEmpty(Model.Tour.ImageUrl))
                            {
                                <img src="@Model.Tour.ImageUrl" alt="@Model.Tour.Name" class="me-3 rounded" style="width: 100px; height: 100px; object-fit: cover;">
                            }
                            <div>
                                <h4 class="mb-1">@Model.Tour.Name</h4>
                                <p class="mb-1">
                                    <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                                    @(Model.Tour.Location != null ? Model.Tour.Location.Name : ""), @(Model.Tour.City != null ? Model.Tour.City.Name : "")
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-clock me-1 text-primary"></i>
                                    @Model.Tour.Duration ngày
                                </p>
                            </div>
                        </div>
                    </div>

                    <form asp-action="SubmitReview" method="post">
                        <input type="hidden" asp-for="TourId" value="@Model.Tour.Id" />

                        <div class="mb-4">
                            <label class="form-label">Đánh giá của bạn</label>
                            <div class="rating">
                                <input type="radio" id="star5" name="Rating" value="5" /><label for="star5"></label>
                                <input type="radio" id="star4" name="Rating" value="4" /><label for="star4"></label>
                                <input type="radio" id="star3" name="Rating" value="3" /><label for="star3"></label>
                                <input type="radio" id="star2" name="Rating" value="2" /><label for="star2"></label>
                                <input type="radio" id="star1" name="Rating" value="1" /><label for="star1"></label>
                            </div>
                            <span asp-validation-for="Rating" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Comment" class="form-label">Nhận xét của bạn</label>
                            <textarea asp-for="Comment" class="form-control" rows="5" placeholder="Chia sẻ trải nghiệm của bạn về tour này..." required></textarea>
                            <span asp-validation-for="Comment" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-controller="Tour" asp-action="MyBookings" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Gửi đánh giá
                            </button>
                        </div>
                    </form>

                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <hr class="my-4">

                        <h5 class="mb-3">Đánh giá khác (@(Model.Reviews.Count()))</h5>

                        @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt))
                        {
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>@review.User.FullName</strong>
                                        </div>
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="@(i <= review.Rating ? "fas" : "far") fa-star"></i>
                                            }
                                        </div>
                                    </div>
                                    <div class="text-muted small mb-2">@review.CreatedAt.ToString("dd/MM/yyyy")</div>
                                    <div>@review.Comment</div>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }

        .rating:not(:checked) > input {
            position: absolute;
            clip: rect(0,0,0,0);
        }

        .rating:not(:checked) > label {
            float: right;
            width: 1em;
            padding: 0 .1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 2rem;
            line-height: 1.2;
            color: #ddd;
        }

        .rating:not(:checked) > label:before {
            content: '★ ';
        }

        .rating > input:checked ~ label {
            color: #ffb700;
        }

        .rating:not(:checked) > label:hover,
        .rating:not(:checked) > label:hover ~ label {
            color: #ffb700;
        }

        .rating > input:checked + label:hover,
        .rating > input:checked + label:hover ~ label,
        .rating > input:checked ~ label:hover,
        .rating > input:checked ~ label:hover ~ label,
        .rating > label:hover ~ input:checked ~ label {
            color: #ffb700;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
