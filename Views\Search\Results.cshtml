@model IEnumerable<ViVu.Models.Accommodation>

@{
    ViewData["Title"] = "Kết quả tìm kiếm khách sạn";
}

<div class="container-fluid px-3 px-md-5 py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Accommodation" asp-action="Index">Khách sạn</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Kết quả tìm kiếm</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title mb-3">Bộ lọc tìm kiếm</h4>
                    <form asp-controller="Search" asp-action="Search" method="post">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Địa điểm</label>
                                <select name="locationId" class="form-select" asp-items="ViewBag.Locations">
                                    <option value="">-- Tất cả địa điểm --</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Ngày nhận phòng</label>
                                <input type="date" name="checkIn" class="form-control" value="@(ViewBag.CheckIn?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(1).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Ngày trả phòng</label>
                                <input type="date" name="checkOut" class="form-control" value="@(ViewBag.CheckOut?.ToString("yyyy-MM-dd") ?? DateTime.Today.AddDays(2).ToString("yyyy-MM-dd"))">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Xếp hạng sao</label>
                                <select name="starRating" class="form-select">
                                    <option value="">-- Tất cả --</option>
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        if (ViewBag.StarRating == i)
                                        {
                                            <option value="@i" selected>@i sao</option>
                                        }
                                        else
                                        {
                                            <option value="@i">@i sao</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i> Lọc kết quả
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">Kết quả tìm kiếm khách sạn</h2>
            <p>Tìm thấy @Model.Count() khách sạn phù hợp với tiêu chí của bạn</p>
        </div>
    </div>

    <div class="row">
        @if (Model.Any())
        {
            foreach (var accommodation in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm hover-lift">
                        <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default/default-hotel.jpg" : accommodation.ImageUrl)"
                             class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title mb-0">@accommodation.Name</h5>
                                <div>
                                    @for (int i = 0; i < accommodation.StarRating; i++)
                                    {
                                        <i class="bi bi-star-fill text-warning"></i>
                                    }
                                </div>
                            </div>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @accommodation.Location?.Name, @accommodation.City?.Name
                            </p>
                            <p class="card-text">@(accommodation.Description?.Length > 100 ? accommodation.Description.Substring(0, 100) + "..." : accommodation.Description)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-outline-primary">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-5">
                <div class="alert alert-info">
                    <h4 class="alert-heading">Không tìm thấy khách sạn nào!</h4>
                    <p>Không có khách sạn nào phù hợp với tiêu chí tìm kiếm của bạn. Vui lòng thử lại với các tiêu chí khác.</p>
                </div>
                <a asp-controller="Accommodation" asp-action="Index" class="btn btn-primary mt-3">Xem tất cả khách sạn</a>
            </div>
        }
    </div>
</div>
