@model ViVu.Models.ServiceBooking
@{
    ViewData["Title"] = "Chi tiết đặt dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết đặt dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Đặt dịch vụ</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Thông tin chi tiết đặt dịch vụ #@Model.Id
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Thông tin khách hàng</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Họ tên:</th>
                            <td>@Model.ApplicationUser.FullName</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>@Model.ApplicationUser.Email</td>
                        </tr>
                        <tr>
                            <th>Địa chỉ:</th>
                            <td>@(Model.ApplicationUser.Address ?? "Không có")</td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-md-6">
                    <h5>Thông tin đặt dịch vụ</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Mã đặt dịch vụ:</th>
                            <td>#@Model.Id</td>
                        </tr>
                        <tr>
                            <th>Ngày đặt:</th>
                            <td>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                        </tr>
                        <tr>
                            <th>Ngày sử dụng:</th>
                            <td>@Model.ServiceDate.ToString("dd/MM/yyyy")</td>
                        </tr>
                        <tr>
                            <th>Trạng thái:</th>
                            <td>
                                @switch (Model.Status)
                                {
                                    case ServiceBookingStatus.Pending:
                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                        break;
                                    case ServiceBookingStatus.Confirmed:
                                        <span class="badge bg-primary">Đã xác nhận</span>
                                        break;
                                    case ServiceBookingStatus.Completed:
                                        <span class="badge bg-success">Hoàn thành</span>
                                        break;
                                    case ServiceBookingStatus.Cancelled:
                                        <span class="badge bg-danger">Đã hủy</span>
                                        break;
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Tổng tiền:</th>
                            <td>@Model.TotalPrice.ToString("N0") VNĐ</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <h5>Chi tiết dịch vụ đã đặt</h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Dịch vụ</th>
                            <th>Địa điểm</th>
                            <th>Số lượng</th>
                            <th>Đơn giá</th>
                            <th>Thành tiền</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var detail in Model.ServiceBookingDetails)
                        {
                            <tr>
                                <td>@detail.Service.Name</td>
                                <td>@detail.Service.Location?.Name, @detail.Service.City?.Name</td>
                                <td>@detail.Quantity</td>
                                <td>@detail.Price.ToString("N0") VNĐ</td>
                                <td>@detail.TotalPrice.ToString("N0") VNĐ</td>
                                <td>
                                    @switch (detail.Status)
                                    {
                                        case ServiceBookingDetailStatus.Pending:
                                            <span class="badge bg-warning">Chờ xác nhận</span>
                                            break;
                                        case ServiceBookingDetailStatus.Confirmed:
                                            <span class="badge bg-primary">Đã xác nhận</span>
                                            break;
                                        case ServiceBookingDetailStatus.Completed:
                                            <span class="badge bg-success">Hoàn thành</span>
                                            break;
                                        case ServiceBookingDetailStatus.Cancelled:
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">Tổng cộng:</th>
                            <th>@Model.TotalPrice.ToString("N0") VNĐ</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            @if (!string.IsNullOrEmpty(Model.SpecialRequests))
            {
                <div class="mt-3">
                    <h5>Yêu cầu đặc biệt</h5>
                    <div class="p-3 bg-light rounded">
                        @Model.SpecialRequests
                    </div>
                </div>
            }
            
            <div class="mt-4">
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Cập nhật trạng thái
                </a>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
    </div>
</div>
