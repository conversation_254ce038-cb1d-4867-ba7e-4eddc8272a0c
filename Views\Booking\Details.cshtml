@model ViVu.Models.Booking

@{
    ViewData["Title"] = "Chi tiết đặt phòng";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Chi tiết đặt phòng</h1>
        <a asp-action="MyBookings" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-2"></i>Quay lại
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Mã đặt phòng: #@Model.Id</h5>
            <span>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</span>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Thông tin đặt phòng</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày đặt phòng:</strong></p>
                                    <p>@Model.BookingDate.ToString("dd/MM/yyyy HH:mm")</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Trạng thái:</strong></p>
                                    <p>
                                        @switch (Model.Status)
                                        {
                                            case BookingStatus.Pending:
                                                <span class="badge bg-warning">Chờ xác nhận</span>
                                                break;
                                            case BookingStatus.Confirmed:
                                                <span class="badge bg-success">Đã xác nhận</span>
                                                break;
                                            case BookingStatus.Cancelled:
                                                <span class="badge bg-danger">Đã hủy</span>
                                                break;
                                            case BookingStatus.Completed:
                                                <span class="badge bg-info">Đã hoàn thành</span>
                                                break;
                                            default:
                                                <span class="badge bg-secondary">@Model.Status</span>
                                                break;
                                        }
                                    </p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày nhận phòng:</strong></p>
                                    <p>@Model.CheckInDate.ToString("dd/MM/yyyy")</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Ngày trả phòng:</strong></p>
                                    <p>@Model.CheckOutDate.ToString("dd/MM/yyyy")</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Số đêm:</strong></p>
                                    <p>@((Model.CheckOutDate - Model.CheckInDate).Days) đêm</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Tổng tiền:</strong></p>
                                    <p class="text-success fw-bold">@Model.TotalPrice.ToString("#,##0") VNĐ</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Chi tiết phòng</h5>
                        </div>
                        <div class="card-body">
                            @foreach (var detail in Model.BookingDetails)
                            {
                                <div class="mb-3 pb-3 @(detail != Model.BookingDetails.Last() ? "border-bottom" : "")">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">@detail.Room.Name</h6>
                                        <span class="badge bg-primary">@detail.NumberOfRooms phòng</span>
                                    </div>
                                    <p class="text-muted mb-2">@detail.Room.Accommodation.Name</p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="d-block mb-1"><strong>Số khách:</strong> @detail.NumberOfGuests người</small>
                                            <small class="d-block mb-1"><strong>Giá/đêm:</strong> @detail.PricePerNight.ToString("#,##0") VNĐ</small>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <small class="d-block mb-1"><strong>Số đêm:</strong> @((Model.CheckOutDate - Model.CheckInDate).Days)</small>
                                            <small class="d-block mb-1"><strong>Thành tiền:</strong> @((detail.PricePerNight * detail.NumberOfRooms * (Model.CheckOutDate - Model.CheckInDate).Days).ToString("#,##0")) VNĐ</small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.SpecialRequests))
                    {
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Yêu cầu đặc biệt</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">@Model.SpecialRequests</p>
                            </div>
                        </div>
                    }
                </div>

                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Thông tin thanh toán</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phương thức:</span>
                                <span>Thanh toán tại khách sạn</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Trạng thái:</span>
                                @switch (Model.Status)
                                {
                                    case BookingStatus.Pending:
                                        <span class="badge bg-warning">Chưa thanh toán</span>
                                        break;
                                    case BookingStatus.Confirmed:
                                        <span class="badge bg-warning">Chưa thanh toán</span>
                                        break;
                                    case BookingStatus.Cancelled:
                                        <span class="badge bg-danger">Đã hủy</span>
                                        break;
                                    case BookingStatus.Completed:
                                        <span class="badge bg-success">Đã thanh toán</span>
                                        break;
                                    default:
                                        <span class="badge bg-secondary">@Model.Status</span>
                                        break;
                                }
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tổng tiền:</span>
                                <span class="fw-bold">@Model.TotalPrice.ToString("#,##0") VNĐ</span>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Thông tin liên hệ</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><strong>Khách sạn:</strong></p>
                            <p class="mb-3">@Model.BookingDetails.First().Room.Accommodation.Name</p>
                            
                            <p class="mb-2"><strong>Địa chỉ:</strong></p>
                            <p class="mb-3">@Model.BookingDetails.First().Room.Accommodation.Address</p>
                            
                            <p class="mb-2"><strong>Điện thoại:</strong></p>
                            <p class="mb-3">0123 456 789</p>
                            
                            <p class="mb-2"><strong>Email:</strong></p>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Thao tác</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="bi bi-printer me-2"></i>In xác nhận
                                </a>
                                
                                @if (Model.Status == BookingStatus.Pending || Model.Status == BookingStatus.Confirmed)
                                {
                                    <form asp-action="Cancel" method="post" onsubmit="return confirm('Bạn có chắc chắn muốn hủy đặt phòng này?');">
                                        <input type="hidden" name="id" value="@Model.Id" />
                                        <button type="submit" class="btn btn-outline-danger w-100">
                                            <i class="bi bi-x-circle me-2"></i>Hủy đặt phòng
                                        </button>
                                    </form>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
