﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class ComboController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly IAccommodationRepository _accommodationRepository;
        private readonly ITourRepository _tourRepository;

        public ComboController(
            ApplicationDbContext context,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            IAccommodationRepository accommodationRepository,
            ITourRepository tourRepository)
        {
            _context = context;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _accommodationRepository = accommodationRepository;
            _tourRepository = tourRepository;
        }

        // GET: Combo
        public async Task<IActionResult> Index()
        {
            // Trong tương lai, đây sẽ là danh sách các combo
            // Hiện tại, chúng ta sẽ hiển thị cả tour và khách sạn
            var viewModel = new ComboViewModel
            {
                Tours = await _tourRepository.GetFeaturedToursAsync(3),
                Accommodations = await _accommodationRepository.GetFeaturedAsync()
            };

            return View(viewModel);
        }

        // POST: Combo/Search
        [HttpPost]
        public async Task<IActionResult> Search(int? locationId, DateTime? startDate, DateTime? endDate, int? groupSize)
        {
            // Kiểm tra ngày bắt đầu và kết thúc
            if (!startDate.HasValue)
            {
                startDate = DateTime.Today.AddDays(1);
            }

            if (!endDate.HasValue)
            {
                endDate = startDate.Value.AddDays(2);
            }

            // Tìm kiếm tour
            var tourQuery = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                tourQuery = tourQuery.Where(t => t.LocationId == locationId.Value);
            }

            var tours = await tourQuery.ToListAsync();

            // Tìm kiếm khách sạn
            var accommodationQuery = _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Rooms)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                accommodationQuery = accommodationQuery.Where(a => a.LocationId == locationId.Value);
            }

            var accommodations = await accommodationQuery.ToListAsync();

            // Tạo view model
            var viewModel = new ComboViewModel
            {
                Tours = tours,
                Accommodations = accommodations
            };

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.LocationId = locationId;
            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.GroupSize = groupSize;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View("Results", viewModel);
        }

        // GET: Combo/Results
        public async Task<IActionResult> Results(int? locationId, DateTime? startDate, DateTime? endDate, int? groupSize)
        {
            // Kiểm tra ngày bắt đầu và kết thúc
            if (!startDate.HasValue)
            {
                startDate = DateTime.Today.AddDays(1);
            }

            if (!endDate.HasValue)
            {
                endDate = startDate.Value.AddDays(2);
            }

            // Tìm kiếm tour
            var tourQuery = _context.Tours
                .Include(t => t.Location)
                .Include(t => t.City)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                tourQuery = tourQuery.Where(t => t.LocationId == locationId.Value);
            }

            var tours = await tourQuery.ToListAsync();

            // Tìm kiếm khách sạn
            var accommodationQuery = _context.Accommodations
                .Include(a => a.Location)
                .Include(a => a.City)
                .Include(a => a.Rooms)
                .AsQueryable();

            // Lọc theo địa điểm
            if (locationId.HasValue)
            {
                accommodationQuery = accommodationQuery.Where(a => a.LocationId == locationId.Value);
            }

            var accommodations = await accommodationQuery.ToListAsync();

            // Tạo view model
            var viewModel = new ComboViewModel
            {
                Tours = tours,
                Accommodations = accommodations
            };

            // Lưu các tham số tìm kiếm vào ViewBag để hiển thị lại trên trang kết quả
            ViewBag.LocationId = locationId;
            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.GroupSize = groupSize;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            return View(viewModel);
        }
    }
}
