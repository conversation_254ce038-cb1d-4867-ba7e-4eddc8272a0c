﻿@model ViVu.Models.Product
@using Microsoft.AspNetCore.Mvc.Rendering

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="text-center my-4">Add Product</h2>
<div class="container">
    <form asp-action="Add" method="post" enctype="multipart/form-data" class="border p-4 rounded shadow bg-light">
        <div class="mb-3">
            <label asp-for="Name" class="form-label fw-bold">Name</label>
            <input asp-for="Name" class="form-control" required />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Price" class="form-label fw-bold">Price</label>
            <input asp-for="Price" type="number" step="0.01" class="form-control" required />
            <span asp-validation-for="Price" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Description" class="form-label fw-bold">Description</label>
            <textarea asp-for="Description" class="form-control" rows="3" required></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="CategoryId" class="form-label fw-bold">Category</label>
            <select asp-for="CategoryId" asp-items="@(ViewBag.Categories)" class="form-select" required>
                <option value="">-- Select Category --</option> 
            </select>
            <span asp-validation-for="CategoryId" class="text-danger"></span>
        </div>


        <div class="mb-3">
            <label class="form-label fw-bold">Product Image</label>
            <input type="file" name="ImageUrl" class="form-control" accept="image/*" />
        </div>

        <div class="text-center">
            <button type="submit" class="btn btn-primary px-4">Add</button>
            <a asp-action="Index" class="btn btn-secondary px-4 ms-2">Cancel</a>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
