@model IEnumerable<ViVu.Models.ServiceBooking>
@{
    ViewData["Title"] = "Quản lý đặt dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý đặt dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Đặt dịch vụ</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-calendar-check me-1"></i>
            Danh sách đặt dịch vụ
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th><PERSON>h<PERSON><PERSON> hàng</th>
                        <th><PERSON><PERSON><PERSON> đặt</th>
                        <th><PERSON><PERSON><PERSON> sử dụng</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Id</td>
                            <td>@item.ApplicationUser.FullName</td>
                            <td>@item.BookingDate.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>@item.ServiceDate.ToString("dd/MM/yyyy")</td>
                            <td>@item.TotalPrice.ToString("N0") VNĐ</td>
                            <td>
                                @switch (item.Status)
                                {
                                    case ServiceBookingStatus.Pending:
                                        <span class="badge bg-warning">Chờ xác nhận</span>
                                        break;
                                    case ServiceBookingStatus.Confirmed:
                                        <span class="badge bg-primary">Đã xác nhận</span>
                                        break;
                                    case ServiceBookingStatus.Completed:
                                        <span class="badge bg-success">Hoàn thành</span>
                                        break;
                                    case ServiceBookingStatus.Cancelled:
                                        <span class="badge bg-danger">Đã hủy</span>
                                        break;
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#datatablesSimple').DataTable();
        });
    </script>
}
