﻿﻿using System.ComponentModel.DataAnnotations;

namespace ViVu.Models.ViewModels
{
    public class ServiceBookingViewModel
    {
        public int ServiceId { get; set; }
        
        public Service Service { get; set; }
        
        [Required(ErrorMessage = "<PERSON>ui lòng chọn ngày sử dụng dịch vụ")]
        [DataType(DataType.Date)]
        [Display(Name = "Ngày sử dụng")]
        public DateTime ServiceDate { get; set; }
        
        [Required(ErrorMessage = "Vui lòng nhập số lượng")]
        [Range(1, 10, ErrorMessage = "Số lượng phải từ 1 đến 10")]
        [Display(Name = "Số lượng")]
        public int Quantity { get; set; } = 1;
        
        [Display(Name = "Yêu cầu đặc biệt")]
        [StringLength(500, ErrorMessage = "Yêu cầu đặc biệt không được vượt quá 500 ký tự")]
        public string? SpecialRequests { get; set; }
        
        [Display(Name = "Tổng tiền")]
        public decimal TotalPrice => Service != null ? Service.Price * Quantity : 0;
    }
}
