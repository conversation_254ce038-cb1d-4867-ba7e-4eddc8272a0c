﻿﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class ServiceCart
    {
        public List<ServiceCartItem> Items { get; set; } = new List<ServiceCartItem>();
        
        public void AddItem(ServiceCartItem item)
        {
            var existingItem = Items.FirstOrDefault(i => i.ServiceId == item.ServiceId &&
                                                        i.ServiceDate == item.ServiceDate);
            if (existingItem != null)
            {
                existingItem.Quantity += item.Quantity;
            }
            else
            {
                Items.Add(item);
            }
        }
        
        public void RemoveItem(int serviceId)
        {
            Items.RemoveAll(i => i.ServiceId == serviceId);
        }
        
        public void Clear()
        {
            Items.Clear();
        }
        
        [NotMapped]
        public decimal TotalPrice => Items.Sum(item => item.TotalPrice);
        
        [NotMapped]
        public int TotalItems => Items.Count;
    }
}
