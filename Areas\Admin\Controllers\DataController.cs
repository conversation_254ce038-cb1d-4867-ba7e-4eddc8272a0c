﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ViVu.Data;
using ViVu.Models;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class DataController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DataController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/Data/SeedLocations
        public IActionResult SeedLocations()
        {
            try
            {
                // Gọi phương thức seed dữ liệu địa điểm
                LocationSeedData.SeedLocations(_context);

                // Cập nhật hình ảnh cho các địa điểm
                LocationImageSeedData.UpdateLocationImages(_context);

                TempData["SuccessMessage"] = "Đã cập nhật và thêm dữ liệu địa điểm thành công!";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Lỗi khi cập nhật dữ liệu địa điểm: {ex.Message}";
            }

            return RedirectToAction("Index", "Location");
        }

        // GET: Admin/Data/UpdateLocationImages
        public IActionResult UpdateLocationImages()
        {
            try
            {
                // Cập nhật hình ảnh cho các địa điểm
                LocationImageSeedData.UpdateLocationImages(_context);

                TempData["SuccessMessage"] = "Đã cập nhật hình ảnh cho các địa điểm thành công!";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Lỗi khi cập nhật hình ảnh: {ex.Message}";
            }

            return RedirectToAction("Index", "Location");
        }
    }
}
