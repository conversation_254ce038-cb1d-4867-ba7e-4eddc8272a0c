@{
    ViewData["Title"] = "Tính năng AI";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="Recommendation" asp-action="Index">AI Gợi Ý Lịch Trình</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Tính năng AI</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary text-white p-4 rounded-3">
                <h1 class="display-5 fw-bold">Tính năng AI cho Du lịch Bến Tre</h1>
                <p class="fs-5">Khám phá các tính năng AI thông minh giúp lên kế hoạch du lịch Bến Tre dễ dàng hơn</p>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-route fa-4x text-primary"></i>
                    </div>
                    <h3 class="card-title">Gợi ý Lịch trình Cá nhân hóa</h3>
                    <p class="card-text">AI phân tích sở thích và lịch sử du lịch của bạn để tạo ra lịch trình du lịch phù hợp nhất.</p>
                    <a asp-action="Index" class="btn btn-primary mt-2">Tạo lịch trình ngay</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-lightbulb fa-4x text-warning"></i>
                    </div>
                    <h3 class="card-title">Thông tin Du lịch Thông minh</h3>
                    <p class="card-text">Nhận thông tin chi tiết về các địa điểm du lịch, văn hóa, lịch sử và đặc sản của Bến Tre.</p>
                    <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#insightModal">
                        Khám phá ngay
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-magic fa-4x text-success"></i>
                    </div>
                    <h3 class="card-title">Tạo Tour Tùy chỉnh</h3>
                    <p class="card-text">Tạo tour du lịch tùy chỉnh dựa trên sở thích, thời gian và ngân sách của bạn.</p>
                    <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#customTourModal">
                        Tạo tour ngay
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h2 class="mb-0">Cách AI giúp bạn lên kế hoạch du lịch tốt hơn</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4><i class="fas fa-fingerprint text-primary me-2"></i>Cá nhân hóa</h4>
                            <p>AI phân tích sở thích, lịch sử du lịch và hành vi tìm kiếm của bạn để tạo ra gợi ý phù hợp nhất với nhu cầu cá nhân.</p>

                            <h4><i class="fas fa-clock text-primary me-2"></i>Tiết kiệm thời gian</h4>
                            <p>Không cần dành hàng giờ để nghiên cứu, AI sẽ tạo ra lịch trình chi tiết chỉ trong vài giây.</p>

                            <h4><i class="fas fa-lightbulb text-primary me-2"></i>Khám phá mới</h4>
                            <p>AI có thể gợi ý những địa điểm ít người biết đến hoặc trải nghiệm độc đáo mà bạn có thể bỏ lỡ khi tự tìm kiếm.</p>
                        </div>

                        <div class="col-md-6">
                            <h4><i class="fas fa-balance-scale text-primary me-2"></i>Cân đối ngân sách</h4>
                            <p>AI giúp tối ưu hóa chi phí du lịch bằng cách đề xuất các lựa chọn phù hợp với ngân sách của bạn.</p>

                            <h4><i class="fas fa-puzzle-piece text-primary me-2"></i>Kết hợp thông minh</h4>
                            <p>AI phân tích vị trí địa lý, thời gian di chuyển và giờ mở cửa để tạo ra lịch trình hợp lý và hiệu quả.</p>

                            <h4><i class="fas fa-sync text-primary me-2"></i>Cập nhật liên tục</h4>
                            <p>AI liên tục học hỏi từ phản hồi của người dùng và dữ liệu mới để cải thiện chất lượng gợi ý.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h2 class="mb-0">Câu hỏi thường gặp</h2>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    AI sử dụng dữ liệu gì để tạo gợi ý?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    AI sử dụng dữ liệu từ sở thích bạn cung cấp, lịch sử tìm kiếm và đặt phòng trước đây (nếu có), cùng với cơ sở dữ liệu về các địa điểm, khách sạn, tour và dịch vụ tại Bến Tre. Tất cả dữ liệu cá nhân được xử lý an toàn và bảo mật.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Gợi ý của AI có chính xác không?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Gợi ý của AI dựa trên dữ liệu hiện có và được thiết kế để phù hợp nhất với sở thích của bạn. Tuy nhiên, chúng chỉ mang tính tham khảo và có thể không phản ánh đầy đủ tất cả các điều kiện thực tế. Bạn nên xem xét các gợi ý này như một điểm khởi đầu tốt cho kế hoạch du lịch của mình.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Tôi có thể tùy chỉnh gợi ý của AI không?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Có, bạn có thể tùy chỉnh gợi ý bằng cách cung cấp thêm thông tin chi tiết về sở thích, thời gian và ngân sách của mình. Bạn cũng có thể lưu gợi ý và chỉnh sửa sau. Trong trang cài đặt AI, bạn có thể nhập yêu cầu cụ thể để AI tạo gợi ý phù hợp hơn với nhu cầu của bạn.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Travel Insights -->
<div class="modal fade" id="insightModal" tabindex="-1" aria-labelledby="insightModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="insightModalLabel">Thông tin Du lịch Thông minh</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="insightForm">
                    <div class="mb-3">
                        <label for="destination" class="form-label">Địa điểm bạn muốn tìm hiểu</label>
                        <input type="text" class="form-control" id="destination" placeholder="Ví dụ: Cồn Phụng, Chợ nổi Cái Răng, Làng dừa Bến Tre...">
                    </div>
                    <div class="mb-3">
                        <label for="interests" class="form-label">Bạn quan tâm đến điều gì?</label>
                        <input type="text" class="form-control" id="interests" placeholder="Ví dụ: lịch sử, ẩm thực, văn hóa, hoạt động giải trí...">
                    </div>
                    <button type="submit" class="btn btn-primary">Tìm kiếm thông tin</button>
                </form>

                <div id="insightResult" class="mt-4 d-none">
                    <div class="d-flex justify-content-center mb-3" id="insightLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title" id="insightTitle"></h5>
                            <div class="markdown-content" id="insightContent"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Custom Tour -->
<div class="modal fade" id="customTourModal" tabindex="-1" aria-labelledby="customTourModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="customTourModalLabel">Tạo Tour Tùy chỉnh</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customTourForm">
                    <div class="mb-3">
                        <label for="tourPreferences" class="form-label">Sở thích và mong muốn của bạn</label>
                        <textarea class="form-control" id="tourPreferences" rows="3" placeholder="Ví dụ: Tôi thích khám phá ẩm thực địa phương, tham quan làng nghề truyền thống, và thích các hoạt động ngoài trời..."></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tourDuration" class="form-label">Thời gian (ngày)</label>
                                <input type="text" class="form-control" id="tourDuration" placeholder="Ví dụ: 2-3 ngày">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tourBudget" class="form-label">Ngân sách</label>
                                <input type="text" class="form-control" id="tourBudget" placeholder="Ví dụ: 2-3 triệu VNĐ">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Tạo tour</button>
                </form>

                <div id="customTourResult" class="mt-4 d-none">
                    <div class="d-flex justify-content-center mb-3" id="customTourLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Tour Tùy chỉnh của bạn</h5>
                            <div class="markdown-content" id="customTourContent"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle Travel Insights form submission
            $('#insightForm').on('submit', function(e) {
                e.preventDefault();

                var destination = $('#destination').val();
                var interests = $('#interests').val();

                if (!destination) {
                    alert('Vui lòng nhập địa điểm bạn muốn tìm hiểu');
                    return;
                }

                // Show loading and result container
                $('#insightResult').removeClass('d-none');
                $('#insightLoading').show();
                $('#insightContent').html('');
                $('#insightTitle').text('Thông tin về ' + destination);

                // Call API to get insights
                console.log("Sending request to GenerateAIInsight with destination: " + destination + ", interests: " + interests);
                console.log("URL: " + '@Url.Action("GenerateAIInsight", "Recommendation")');

                // Add debug info to the page
                $('#insightContent').html('<div class="alert alert-info">Đang gửi yêu cầu đến máy chủ...</div>');

                // Log the full URL for debugging
                var url = '@Url.Action("GenerateAIInsight", "Recommendation")';
                console.log("Full URL: " + window.location.origin + url);

                // Create form data
                var formData = new FormData();
                formData.append('destination', destination);
                formData.append('interests', interests);

                console.log("Form data created:", {
                    destination: destination,
                    interests: interests
                });

                // Use jQuery AJAX instead of fetch
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: $.param({
                        destination: destination,
                        interests: interests
                    }),
                    contentType: 'application/x-www-form-urlencoded',
                    success: function(response) {
                        $('#insightLoading').hide();
                        console.log("Received response from GenerateAIInsight:", response);

                        if (response.success) {
                            // Convert markdown to HTML and display
                            console.log("Success response, insight:", response.insight);
                            $('#insightContent').html(marked.parse(response.insight));
                        } else {
                            console.log("Error response:", response.error);
                            $('#insightContent').html('<div class="alert alert-danger">Có lỗi xảy ra: ' + response.error + '</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#insightLoading').hide();
                        console.log("AJAX error:", status, error);
                        console.log("Response text:", xhr.responseText);
                        $('#insightContent').html('<div class="alert alert-danger">Có lỗi xảy ra khi kết nối với máy chủ: ' + error + '</div>');
                    }
                });

                // Comment out the old jQuery ajax code
                /*$.ajax({
                    url: '@Url.Action("GenerateAIInsight", "Recommendation")',
                    type: 'POST',
                    data: {
                        destination: destination,
                        interests: interests
                    },*/
                    /* success: function(response) {
                        $('#insightLoading').hide();
                        console.log("Received response from GenerateAIInsight:", response);

                        if (response.success) {
                            // Convert markdown to HTML and display
                            console.log("Success response, insight:", response.insight);
                            $('#insightContent').html(marked.parse(response.insight));
                        } else {
                            console.log("Error response:", response.error);
                            $('#insightContent').html('<div class="alert alert-danger">Có lỗi xảy ra: ' + response.error + '</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#insightLoading').hide();
                        console.log("AJAX error:", status, error);
                        console.log("Response text:", xhr.responseText);
                        $('#insightContent').html('<div class="alert alert-danger">Có lỗi xảy ra khi kết nối với máy chủ. Vui lòng thử lại sau.</div>');
                    }
                }); */
            });

            // Handle Custom Tour form submission
            $('#customTourForm').on('submit', function(e) {
                e.preventDefault();

                var preferences = $('#tourPreferences').val();
                var duration = $('#tourDuration').val();
                var budget = $('#tourBudget').val();

                if (!preferences) {
                    alert('Vui lòng nhập sở thích và mong muốn của bạn');
                    return;
                }

                // Show loading and result container
                $('#customTourResult').removeClass('d-none');
                $('#customTourLoading').show();
                $('#customTourContent').html('');

                // Call API to generate custom tour
                $.ajax({
                    url: '@Url.Action("GenerateCustomTour", "Recommendation")',
                    type: 'POST',
                    data: {
                        preferences: preferences,
                        duration: duration,
                        budget: budget
                    },
                    success: function(response) {
                        $('#customTourLoading').hide();

                        if (response.success) {
                            // Convert markdown to HTML and display
                            $('#customTourContent').html(marked.parse(response.suggestion));
                        } else {
                            $('#customTourContent').html('<div class="alert alert-danger">Có lỗi xảy ra: ' + response.error + '</div>');
                        }
                    },
                    error: function() {
                        $('#customTourLoading').hide();
                        $('#customTourContent').html('<div class="alert alert-danger">Có lỗi xảy ra khi kết nối với máy chủ. Vui lòng thử lại sau.</div>');
                    }
                });
            });
        });
    </script>

    <!-- Include Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
}
