@model ViVu.Models.ViewModels.TourReviewViewModel

@{
    ViewData["Title"] = Model.Tour.Name;
}

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="mb-4">
                <h1 class="mb-2">@Model.Tour.Name</h1>
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                        <span>@(Model.Tour.Location != null ? Model.Tour.Location.Name : ""), @(Model.Tour.City != null ? Model.Tour.City.Name : "")</span>
                    </div>
                    <div class="me-3">
                        <i class="fas fa-clock me-1 text-primary"></i>
                        <span>@Model.Tour.Duration ngày</span>
                    </div>
                    @if (Model.Tour.Reviews != null && Model.Tour.Reviews.Any())
                    {
                        <div>
                            <span class="text-warning">
                                @Model.Tour.Reviews.Average(r => r.Rating).ToString("0.0")
                                <i class="fas fa-star"></i>
                            </span>
                            <span class="text-muted">(@Model.Tour.Reviews.Count đánh giá)</span>
                        </div>
                    }
                </div>
            </div>

            <div class="mb-4">
                <div id="tourCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-indicators">
                        <button type="button" data-bs-target="#tourCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                        <button type="button" data-bs-target="#tourCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                        <button type="button" data-bs-target="#tourCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                    </div>
                    <div class="carousel-inner rounded">
                        <div class="carousel-item active">
                            <img src="@(string.IsNullOrEmpty(Model.Tour.ImageUrl) ? "/images/tours/banners/tour_kham-pha-ben-tre_banner.jpg" : Model.Tour.ImageUrl)"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Tour.Name">
                        </div>
                        <div class="carousel-item">
                            <img src="/images/tours/details/tour_ben-tre-half-day_detail_01.jpg"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Tour.Name">
                        </div>
                        <div class="carousel-item">
                            <img src="/images/tours/details/tour_ben-tre-half-day_detail_02.jpg"
                                 class="d-block w-100" style="height: 500px; object-fit: cover;" alt="@Model.Tour.Name">
                        </div>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#tourCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#tourCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Thông tin tour</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                <strong>Địa điểm:</strong>
                                <span>@(Model.Tour.Location != null ? Model.Tour.Location.Name : ""), @(Model.Tour.City != null ? Model.Tour.City.Name : "")</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-map me-2 text-primary"></i>
                                <strong>Địa chỉ:</strong>
                                <span>@Model.Tour.Address</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="fas fa-clock me-2 text-primary"></i>
                                <strong>Thời gian:</strong>
                                <span>@Model.Tour.Duration ngày</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-users me-2 text-primary"></i>
                                <strong>Số người tối đa:</strong>
                                <span>@Model.Tour.MaxGroupSize người</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">Mô tả</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Tour.Description.Replace("\n", "<br>"))
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">Lịch trình</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Tour.Itinerary.Replace("\n", "<br>"))
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="mb-3">Du lịch 360°</h5>
                        <div class="p-3 bg-light rounded">
                            <p>Trải nghiệm du lịch ảo 360° tại @Model.Tour.Name để khám phá trước khi đến thăm thực tế.</p>
                            <a href="@Url.Action("TourView", "Panorama360", new { id = Model.Tour.Id })" class="btn btn-primary">
                                <i class="fas fa-vr-cardboard me-2"></i>Xem du lịch 360°
                            </a>
                        </div>
                    </div>

                    @if (Model.Tour.TourAmenities != null && Model.Tour.TourAmenities.Any())
                    {
                        <div class="mb-4">
                            <h5 class="mb-3">Tiện ích</h5>
                            <div class="row">
                                @foreach (var tourAmenity in Model.Tour.TourAmenities)
                                {
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(tourAmenity.Amenity.Icon))
                                            {
                                                <i class="@tourAmenity.Amenity.Icon me-2 text-primary"></i>
                                            }
                                            <span>@tourAmenity.Amenity.Name</span>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Đánh giá (@(Model.Tour.Reviews != null ? Model.Tour.Reviews.Count : 0))</h5>
                </div>
                <div class="card-body">
                    @if (Model.Tour.Reviews != null && Model.Tour.Reviews.Any())
                    {
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <span class="display-4 fw-bold text-warning">@Model.Tour.Reviews.Average(r => r.Rating).ToString("0.0")</span>
                                </div>
                                <div>
                                    <div class="text-warning mb-1">
                                        @{
                                            var avgRating = Model.Tour.Reviews.Average(r => r.Rating);
                                            for (int i = 1; i <= 5; i++)
                                            {
                                                if (i <= Math.Floor(avgRating))
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                else if (i - avgRating < 1 && i - avgRating > 0)
                                                {
                                                    <i class="fas fa-star-half-alt"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star"></i>
                                                }
                                            }
                                        }
                                    </div>
                                    <div class="text-muted">Dựa trên @Model.Tour.Reviews.Count đánh giá</div>
                                </div>
                            </div>
                        </div>

                        @foreach (var review in Model.Tour.Reviews.OrderByDescending(r => r.CreatedAt))
                        {
                            <div class="border-bottom mb-3 pb-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>@review.User.FullName</strong>
                                    </div>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="@(i <= review.Rating ? "fas" : "far") fa-star"></i>
                                        }
                                    </div>
                                </div>
                                <div class="text-muted small">@review.CreatedAt.ToString("dd/MM/yyyy")</div>
                                <div>@review.Comment</div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Chưa có đánh giá nào cho tour này.
                        </div>
                    }

                    @if (User.Identity != null && User.Identity.IsAuthenticated)
                    {
                        <div class="mt-4">
                            <a asp-controller="Review" asp-action="Create" asp-route-type="Tour" asp-route-id="@Model.Tour.Id" class="btn btn-primary">
                                <i class="fas fa-star me-2"></i>Viết đánh giá
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm border-0 mb-4 sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Đặt tour</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h3 class="text-primary mb-3">@Model.Tour.Price.ToString("N0") VNĐ</h3>
                    </div>

                    <form asp-controller="Tour" asp-action="AddToCart" method="post">
                        <input type="hidden" name="tourId" value="@Model.Tour.Id" />

                        <div class="mb-3">
                            <label for="tourDate" class="form-label">Ngày khởi hành</label>
                            <input type="date" id="tourDate" name="tourDate" class="form-control" required min="@DateTime.Now.ToString("yyyy-MM-dd")" />
                        </div>

                        <div class="mb-3">
                            <label for="numberOfAdults" class="form-label">Số người lớn</label>
                            <input type="number" id="numberOfAdults" name="numberOfAdults" class="form-control" min="1" max="@Model.Tour.MaxGroupSize" value="1" required />
                        </div>

                        <div class="mb-3">
                            <label for="numberOfChildren" class="form-label">Số trẻ em</label>
                            <input type="number" id="numberOfChildren" name="numberOfChildren" class="form-control" min="0" max="@Model.Tour.MaxGroupSize" value="0" />
                            <small class="text-muted">Trẻ em dưới 12 tuổi (giảm 50% giá)</small>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-shopping-cart me-2"></i>Thêm vào giỏ hàng
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }

        .rating:not(:checked) > input {
            position: absolute;
            clip: rect(0,0,0,0);
        }

        .rating:not(:checked) > label {
            float: right;
            width: 1em;
            padding: 0 .1em;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            font-size: 2rem;
            line-height: 1.2;
            color: #ddd;
        }

        .rating:not(:checked) > label:before {
            content: '★ ';
        }

        .rating > input:checked ~ label {
            color: #ffb700;
        }

        .rating:not(:checked) > label:hover,
        .rating:not(:checked) > label:hover ~ label {
            color: #ffb700;
        }

        .rating > input:checked + label:hover,
        .rating > input:checked + label:hover ~ label,
        .rating > input:checked ~ label:hover,
        .rating > input:checked ~ label:hover ~ label,
        .rating > label:hover ~ input:checked ~ label {
            color: #ffb700;
        }
    </style>
}
