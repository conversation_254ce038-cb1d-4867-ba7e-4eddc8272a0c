/**
 * AI Loading Screen JavaScript
 * Handles the display and animation of the AI recommendation loading screen
 */

// Show the AI loading screen
function showAILoadingScreen() {
    // Display the loading screen
    $('#aiLoadingScreen').css('display', 'flex');
    
    // Add body class to prevent scrolling
    $('body').addClass('ai-loading-active');
    
    // Start the progress animation
    startProgressAnimation();
    
    // Log for debugging
    console.log('AI loading screen displayed');
}

// Hide the AI loading screen
function hideAILoadingScreen() {
    // Add fade-out animation
    $('#aiLoadingScreen').fadeOut(500, function() {
        // Remove body class to allow scrolling again
        $('body').removeClass('ai-loading-active');
    });
    
    // Log for debugging
    console.log('AI loading screen hidden');
}

// Simulate progress animation
function startProgressAnimation() {
    // This is just a visual effect since we don't know the actual progress
    // The progress bar is already animated with Bootstrap classes
}

// Initialize the AI loading functionality
$(document).ready(function() {
    // Add event listener to recommendation form submission
    $('#recommendationForm, #simpleRecommendationForm').on('submit', function() {
        // Show loading screen when form is submitted
        showAILoadingScreen();
        
        // Store the form submission time
        localStorage.setItem('aiLoadingStartTime', new Date().getTime());
        
        // Return true to allow the form to submit
        return true;
    });
    
    // Check if we need to show the loading screen (e.g., after a page refresh during processing)
    if (localStorage.getItem('aiLoadingActive') === 'true') {
        showAILoadingScreen();
    }
});

// Function to check if AI is still processing
function checkAIProcessingStatus() {
    // This function would typically make an AJAX call to check the status
    // For now, we'll just use a timeout for demonstration
    
    // Get the start time from localStorage
    const startTime = localStorage.getItem('aiLoadingStartTime');
    
    if (startTime) {
        const currentTime = new Date().getTime();
        const elapsedTime = (currentTime - startTime) / 1000; // in seconds
        
        console.log(`AI processing time: ${elapsedTime.toFixed(1)} seconds`);
        
        // If processing is taking too long (over 60 seconds), show a message
        if (elapsedTime > 60) {
            $('.ai-loading-subtitle').text('Quá trình đang mất nhiều thời gian hơn dự kiến...');
        }
    }
}

// Set interval to check processing status every 5 seconds
setInterval(checkAIProcessingStatus, 5000);
