@{
    ViewData["Title"] = "Hướng dẫn sử dụng Du lịch 360°";
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Hướng dẫn sử dụng tính năng Du lịch 360°</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Giới thiệu</h2>
                </div>
                <div class="card-body">
                    <p>
                        Tính năng Du lịch 360° cho phép bạn khám phá các địa điểm du lịch tại Bến Tre với góc nhìn toàn cảnh 360 độ, 
                        giúp bạn có cái nhìn tổng quan về điểm đến trước khi quyết định đặt tour hoặc lưu trú.
                    </p>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Cách điều khiển</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h6">Điều khiển bằng chuột:</h3>
                            <ul>
                                <li><strong>Kéo chuột</strong>: Di chuyển góc nhìn</li>
                                <li><strong>Cuộn chuột</strong>: Phóng to/thu nhỏ</li>
                                <li><strong>Nhấp đúp</strong>: Phóng to vào một điểm</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h6">Điều khiển bằng cảm ứng:</h3>
                            <ul>
                                <li><strong>Vuốt</strong>: Di chuyển góc nhìn</li>
                                <li><strong>Chụm/Tách hai ngón tay</strong>: Phóng to/thu nhỏ</li>
                                <li><strong>Nhấp đúp</strong>: Phóng to vào một điểm</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h3 class="h6">Các nút điều khiển:</h3>
                        <ul>
                            <li><strong>Nút la bàn</strong>: Hiển thị hướng bắc</li>
                            <li><strong>Nút toàn màn hình</strong>: Chuyển sang chế độ toàn màn hình</li>
                            <li><strong>Nút phóng to/thu nhỏ</strong>: Điều chỉnh mức độ phóng đại</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Điểm thông tin (Hotspot)</h2>
                </div>
                <div class="card-body">
                    <p>
                        Trong các hình ảnh 360°, bạn sẽ thấy các <strong>điểm thông tin</strong> (hotspot) được đánh dấu.
                        Khi di chuột hoặc chạm vào các điểm này, bạn sẽ thấy thông tin mô tả về địa điểm hoặc đối tượng đó.
                    </p>
                    <p>
                        Một số điểm thông tin có thể được liên kết đến các trang khác, khi nhấp vào sẽ đưa bạn đến trang thông tin chi tiết.
                    </p>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Mẹo sử dụng</h2>
                </div>
                <div class="card-body">
                    <ul>
                        <li>Sử dụng chế độ toàn màn hình để có trải nghiệm tốt nhất</li>
                        <li>Trên thiết bị di động, xoay ngang màn hình để có góc nhìn rộng hơn</li>
                        <li>Nếu bạn đang sử dụng thiết bị có cảm biến con quay hồi chuyển (gyroscope), hãy thử chế độ xem theo chuyển động của thiết bị</li>
                        <li>Khám phá tất cả các điểm thông tin để có thêm thông tin về địa điểm</li>
                    </ul>
                </div>
            </div>
            
            <div class="text-center mb-5">
                <a href="@Url.Action("Index", "Home")" class="btn btn-primary">
                    <i class="fas fa-home"></i> Quay lại trang chủ
                </a>
            </div>
        </div>
    </div>
</div>
