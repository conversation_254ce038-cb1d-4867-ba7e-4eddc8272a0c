﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class VehicleImage
    {
        public int Id { get; set; }
        
        [Required]
        public string ImageUrl { get; set; }
        
        public int VehicleId { get; set; }
        
        [ForeignKey("VehicleId")]
        public Vehicle? Vehicle { get; set; }
    }
}
