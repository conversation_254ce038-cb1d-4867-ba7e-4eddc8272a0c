﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ViVu.Models
{
    public class TourImage
    {
        public int Id { get; set; }
        
        [Required]
        public string ImageUrl { get; set; }
        
        public int TourId { get; set; }
        
        [ForeignKey("TourId")]
        public Tour Tour { get; set; }
        
        public bool IsMain { get; set; } = false;
        
        public string? Caption { get; set; }
    }
}
