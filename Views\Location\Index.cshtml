@model IEnumerable<ViVu.Models.Location>

@{
    ViewData["Title"] = "Địa điểm du lịch";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex">
                <input type="text" name="searchTerm" class="form-control me-2" placeholder="Tìm kiếm địa điểm..." value="@ViewBag.SearchTerm">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    
    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Không tìm thấy địa điểm nào.
        </div>
    }
    else
    {
        <div class="row">
            @foreach (var location in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <img src="@(string.IsNullOrEmpty(location.ImageUrl) ? "/images/destinations/default.jpg" : location.ImageUrl)" 
                             class="card-img-top" alt="@location.Name" style="height: 200px; object-fit: cover;">
                        <div class="card-body">
                            <h5 class="card-title">@location.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i> @location.City?.Name
                            </p>
                            <p class="card-text">@(location.Description?.Length > 100 ? location.Description.Substring(0, 100) + "..." : location.Description)</p>
                        </div>
                        <div class="card-footer bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <a asp-action="Details" asp-route-id="@location.Id" class="btn btn-outline-primary">
                                    <i class="fas fa-info-circle me-1"></i>Chi tiết
                                </a>
                                <a href="@Url.Action("LocationView", "Panorama360", new { id = location.Id })" class="btn btn-outline-info">
                                    <i class="fas fa-vr-cardboard me-1"></i>Xem 360°
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
