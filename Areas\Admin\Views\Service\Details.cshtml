@model ViVu.Models.Service
@{
    ViewData["Title"] = "Chi tiết dịch vụ";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Chi tiết dịch vụ</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-action="Index">Dịch vụ</a></li>
        <li class="breadcrumb-item active">Chi tiết</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Thông tin chi tiết dịch vụ
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h3 class="mb-3">@Model.Name</h3>
                    
                    <div class="mb-3">
                        <strong>Đ<PERSON><PERSON> điểm:</strong>
                        <span>@(Model.Location != null ? Model.Location.Name : ""), @(Model.City != null ? Model.City.Name : "")</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Thời gian:</strong>
                        <span>@Model.Duration phút</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Giá:</strong>
                        <span>@Model.Price.ToString("N0") VNĐ</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Nổi bật:</strong>
                        @if (Model.IsFeatured)
                        {
                            <span class="badge bg-success">Có</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Không</span>
                        }
                    </div>
                    
                    <div class="mb-4">
                        <h5>Mô tả</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Description.Replace("\n", "<br>"))
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>Chi tiết dịch vụ</h5>
                        <div class="p-3 bg-light rounded">
                            @Html.Raw(Model.Details.Replace("\n", "<br>"))
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded mb-3" />
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>Chưa có hình ảnh
                        </div>
                    }
                    
                    @if (Model.Reviews != null && Model.Reviews.Any())
                    {
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Đánh giá (@Model.Reviews.Count)</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Điểm trung bình:</strong>
                                    <span class="text-warning">
                                        @Model.Reviews.Average(r => r.Rating).ToString("0.0")
                                        <i class="fas fa-star"></i>
                                    </span>
                                </div>
                                
                                <div class="list-group">
                                    @foreach (var review in Model.Reviews.OrderByDescending(r => r.CreatedAt).Take(3))
                                    {
                                        <div class="list-group-item">
                                            <div class="d-flex justify-content-between">
                                                <strong>@review.User.FullName</strong>
                                                <small>@review.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </div>
                                            <div class="text-warning mb-1">
                                                @for (int i = 0; i < review.Rating; i++)
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                            </div>
                                            <p class="mb-0">@review.Comment</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
            
            <div class="mt-3">
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Chỉnh sửa
                </a>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
    </div>
</div>
