﻿// ViVu Travel - Enhanced UI JavaScript

// Wait for the DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
  // Navbar scroll effect
  const navbar = document.querySelector(".navbar");

  if (navbar) {
    // Initial check for page load with scroll already happened
    if (window.scrollY > 50) {
      navbar.classList.add("scrolled");
    }

    window.addEventListener("scroll", function () {
      if (window.scrollY > 50) {
        navbar.classList.add("scrolled");
      } else {
        navbar.classList.remove("scrolled");
      }
    });
  }

  // Add active class to current nav item
  const currentLocation = location.pathname;
  const navLinks = document.querySelectorAll(".navbar-nav .nav-link");

  navLinks.forEach((link) => {
    const linkPath = link.getAttribute("href");
    if (linkPath && currentLocation.includes(linkPath) && linkPath !== "/") {
      link.closest(".nav-item").classList.add("active");
    } else if (currentLocation === "/" && linkPath === "/") {
      link.closest(".nav-item").classList.add("active");
    }
  });

  // Initialize reveal on scroll elements
  const revealElements = document.querySelectorAll(".reveal-on-scroll");

  if (revealElements.length > 0) {
    const revealOnScroll = function () {
      revealElements.forEach((element) => {
        const elementTop = element.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        if (elementTop < windowHeight - 100) {
          element.classList.add("visible");
        }
      });
    };

    // Initial check
    revealOnScroll();

    // Check on scroll
    window.addEventListener("scroll", revealOnScroll);
  }

  // Add hover effects to cards
  const cards = document.querySelectorAll(".card");

  cards.forEach((card, index) => {
    // Add staggered animation delay
    card.style.animationDelay = `${index * 0.1}s`;

    // Add hover sound effect (subtle)
    card.addEventListener("mouseenter", function () {
      // You could add a subtle sound effect here if desired
      // const hoverSound = new Audio('/sounds/hover.mp3');
      // hoverSound.volume = 0.1;
      // hoverSound.play();
    });
  });

  // Enhance buttons with ripple effect
  const buttons = document.querySelectorAll(".btn");

  buttons.forEach((button) => {
    button.addEventListener("click", function (e) {
      const x = e.clientX - e.target.getBoundingClientRect().left;
      const y = e.clientY - e.target.getBoundingClientRect().top;

      const ripple = document.createElement("span");
      ripple.classList.add("btn-ripple");
      ripple.style.left = `${x}px`;
      ripple.style.top = `${y}px`;

      this.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Enhance form inputs
  const formInputs = document.querySelectorAll(".form-control");

  formInputs.forEach((input) => {
    input.addEventListener("focus", function () {
      this.parentElement.classList.add("input-focused");
    });

    input.addEventListener("blur", function () {
      this.parentElement.classList.remove("input-focused");
    });
  });

  // Add parallax effect to hero section
  const heroSection = document.querySelector(".hero-section");

  if (heroSection) {
    const heroImg = heroSection.querySelector("img");

    if (heroImg) {
      window.addEventListener("scroll", function () {
        const scrollPosition = window.scrollY;
        heroImg.style.transform = `translateY(${scrollPosition * 0.4}px)`;
      });
    }
  }

  // Add smooth scrolling to all links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");

      if (targetId === "#") return;

      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        // Get navbar height dynamically to ensure proper scrolling position
        const navbarHeight =
          document.querySelector(".navbar")?.offsetHeight || 90;

        window.scrollTo({
          top: targetElement.offsetTop - navbarHeight - 20, // Added extra padding
          behavior: "smooth",
        });
      }
    });
  });

  // Add counter animation to numbers
  const counters = document.querySelectorAll(".counter");

  if (counters.length > 0) {
    const animateCounter = function (counter) {
      const target = parseInt(counter.getAttribute("data-target"));
      const count = parseInt(counter.innerText);
      const increment = target / 100;

      if (count < target) {
        counter.innerText = Math.ceil(count + increment);
        setTimeout(() => animateCounter(counter), 20);
      } else {
        counter.innerText = target;
      }
    };

    const counterObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateCounter(entry.target);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.5 }
    );

    counters.forEach((counter) => {
      counter.innerText = "0";
      counterObserver.observe(counter);
    });
  }

  // Add image zoom effect on hover
  const zoomImages = document.querySelectorAll(".zoom-img");

  zoomImages.forEach((img) => {
    img.addEventListener("mouseenter", function () {
      this.style.transform = "scale(1.1)";
    });

    img.addEventListener("mouseleave", function () {
      this.style.transform = "scale(1)";
    });
  });

  // Initialize tooltips if Bootstrap is available
  if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
    const tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Initialize popovers if Bootstrap is available
  if (typeof bootstrap !== "undefined" && bootstrap.Popover) {
    const popoverTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="popover"]')
    );
    popoverTriggerList.map(function (popoverTriggerEl) {
      return new bootstrap.Popover(popoverTriggerEl);
    });
  }
});

// Add CSS class for button ripple effect
const style = document.createElement("style");
style.textContent = `
.btn-ripple {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    transform: scale(0);
    animation: ripple 0.6s linear;
    z-index: 0;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.btn {
    position: relative;
    overflow: hidden;
}

.input-focused {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}
`;
document.head.appendChild(style);
