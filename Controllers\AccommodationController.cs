﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Controllers
{
    public class AccommodationController : Controller
    {
        private readonly IAccommodationRepository _accommodationRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly ICityRepository _cityRepository;
        private readonly IRoomRepository _roomRepository;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public AccommodationController(
            IAccommodationRepository accommodationRepository,
            ILocationRepository locationRepository,
            ICityRepository cityRepository,
            IRoomRepository roomRepository,
            IWebHostEnvironment webHostEnvironment)
        {
            _accommodationRepository = accommodationRepository;
            _locationRepository = locationRepository;
            _cityRepository = cityRepository;
            _roomRepository = roomRepository;
            _webHostEnvironment = webHostEnvironment;
        }

        // Hiển thị danh sách khách sạn
        [AllowAnonymous]
        public async Task<IActionResult> Index(string searchQuery, int? locationId, int? cityId, int? minPrice, int? maxPrice, int? starRating, string sortOrder)
        {
            ViewBag.CurrentSort = sortOrder;
            ViewBag.SearchQuery = searchQuery;
            ViewBag.LocationId = locationId;
            ViewBag.CityId = cityId;
            ViewBag.MinPrice = minPrice;
            ViewBag.MaxPrice = maxPrice;
            ViewBag.StarRating = starRating;

            // Lấy danh sách địa điểm và thành phố cho bộ lọc
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");

            // Tìm kiếm khách sạn
            var accommodations = await _accommodationRepository.SearchAsync(
                searchQuery, locationId, cityId, null, null, minPrice, maxPrice, starRating);

            // Sắp xếp kết quả
            switch (sortOrder)
            {
                case "price_asc":
                    accommodations = accommodations.OrderBy(a => a.MinPrice).ToList();
                    break;
                case "price_desc":
                    accommodations = accommodations.OrderByDescending(a => a.MinPrice).ToList();
                    break;
                case "rating_desc":
                    accommodations = accommodations.OrderByDescending(a => a.AverageRating).ToList();
                    break;
                default:
                    accommodations = accommodations.OrderBy(a => a.Name).ToList();
                    break;
            }

            return View(accommodations);
        }

        // Hiển thị chi tiết khách sạn
        [AllowAnonymous]
        public async Task<IActionResult> Details(int id, DateTime? checkIn, DateTime? checkOut)
        {
            var accommodation = await _accommodationRepository.GetByIdAsync(id);
            if (accommodation == null)
            {
                return NotFound();
            }

            ViewBag.CheckIn = checkIn ?? DateTime.Today.AddDays(1);
            ViewBag.CheckOut = checkOut ?? DateTime.Today.AddDays(2);

            // Lấy danh sách phòng có sẵn trong khoảng thời gian
            if (checkIn.HasValue && checkOut.HasValue)
            {
                ViewBag.AvailableRooms = await _roomRepository.GetAvailableRoomsAsync(id, checkIn.Value, checkOut.Value);
            }
            else
            {
                ViewBag.AvailableRooms = await _roomRepository.GetByAccommodationIdAsync(id);
            }

            return View(accommodation);
        }

        // Hiển thị form thêm khách sạn mới (chỉ Admin)
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create()
        {
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View();
        }

        // Xử lý thêm khách sạn mới
        [Authorize(Roles = "Admin")]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Accommodation accommodation, IFormFile imageFile)
        {
            if (ModelState.IsValid)
            {
                if (imageFile != null)
                {
                    accommodation.ImageUrl = await SaveImage(imageFile);
                }

                await _accommodationRepository.AddAsync(accommodation);
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View(accommodation);
        }

        // Hiển thị form cập nhật khách sạn
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int id)
        {
            var accommodation = await _accommodationRepository.GetByIdAsync(id);
            if (accommodation == null)
            {
                return NotFound();
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View(accommodation);
        }

        // Xử lý cập nhật khách sạn
        [Authorize(Roles = "Admin")]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Accommodation accommodation, IFormFile imageFile)
        {
            if (id != accommodation.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                if (imageFile != null)
                {
                    accommodation.ImageUrl = await SaveImage(imageFile);
                }

                await _accommodationRepository.UpdateAsync(accommodation);
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Cities = new SelectList(await _cityRepository.GetAllAsync(), "Id", "Name");
            return View(accommodation);
        }

        // Xử lý xóa khách sạn
        [Authorize(Roles = "Admin")]
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _accommodationRepository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }

        // Phương thức lưu hình ảnh
        private async Task<string> SaveImage(IFormFile imageFile)
        {
            string uniqueFileName = null;
            if (imageFile != null)
            {
                string uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "images", "accommodations");
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }
                uniqueFileName = Guid.NewGuid().ToString() + "_" + imageFile.FileName;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(fileStream);
                }
                return "/images/accommodations/" + uniqueFileName;
            }
            return uniqueFileName;
        }
    }
}
