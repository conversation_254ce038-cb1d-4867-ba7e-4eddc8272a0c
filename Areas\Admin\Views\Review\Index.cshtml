@model IEnumerable<ViVu.Models.Review>
@{
    ViewData["Title"] = "Quản lý đánh giá";
    Layout = "_AdminLayout";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">@ViewData["Title"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Admin" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">@ViewData["Title"]</li>
    </ol>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Danh sách đánh giá
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <form asp-action="Index" method="get" class="d-flex">
                        <div class="input-group">
                            <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" class="form-control" placeholder="Tìm kiếm...">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            @if (!string.IsNullOrEmpty((string)ViewData["CurrentFilter"]))
                            {
                                <a asp-action="Index" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            }
                        </div>
                    </form>
                </div>
            </div>

            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Người dùng</th>
                        <th>Đối tượng</th>
                        <th>
                            <a asp-action="Index" asp-route-sortOrder="@ViewData["RatingSortParam"]">
                                Đánh giá
                                @if (ViewData["CurrentSort"] != null && ViewData["CurrentSort"].ToString() == "rating")
                                {
                                    <i class="fas fa-sort-up"></i>
                                }
                                else if (ViewData["CurrentSort"] != null && ViewData["CurrentSort"].ToString() == "rating_desc")
                                {
                                    <i class="fas fa-sort-down"></i>
                                }
                                else
                                {
                                    <i class="fas fa-sort"></i>
                                }
                            </a>
                        </th>
                        <th>Nhận xét</th>
                        <th>
                            <a asp-action="Index" asp-route-sortOrder="@ViewData["DateSortParam"]">
                                Ngày tạo
                                @if (ViewData["CurrentSort"] == null || string.IsNullOrEmpty(ViewData["CurrentSort"].ToString()))
                                {
                                    <i class="fas fa-sort-up"></i>
                                }
                                else if (ViewData["CurrentSort"] != null && ViewData["CurrentSort"].ToString() == "date_desc")
                                {
                                    <i class="fas fa-sort-down"></i>
                                }
                                else
                                {
                                    <i class="fas fa-sort"></i>
                                }
                            </a>
                        </th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        string itemName = "";
                        string itemType = "";

                        if (item.AccommodationId.HasValue && item.Accommodation != null)
                        {
                            itemName = item.Accommodation.Name;
                            itemType = "Chỗ ở";
                        }
                        else if (item.TourId.HasValue && item.Tour != null)
                        {
                            itemName = item.Tour.Name;
                            itemType = "Tour";
                        }
                        else if (item.ServiceId.HasValue && item.Service != null)
                        {
                            itemName = item.Service.Name;
                            itemType = "Dịch vụ";
                        }

                        <tr>
                            <td>@item.Id</td>
                            <td>@item.User?.FullName</td>
                            <td>
                                <span class="badge bg-info">@itemType</span>
                                @itemName
                            </td>
                            <td>
                                <div class="text-warning">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        if (i <= item.Rating)
                                        {
                                            <i class="fas fa-star"></i>
                                        }
                                        else
                                        {
                                            <i class="far fa-star"></i>
                                        }
                                    }
                                </div>
                            </td>
                            <td>@(item.Comment.Length > 50 ? item.Comment.Substring(0, 50) + "..." : item.Comment)</td>
                            <td>@item.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>
                                <div class="btn-group">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
