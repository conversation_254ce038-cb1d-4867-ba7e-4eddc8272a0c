﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using ViVu.Models;
using ViVu.Repositories;

namespace ViVu.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class Panorama360Controller : Controller
    {
        private readonly IPanorama360Repository _panorama360Repository;
        private readonly ITourRepository _tourRepository;
        private readonly ILocationRepository _locationRepository;
        private readonly IWebHostEnvironment _hostEnvironment;

        public Panorama360Controller(
            IPanorama360Repository panorama360Repository,
            ITourRepository tourRepository,
            ILocationRepository locationRepository,
            IWebHostEnvironment hostEnvironment)
        {
            _panorama360Repository = panorama360Repository;
            _tourRepository = tourRepository;
            _locationRepository = locationRepository;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Admin/Panorama360
        public async Task<IActionResult> Index()
        {
            var panoramas = await _panorama360Repository.GetAllAsync();
            return View(panoramas);
        }

        // GET: Admin/Panorama360/Create
        public async Task<IActionResult> Create()
        {
            await LoadDropdownData();
            return View();
        }

        // POST: Admin/Panorama360/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Panorama360 panorama, IFormFile imageFile)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // Xử lý hình ảnh
                    if (imageFile != null)
                    {
                        panorama.ImageUrl = await SaveImage(imageFile);
                    }
                    else
                    {
                        // Nếu không có file ảnh, sử dụng ảnh mặc định hoặc báo lỗi
                        ModelState.AddModelError("imageFile", "Vui lòng chọn hình ảnh panorama 360°");
                        await LoadDropdownData();
                        return View(panorama);
                    }

                    await _panorama360Repository.AddAsync(panorama);
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                // Log lỗi
                ModelState.AddModelError("", "Lỗi khi lưu: " + ex.Message);
            }

            await LoadDropdownData();
            return View(panorama);
        }

        // GET: Admin/Panorama360/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var panorama = await _panorama360Repository.GetByIdAsync(id);
            if (panorama == null)
            {
                return NotFound();
            }

            await LoadDropdownData();
            return View(panorama);
        }

        // POST: Admin/Panorama360/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Panorama360 panorama, IFormFile imageFile)
        {
            if (id != panorama.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                // Xử lý hình ảnh
                if (imageFile != null)
                {
                    panorama.ImageUrl = await SaveImage(imageFile);
                }

                await _panorama360Repository.UpdateAsync(panorama);
                return RedirectToAction(nameof(Index));
            }

            await LoadDropdownData();
            return View(panorama);
        }

        // GET: Admin/Panorama360/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var panorama = await _panorama360Repository.GetByIdAsync(id);
            if (panorama == null)
            {
                return NotFound();
            }

            return View(panorama);
        }

        // POST: Admin/Panorama360/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _panorama360Repository.DeleteAsync(id);
            return RedirectToAction(nameof(Index));
        }

        // Phương thức lưu hình ảnh
        private async Task<string> SaveImage(IFormFile imageFile)
        {
            try
            {
                string wwwRootPath = _hostEnvironment.WebRootPath;
                string fileName = Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName);
                string panoramaPath = Path.Combine(wwwRootPath, "images", "panoramas");

                // Đảm bảo thư mục tồn tại
                if (!Directory.Exists(panoramaPath))
                {
                    Directory.CreateDirectory(panoramaPath);
                }

                string fullPath = Path.Combine(panoramaPath, fileName);

                // Lưu file
                using (var fileStream = new FileStream(fullPath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(fileStream);
                }

                // Kiểm tra xem file đã được lưu thành công chưa
                if (!System.IO.File.Exists(fullPath))
                {
                    throw new Exception("Không thể lưu file. Vui lòng kiểm tra quyền truy cập thư mục.");
                }

                return "/images/panoramas/" + fileName;
            }
            catch (Exception ex)
            {
                // Log lỗi và ném ngoại lệ để xử lý ở phương thức gọi
                Console.WriteLine($"Lỗi khi lưu hình ảnh: {ex.Message}");
                throw new Exception($"Lỗi khi lưu hình ảnh: {ex.Message}", ex);
            }
        }

        // Phương thức load dữ liệu cho dropdown
        private async Task LoadDropdownData()
        {
            ViewBag.Tours = new SelectList(await _tourRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Locations = new SelectList(await _locationRepository.GetAllAsync(), "Id", "Name");
            ViewBag.Types = new SelectList(new[] { "Tour", "Location" }, "Type");
        }
    }
}
