using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;

namespace ViVu.Services
{
    public class OpenAIService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly string _model = "gpt-3.5-turbo";
        private readonly string _apiUrl = "https://api.openai.com/v1/chat/completions";

        public OpenAIService(IConfiguration configuration)
        {
            _configuration = configuration;
            string apiKey = _configuration["OpenAI:ApiKey"];

            if (string.IsNullOrEmpty(apiKey))
            {
                Console.WriteLine("CRITICAL ERROR: OpenAI API key is not configured in appsettings.json");
                throw new ArgumentException("OpenAI API key is not configured.");
            }

            // Kiểm tra định dạng API key
            if (!apiKey.StartsWith("sk-"))
            {
                Console.WriteLine("WARNING: OpenAI API key may be invalid. It should start with 'sk-'");
            }

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

            // Ghi log thông tin khởi tạo
            Console.WriteLine("OpenAIService initialized with API key (first 5 chars): " + (apiKey.Length > 5 ? apiKey.Substring(0, 5) + "..." : "invalid"));

            // Thiết lập timeout cho HTTP client
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task<string> GeneratePersonalizedRecommendation(string userPreferences, string travelHistory, string destination)
        {
            try
            {
                var requestData = new
                {
                    model = _model,
                    messages = new[]
                    {
                        new { role = "system", content =
                            "You are an AI travel assistant specializing in Vietnam tourism, particularly for Ben Tre province. " +
                            "Your task is to provide personalized travel recommendations based on user preferences and travel history. " +
                            "Make your recommendations specific, detailed, and tailored to the individual user. " +
                            "IMPORTANT GUIDELINES:\n" +
                            "1. You must STRICTLY follow the user's preferences. Do not recommend activities or travel styles that contradict their stated preferences.\n" +
                            "2. If they prefer 'Tour có hướng dẫn' (guided tours), suggest guided tours. If they prefer 'Du lịch tự túc' (independent travel), suggest self-guided itineraries.\n" +
                            "3. Only suggest nature activities if they selected 'Thiên nhiên' (Nature). Similarly for 'Lịch sử' (History), 'Ẩm thực' (Food), 'Mạo hiểm' (Adventure), and 'Thư giãn' (Relaxation).\n" +
                            "4. If they are traveling with children or elderly people, ensure activities are suitable for them.\n" +
                            "5. Ensure budget and duration recommendations fall within the user's specified range.\n" +
                            "6. Do not recommend activities they didn't select in their preferences - this is critical." },

                        new { role = "user", content =
                            $"Generate a personalized travel recommendation for a trip to {destination}.\n\n" +
                            $"USER PREFERENCES:\n{userPreferences}\n\n" +
                            $"PREVIOUS TRAVEL HISTORY:\n{travelHistory}\n\n" +
                            "INSTRUCTIONS:\n" +
                            "1. Create a detailed itinerary that EXACTLY matches the user's preferences - this is CRITICAL\n" +
                            "2. The budget MUST be within the exact range specified by the user - do not suggest cheaper or more expensive options\n" +
                            "3. The duration MUST match exactly what the user specified - not shorter or longer\n" +
                            "4. If the user is traveling with children, include child-friendly activities\n" +
                            "5. Include day-by-day activities that match ALL of their selected interests, not just some\n" +
                            "6. Provide accommodation suggestions that are EXACTLY within their budget range\n" +
                            "7. If they selected both guided tours and independent travel, include BOTH in your recommendation\n" +
                            "8. Format the response as a clear, structured itinerary with days clearly labeled\n" +
                            "9. DO NOT recommend activities that contradict their preferences or that they didn't select\n" +
                            "10. Include a summary at the beginning that confirms you're addressing ALL their preferences" }
                    },
                    temperature = 0.5 // Reduced temperature for more focused responses
                };

                var content = new StringContent(JsonSerializer.Serialize(requestData), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"OpenAI API response received. Length: {responseContent.Length} characters");

                    try
                    {
                        var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);
                        var responseText = responseObject.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();

                        // Kiểm tra nội dung phản hồi
                        if (string.IsNullOrWhiteSpace(responseText))
                        {
                            Console.WriteLine("WARNING: OpenAI returned empty content");
                            return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                        }

                        // Ghi log thành công
                        Console.WriteLine($"Successfully generated AI recommendation. Length: {responseText.Length} characters");
                        return responseText;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing OpenAI response: {ex.Message}");
                        Console.WriteLine($"Response content: {responseContent}");
                        return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    // Ghi log chi tiết hơn để dễ dàng gỡ lỗi
                    Console.WriteLine($"OpenAI API error (Status: {response.StatusCode}): {errorContent}");

                    // Ghi log thêm thông tin về request để giúp gỡ lỗi
                    Console.WriteLine($"Request details - Model: {_model}, Destination: {destination}");
                    Console.WriteLine($"User preferences length: {userPreferences?.Length ?? 0}, Travel history length: {travelHistory?.Length ?? 0}");

                    // Thông báo lỗi cụ thể hơn
                    string errorMessage = "Không thể kết nối với dịch vụ AI.";

                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        errorMessage = "API key không hợp lệ hoặc đã hết hạn. Vui lòng kiểm tra lại cấu hình.";
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        errorMessage = "Đã vượt quá giới hạn yêu cầu đến dịch vụ AI. Vui lòng thử lại sau.";
                    }

                    return $"Rất tiếc, chúng tôi không thể tạo gợi ý cá nhân hóa vào lúc này. Lỗi: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                // Ghi log chi tiết hơn
                Console.WriteLine($"Error generating AI recommendation: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                return "Rất tiếc, chúng tôi không thể tạo gợi ý cá nhân hóa vào lúc này. Vui lòng thử lại sau. " +
                       "Lỗi: Đã xảy ra lỗi khi xử lý yêu cầu.";
            }
        }

        public async Task<string> GenerateTravelInsights(string destination, string interests)
        {
            try
            {
                var requestData = new
                {
                    model = _model,
                    messages = new[]
                    {
                        new { role = "system", content = "You are a knowledgeable travel guide with expertise in Vietnamese culture, history, and tourism. " +
                                                        "Provide insightful information about destinations in Vietnam, focusing on cultural context, historical significance, " +
                                                        "and unique experiences." },
                        new { role = "user", content = $"Tell me about {destination} in Vietnam. I'm particularly interested in {interests}. " +
                                                      "Include information about the best time to visit, cultural significance, historical background, " +
                                                      "and any unique or lesser-known aspects that would enhance a visitor's experience. " +
                                                      "Keep the response informative but concise, under 500 words." }
                    },
                    temperature = 0.7
                };

                var content = new StringContent(JsonSerializer.Serialize(requestData), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"OpenAI API response received. Length: {responseContent.Length} characters");

                    try
                    {
                        var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);
                        var responseText = responseObject.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();

                        // Kiểm tra nội dung phản hồi
                        if (string.IsNullOrWhiteSpace(responseText))
                        {
                            Console.WriteLine("WARNING: OpenAI returned empty content");
                            return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                        }

                        // Ghi log thành công
                        Console.WriteLine($"Successfully generated travel insights. Length: {responseText.Length} characters");
                        return responseText;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing OpenAI response: {ex.Message}");
                        Console.WriteLine($"Response content: {responseContent}");
                        return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    // Ghi log chi tiết hơn để dễ dàng gỡ lỗi
                    Console.WriteLine($"OpenAI API error (Status: {response.StatusCode}): {errorContent}");

                    // Ghi log thêm thông tin về request để giúp gỡ lỗi
                    Console.WriteLine($"Request details - Model: {_model}, Destination: {destination}, Interests: {interests}");

                    // Thông báo lỗi cụ thể hơn
                    string errorMessage = "Không thể kết nối với dịch vụ AI.";

                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        errorMessage = "API key không hợp lệ hoặc đã hết hạn. Vui lòng kiểm tra lại cấu hình.";
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        errorMessage = "Đã vượt quá giới hạn yêu cầu đến dịch vụ AI. Vui lòng thử lại sau.";
                    }

                    return $"Rất tiếc, chúng tôi không thể tạo thông tin du lịch vào lúc này. Lỗi: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                // Ghi log chi tiết hơn
                Console.WriteLine($"Error generating travel insights: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                return "Rất tiếc, chúng tôi không thể tạo thông tin du lịch vào lúc này. Vui lòng thử lại sau. " +
                       "Lỗi: Đã xảy ra lỗi khi xử lý yêu cầu.";
            }
        }

        public async Task<string> GenerateCustomTourSuggestion(string preferences, string duration, string budget)
        {
            try
            {
                var requestData = new
                {
                    model = _model,
                    messages = new[]
                    {
                        new { role = "system", content = "You are a specialized tour planner for Ben Tre province in Vietnam. " +
                                                        "Create custom tour itineraries based on user preferences, duration, and budget constraints. " +
                                                        "Focus on providing authentic experiences that showcase the local culture, cuisine, and natural beauty.\n" +
                                                        "IMPORTANT GUIDELINES:\n" +
                                                        "1. You must STRICTLY follow the user's preferences. Do not recommend activities or travel styles that contradict their stated preferences.\n" +
                                                        "2. If they mention 'tour có hướng dẫn' (guided tours), suggest guided tours. If they mention 'du lịch tự túc' (independent travel), suggest self-guided itineraries.\n" +
                                                        "3. Only suggest nature activities if they mentioned 'thiên nhiên' (Nature). Similarly for 'lịch sử' (History), 'ẩm thực' (Food), 'mạo hiểm' (Adventure), and 'thư giãn' (Relaxation).\n" +
                                                        "4. If they mention traveling with children or elderly people, ensure activities are suitable for them.\n" +
                                                        "5. Ensure budget recommendations EXACTLY match the specified budget range.\n" +
                                                        "6. Ensure duration recommendations EXACTLY match the specified duration." },
                        new { role = "user", content = $"Create a custom tour itinerary for Ben Tre with the following parameters: " +
                                                      $"Preferences: {preferences}, Duration: {duration}, Budget: {budget}. " +
                                                      "Include a mix of popular attractions and hidden gems. Provide specific recommendations for " +
                                                      "accommodations, activities, transportation, and dining that fit within the budget. " +
                                                      "Structure the response as a day-by-day itinerary with estimated costs for each component." }
                    },
                    temperature = 0.7
                };

                var content = new StringContent(JsonSerializer.Serialize(requestData), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"OpenAI API response received. Length: {responseContent.Length} characters");

                    try
                    {
                        var responseObject = JsonSerializer.Deserialize<JsonElement>(responseContent);
                        var responseText = responseObject.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();

                        // Kiểm tra nội dung phản hồi
                        if (string.IsNullOrWhiteSpace(responseText))
                        {
                            Console.WriteLine("WARNING: OpenAI returned empty content");
                            return "Rất tiếc, dịch vụ AI đã trả về nội dung trống. Vui lòng thử lại sau.";
                        }

                        // Ghi log thành công
                        Console.WriteLine($"Successfully generated custom tour suggestion. Length: {responseText.Length} characters");
                        return responseText;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing OpenAI response: {ex.Message}");
                        Console.WriteLine($"Response content: {responseContent}");
                        return "Rất tiếc, có lỗi khi xử lý phản hồi từ dịch vụ AI. Vui lòng thử lại sau.";
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    // Ghi log chi tiết hơn để dễ dàng gỡ lỗi
                    Console.WriteLine($"OpenAI API error (Status: {response.StatusCode}): {errorContent}");

                    // Ghi log thêm thông tin về request để giúp gỡ lỗi
                    Console.WriteLine($"Request details - Model: {_model}, Preferences: {preferences}, Duration: {duration}, Budget: {budget}");

                    // Thông báo lỗi cụ thể hơn
                    string errorMessage = "Không thể kết nối với dịch vụ AI.";

                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        errorMessage = "API key không hợp lệ hoặc đã hết hạn. Vui lòng kiểm tra lại cấu hình.";
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        errorMessage = "Đã vượt quá giới hạn yêu cầu đến dịch vụ AI. Vui lòng thử lại sau.";
                    }

                    return $"Rất tiếc, chúng tôi không thể tạo gợi ý tour tùy chỉnh vào lúc này. Lỗi: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                // Ghi log chi tiết hơn
                Console.WriteLine($"Error generating custom tour suggestion: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                return "Rất tiếc, chúng tôi không thể tạo gợi ý tour tùy chỉnh vào lúc này. Vui lòng thử lại sau. " +
                       "Lỗi: Đã xảy ra lỗi khi xử lý yêu cầu.";
            }
        }
    }
}
