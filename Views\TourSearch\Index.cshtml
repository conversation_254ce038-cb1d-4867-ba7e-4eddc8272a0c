@model IEnumerable<ViVu.Models.Tour>

@{
    ViewData["Title"] = "Tìm kiếm tour";
}

<div class="container py-5">
    <h1 class="mb-4">@ViewData["Title"]</h1>
    
    <div class="row">
        <div class="col-lg-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Bộ lọc tìm kiếm</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="@Url.Action("Index", "TourSearch")">
                        <div class="mb-3">
                            <label for="searchTerm" class="form-label">Từ khóa</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" value="@ViewBag.SearchTerm" placeholder="Nhập từ khóa...">
                        </div>
                        
                        <div class="mb-3">
                            <label for="locationId" class="form-label"><PERSON><PERSON><PERSON> điểm</label>
                            <select class="form-select" id="locationId" name="locationId">
                                <option value="">Tất cả địa điểm</option>
                                @foreach (var item in ViewBag.Locations)
                                {
                                    <option value="@item.Value" selected="@(ViewBag.SelectedLocationId != null && ViewBag.SelectedLocationId.ToString() == item.Value)">@item.Text</option>
                                }
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="cityId" class="form-label">Thành phố</label>
                            <select class="form-select" id="cityId" name="cityId">
                                <option value="">Tất cả thành phố</option>
                                @foreach (var item in ViewBag.Cities)
                                {
                                    <option value="@item.Value" selected="@(ViewBag.SelectedCityId != null && ViewBag.SelectedCityId.ToString() == item.Value)">@item.Text</option>
                                }
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tourDate" class="form-label">Ngày khởi hành</label>
                            <input type="date" class="form-control" id="tourDate" name="tourDate" value="@ViewBag.SelectedTourDate">
                        </div>
                        
                        <div class="mb-3">
                            <label for="duration" class="form-label">Thời gian tour</label>
                            <select class="form-select" id="duration" name="duration">
                                @foreach (var item in ViewBag.Durations)
                                {
                                    <option value="@item.Value" selected="@(ViewBag.SelectedDuration == item.Value)">@item.Text</option>
                                }
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="priceRange" class="form-label">Khoảng giá</label>
                            <select class="form-select" id="priceRange" name="priceRange">
                                @foreach (var item in ViewBag.PriceRanges)
                                {
                                    <option value="@item.Value" selected="@(ViewBag.SelectedMinPrice != null && ViewBag.SelectedMaxPrice != null && 
                                                                  item.Value == $"{ViewBag.SelectedMinPrice}-{ViewBag.SelectedMaxPrice}")">@item.Text</option>
                                }
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Tìm kiếm
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-9">
            <div class="mb-4">
                <h4>Kết quả tìm kiếm (@Model.Count() tour)</h4>
            </div>
            
            @if (!Model.Any())
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Không tìm thấy tour nào phù hợp với tiêu chí tìm kiếm của bạn.
                </div>
            }
            else
            {
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    @foreach (var tour in Model)
                    {
                        <div class="col">
                            <div class="card h-100 shadow-sm">
                                <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/no-image.jpg" : tour.ImageUrl)" 
                                     class="card-img-top" alt="@tour.Name" style="height: 180px; object-fit: cover;">
                                <div class="card-body">
                                    <h5 class="card-title">@tour.Name</h5>
                                    <p class="card-text text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>@(tour.Location != null ? tour.Location.Name : ""), @(tour.City != null ? tour.City.Name : "")
                                    </p>
                                    <p class="card-text">
                                        <i class="fas fa-clock me-1"></i>@tour.Duration ngày
                                    </p>
                                    <p class="card-text fw-bold text-primary">
                                        @tour.Price.ToString("N0") VNĐ
                                    </p>
                                </div>
                                <div class="card-footer bg-white border-top-0">
                                    <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-info-circle me-1"></i>Chi tiết
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>
