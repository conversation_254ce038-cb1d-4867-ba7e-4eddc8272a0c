﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ViVu.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRecommendationModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InterestedInNightlife",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInPhotography",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInShopping",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "InterestedInWellness",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PreferredPace",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PreferredSeason",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersBiking",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersFineDining",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersHomestays",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersHotels",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersLocalCuisine",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersPublicTransport",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersRentalVehicle",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersResorts",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersStreetFood",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersVillas",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "PrefersWalking",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "RequiresAccessibility",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForAdventure",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForCulture",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForEducation",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "TravelingForRelaxation",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "AccessibilityNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "AfternoonActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "BestTimeToVisit",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "CulinaryExperienceNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "CulturalExperienceNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "DailyItinerary",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "DiningRecommendations",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "EveningActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "FamilyFriendlinessNotes",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "LocalTips",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "MorningActivities",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "PersonalizationFactors",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "UserRating",
                table: "TravelRecommendations");

            migrationBuilder.RenameColumn(
                name: "TravelingWithPartner",
                table: "UserPreferences",
                newName: "TravelingAsCouple");

            migrationBuilder.RenameColumn(
                name: "TravelingWithFamily",
                table: "UserPreferences",
                newName: "InterestedInCycling");

            migrationBuilder.RenameColumn(
                name: "TravelingForRomance",
                table: "UserPreferences",
                newName: "InterestedInBoating");

            migrationBuilder.RenameColumn(
                name: "WeatherConsiderations",
                table: "TravelRecommendations",
                newName: "TipsSection");

            migrationBuilder.RenameColumn(
                name: "Version",
                table: "TravelRecommendations",
                newName: "ViewCount");

            migrationBuilder.RenameColumn(
                name: "UserFeedback",
                table: "TravelRecommendations",
                newName: "RecommendationSource");

            migrationBuilder.RenameColumn(
                name: "TransportationRecommendations",
                table: "TravelRecommendations",
                newName: "HighlightsSection");

            migrationBuilder.RenameColumn(
                name: "Tags",
                table: "TravelRecommendations",
                newName: "DayByDaySection");

            migrationBuilder.RenameColumn(
                name: "ShoppingRecommendations",
                table: "TravelRecommendations",
                newName: "BudgetBreakdownSection");

            migrationBuilder.RenameColumn(
                name: "FeedbackDate",
                table: "TravelRecommendations",
                newName: "LastViewedDate");

            migrationBuilder.RenameColumn(
                name: "ConfidenceScore",
                table: "TravelRecommendations",
                newName: "Priority");

            migrationBuilder.AddColumn<int>(
                name: "AdventureSearchCount",
                table: "UserPreferences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FoodSearchCount",
                table: "UserPreferences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "HistorySearchCount",
                table: "UserPreferences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NatureSearchCount",
                table: "UserPreferences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "RelaxationSearchCount",
                table: "UserPreferences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "ForCouples",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForElderlyTravelers",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForExperiencedTravelers",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForFamiliesWithChildren",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForFirstTimeVisitors",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForFriends",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForReturnVisitors",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForSoloTravelers",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasAccessibility",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesAdventure",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesBoating",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesCooking",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesCrafts",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesCycling",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesFarming",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesFishing",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesFood",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesHistory",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesNature",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IncludesRelaxation",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "RequiresPhysicalEffort",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SuitableForChildren",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SuitableForElders",
                table: "TravelRecommendations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ForCouples",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ForElderlyTravelers",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ForFamiliesWithChildren",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ForFriends",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ForSoloTravelers",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "InterestLevel",
                table: "SearchHistories",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToAdventure",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToBoating",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToCooking",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToCrafts",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToCycling",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToFarming",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToFishing",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToFood",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToHistory",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToNature",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RelatedToRelaxation",
                table: "SearchHistories",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ResultClicked",
                table: "SearchHistories",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "ViewDuration",
                table: "SearchHistories",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdventureSearchCount",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "FoodSearchCount",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "HistorySearchCount",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "NatureSearchCount",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "RelaxationSearchCount",
                table: "UserPreferences");

            migrationBuilder.DropColumn(
                name: "ForCouples",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForElderlyTravelers",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForExperiencedTravelers",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForFamiliesWithChildren",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForFirstTimeVisitors",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForFriends",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForReturnVisitors",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForSoloTravelers",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "HasAccessibility",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesAdventure",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesBoating",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesCooking",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesCrafts",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesCycling",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesFarming",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesFishing",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesFood",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesHistory",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesNature",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "IncludesRelaxation",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "RequiresPhysicalEffort",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "SuitableForChildren",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "SuitableForElders",
                table: "TravelRecommendations");

            migrationBuilder.DropColumn(
                name: "ForCouples",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ForElderlyTravelers",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ForFamiliesWithChildren",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ForFriends",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ForSoloTravelers",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "InterestLevel",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToAdventure",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToBoating",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToCooking",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToCrafts",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToCycling",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToFarming",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToFishing",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToFood",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToHistory",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToNature",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "RelatedToRelaxation",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ResultClicked",
                table: "SearchHistories");

            migrationBuilder.DropColumn(
                name: "ViewDuration",
                table: "SearchHistories");

            migrationBuilder.RenameColumn(
                name: "TravelingAsCouple",
                table: "UserPreferences",
                newName: "TravelingWithPartner");

            migrationBuilder.RenameColumn(
                name: "InterestedInCycling",
                table: "UserPreferences",
                newName: "TravelingWithFamily");

            migrationBuilder.RenameColumn(
                name: "InterestedInBoating",
                table: "UserPreferences",
                newName: "TravelingForRomance");

            migrationBuilder.RenameColumn(
                name: "ViewCount",
                table: "TravelRecommendations",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "TipsSection",
                table: "TravelRecommendations",
                newName: "WeatherConsiderations");

            migrationBuilder.RenameColumn(
                name: "RecommendationSource",
                table: "TravelRecommendations",
                newName: "UserFeedback");

            migrationBuilder.RenameColumn(
                name: "Priority",
                table: "TravelRecommendations",
                newName: "ConfidenceScore");

            migrationBuilder.RenameColumn(
                name: "LastViewedDate",
                table: "TravelRecommendations",
                newName: "FeedbackDate");

            migrationBuilder.RenameColumn(
                name: "HighlightsSection",
                table: "TravelRecommendations",
                newName: "TransportationRecommendations");

            migrationBuilder.RenameColumn(
                name: "DayByDaySection",
                table: "TravelRecommendations",
                newName: "Tags");

            migrationBuilder.RenameColumn(
                name: "BudgetBreakdownSection",
                table: "TravelRecommendations",
                newName: "ShoppingRecommendations");

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInNightlife",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInPhotography",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInShopping",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "InterestedInWellness",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "PreferredPace",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PreferredSeason",
                table: "UserPreferences",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersBiking",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersFineDining",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersHomestays",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersHotels",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersLocalCuisine",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersPublicTransport",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersRentalVehicle",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersResorts",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersStreetFood",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersVillas",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PrefersWalking",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "RequiresAccessibility",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForAdventure",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForCulture",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForEducation",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TravelingForRelaxation",
                table: "UserPreferences",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "AccessibilityNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AfternoonActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BestTimeToVisit",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CulinaryExperienceNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CulturalExperienceNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DailyItinerary",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DiningRecommendations",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EveningActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FamilyFriendlinessNotes",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LocalTips",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MorningActivities",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PersonalizationFactors",
                table: "TravelRecommendations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UserRating",
                table: "TravelRecommendations",
                type: "int",
                nullable: true);
        }
    }
}
